from django.db import models
from apps.employee.models import Employee

class Meet(models.Model):
    STATUS_CHOICES = (
        ('PENDING', 'Pending'),            # تماس در انتظار انجام
        ('IN_PROGRESS', 'In Progress'),    # تماس در حال گفتگو
        ('COMPLETED', 'Completed'),        # تماس انجام شده
        ('FAILED', 'Failed'),              # تماس ناموفق
    )

    participants = models.ManyToManyField(
        Employee,
        related_name='contacts',
        verbose_name='Participants'
    )
    status = models.CharField(
        max_length=32,
        choices=STATUS_CHOICES,
        default='PENDING',
        verbose_name='Status'
    )
    start_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='Start Time'
    )
    end_time = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name='End Time'
    )
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name='Description'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created At'
    )

    class Meta:
        verbose_name = 'Meeting'
        verbose_name_plural = 'Meetings'
        ordering = ['-created_at']

    def __str__(self):
        participant_names = ', '.join([p.full_name for p in self.participants.all()])
        return f"Participants: {participant_names} - {self.get_status_display()}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)




# مدل واسطه برای ثبت پاسخ‌ها با وضعیت‌های مختلف
class CallHistory(models.Model):
    RESPONSE_CHOICES = (
        ('ANSWERED', 'Answered'),          # پاسخ داده
        ('DECLINED', 'Declined'),          # رد تماس
        ('NO_RESPONSE', 'No Response'),    # پاسخ نداده
    )

    meet = models.ForeignKey(Meet, on_delete=models.CASCADE, related_name='responses')
    from_employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='calls_made'  # Unique related_name for calls initiated
    )
    to_employee = models.ForeignKey(
        Employee,
        on_delete=models.CASCADE,
        related_name='calls_received'  # Unique related_name for calls received
    )
    response_status = models.CharField(
        max_length=20,
        choices=RESPONSE_CHOICES,
        default='NO_RESPONSE'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='Created At'
    )


# class ParticipantResponse(models.Model):
#     RESPONSE_CHOICES = (
#         ('INITIATOR', 'Initiator'),        # شروع کننده تماس
#         ('ANSWERED', 'Answered'),          # پاسخ داده
#         ('DECLINED', 'Declined'),          # رد تماس
#         ('NO_RESPONSE', 'No Response'),    # پاسخ نداده
#     )
#
#     meet = models.ForeignKey(Meet, on_delete=models.CASCADE, related_name='responses')
#     participant = models.ForeignKey(Employee, on_delete=models.CASCADE)
#     response_status = models.CharField(
#         max_length=20,
#         choices=RESPONSE_CHOICES,
#         default='NO_RESPONSE'
#     )
#
#     class Meta:
#         unique_together = ('meet', 'participant')