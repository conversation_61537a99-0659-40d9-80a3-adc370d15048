from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin

from apps.meet.models import Meet  # فرض بر این است که مدل Meet در فایل models.py قرار دارد


@admin.register(Meet)
class MeetAdmin(AjaxDatatable):
    list_display = ('get_participants', 'status', 'start_time', 'end_time')
    search_fields = ('participants__full_name',)
    list_filter = ('status',)
    readonly_fields = ('created_at',)

    def get_queryset(self, request):
        # اضافه کردن یا تغییر در کوئری‌ست برای سفارشی‌سازی لیست نمایش داده شده
        queryset = super().get_queryset(request)
        return queryset.prefetch_related('participants')

    def get_participants(self, obj):
        return ", ".join([p.full_name for p in obj.participants.all()])
    get_participants.short_description = 'Participants'
