from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from apps.meet.models import Meet, CallHistory
from apps.employee.models import Employee, DeviceToken
import requests


def send_notification(device_token, data):
    url = "https://api.pushy.me/push?api_key=a01a7f89378f0de55d8e470e75e491c610c63ab73cdf563949a02a5e923a9cd9"
    payload = {
        "to": [device_token],
        "time_to_live": 5,
        "data": data
    }
    headers = {'Content-Type': 'application/json'}
    response = requests.post(url, json=payload, headers=headers)
    return response.json()

class CallView(APIView):
    def post(self, request):
        # دریافت توکن از هدر درخواست
        # دریافت توکن از هدر درخواست
        global to_employee_avatar
        request_token = self.request.headers.get('X-Api-Key')
        current_employee = Employee.objects.filter(token=request_token).first()

        # بررسی وجود کارمند
        if not current_employee:
            return Response({"error": "Invalid API Key."}, status=status.HTTP_401_UNAUTHORIZED)

        # دریافت شناسه کارمندان مقصد از داده‌های درخواست
        to_employee_ids = request.data.get('to_employee_ids', [])

        # دریافت لیست کارمندان مقصد
        to_employees = Employee.objects.filter(id__in=to_employee_ids)

        meet_id = request.data.get('meet_id', None)



        if not to_employees.exists():
            return Response({"error": "One or more employees not found."}, status=status.HTTP_404_NOT_FOUND)

        try:

            if meet_id:
                meet = Meet.objects.filter(id=meet_id).first()
                if not meet:
                    return Response({"error": "Meet not found."}, status=status.HTTP_404_NOT_FOUND)
            else:
                # اگر meet_id ارسال نشده بود، یک میت جدید ساخته شود
                meet = Meet.objects.create(status='PENDING')

            # افزودن شرکت‌کنندگان به جلسه
            meet.participants.set(to_employees)

            # ایجاد رکوردهای تاریخچه تماس برای کارمندان
            for employee in to_employees:
                CallHistory.objects.create(
                    meet=meet,
                    from_employee=current_employee,
                    to_employee=employee,
                    response_status='NO_RESPONSE'
                )
                to_employee_avatar = employee.avatar.url
                to_employee_name = employee.full_name



            # دریافت تمام device token‌های شرکت‌کنندگان
            device_tokens = DeviceToken.objects.filter(employee__in=to_employees)

            # جمع‌آوری اطلاعات افراد حاضر در جلسه
            room_participants = [
                {
                    "name": participant.full_name,
                    "profile_image": participant.avatar.url if participant.avatar else None
                }
                for participant in meet.participants.all()
            ]

            # جزئیات نوتیفیکیشن
            notification_data = {
                "notification_type": "incoming_call",
                "caller_id": current_employee.id,
                "caller_name": current_employee.full_name,
                "caller_profile_image": current_employee.avatar.url if current_employee.avatar else None,
                "meet_id": meet.id,  # شناسه جلسه
                "participants": room_participants,
                "message": f"{current_employee.full_name} is calling you!",
            }

            # ارسال نوتیفیکیشن به تمام دستگاه‌های شرکت‌کنندگان
            for device_token in device_tokens:
                send_notification(device_token.token, notification_data)

            return Response({
                "meet_id": meet.id,
                "status": "success",
                "to_employee_avatar": to_employee_avatar,
                "to_employee_name": to_employee_name
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class CallResponseView(APIView):
    def post(self, request):
        # دریافت توکن از هدر درخواست
        request_token = self.request.headers.get('X-Api-Key')
        current_employee = Employee.objects.filter(token=request_token).first()

        # بررسی وجود کارمند
        if not current_employee:
            return Response({"error": "Invalid API Key."}, status=status.HTTP_401_UNAUTHORIZED)

        # دریافت شناسه جلسه، نوع اقدام و شناسه شرکت‌کننده مقصد
        meet_id = request.data.get('meet_id')
        action = request.data.get('action')  # 'decline' برای رد تماس، 'end' برای پایان تماس
        to_employee_id = request.data.get('to_employee')  # شناسه کارمند مقصد

        # دریافت رکورد جلسه
        meet = Meet.objects.filter(id=meet_id).first()

        if not meet:
            return Response({"error": "Meet not found."}, status=status.HTTP_404_NOT_FOUND)

        # بررسی اینکه کارمند جزو شرکت‌کنندگان جلسه باشد
        # if not meet.participants.filter(id=current_employee.id).exists():
        #     return Response({"error": "You are not a participant of this meet."}, status=status.HTTP_403_FORBIDDEN)

        # دریافت شرکت‌کننده مقصد
        to_employee = Employee.objects.filter(id=to_employee_id).first()

        # if not to_employee or not meet.participants.filter(id=to_employee.id).exists():
        if not to_employee:
            return Response({"error": "Target employee not found."},
                            status=status.HTTP_404_NOT_FOUND)

        try:
            # بررسی اقدام
            if action == 'decline':
                # به‌روزرسانی وضعیت تاریخچه تماس برای کارمند به "رد شده"
                call_history = CallHistory.objects.filter(meet=meet, from_employee=current_employee).last()
                if call_history:
                    call_history.response_status = 'DECLINED'
                    call_history.save()

                # ارسال نوتیفیکیشن به شرکت‌کننده مقصد درباره رد تماس
                notification_message = f"{current_employee.full_name} declined the call."
                self.notify_participant(to_employee, notification_message, meet.id, current_employee.id)

                return Response({"message": "Call declined and notification sent to the target participant.",
                                 "status": "success"},
                                status=status.HTTP_200_OK)

            elif action == 'end':
                # به‌روزرسانی وضعیت جلسه به "انجام شده"
                meet.status = 'COMPLETED'
                meet.save()


                # ارسال نوتیفیکیشن به شرکت‌کننده مقصد درباره پایان تماس
                notification_message = f"{current_employee.full_name} ended the call."
                self.notify_participant(to_employee, notification_message, meet.id)

                return Response({"message": "Call ended and notification sent to the target participant.",
                                 "status": "success"},
                                status=status.HTTP_200_OK)

            else:
                return Response({"error": "Invalid action."}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def notify_participant(self, to_employee, message, meet_id, from_employee=None):
        # دریافت device token شرکت‌کننده مقصد
        device_token = DeviceToken.objects.filter(employee=to_employee).first()

        # جزئیات مشترک نوتیفیکیشن
        notification_data = {
            "notification_type": "call_status_update",
            "message": message,
            "meet_id": meet_id,
        }

        # افزودن from_employee در صورت وجود
        if from_employee:
            notification_data["from_employee"] = from_employee

        # ارسال نوتیفیکیشن به دستگاه شرکت‌کننده مقصد
        send_notification(device_token.token, notification_data)