# Generated by Django 3.2.17 on 2024-09-02 17:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('employee', '0013_auto_20240902_1746'),
    ]

    operations = [
        migrations.CreateModel(
            name='Meet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed')], default='PENDING', max_length=32, verbose_name='Status')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='Start Time')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='End Time')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('participants', models.ManyToManyField(related_name='contacts', to='employee.Employee', verbose_name='Participants')),
            ],
            options={
                'verbose_name': 'Meeting',
                'verbose_name_plural': 'Meetings',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ParticipantResponse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response_status', models.CharField(choices=[('INITIATOR', 'Initiator'), ('ANSWERED', 'Answered'), ('DECLINED', 'Declined'), ('NO_RESPONSE', 'No Response')], default='NO_RESPONSE', max_length=20)),
                ('meet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='meet.meet')),
                ('participant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='employee.employee')),
            ],
            options={
                'unique_together': {('meet', 'participant')},
            },
        ),
    ]
