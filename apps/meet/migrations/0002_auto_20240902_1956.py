# Generated by Django 3.2.17 on 2024-09-02 19:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0013_auto_20240902_1746'),
        ('meet', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CallHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('response_status', models.CharField(choices=[('ANSWERED', 'Answered'), ('DECLINED', 'Declined'), ('NO_RESPONSE', 'No Response')], default='NO_RESPONSE', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('from_employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calls_made', to='employee.employee')),
                ('meet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='meet.meet')),
                ('to_employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='calls_received', to='employee.employee')),
            ],
        ),
        migrations.DeleteModel(
            name='ParticipantResponse',
        ),
    ]
