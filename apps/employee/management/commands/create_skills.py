from django.core.management.base import BaseCommand
from apps.employee.models import Skill


class Command(BaseCommand):
    help = 'Create 20 skill records for the Skills model'

    def handle(self, *args, **options):
        skills_data = [
            {'name': 'Python', 'description': 'Python programming language'},
            {'name': 'Django', 'description': 'Django web framework'},
            {'name': 'JavaScript', 'description': 'JavaScript programming language'},
            {'name': 'React', 'description': 'React frontend framework'},
            {'name': 'Vue.js', 'description': 'Vue.js frontend framework'},
            {'name': 'Node.js', 'description': 'Node.js backend runtime'},
            {'name': 'HTML/CSS', 'description': 'Web markup and styling'},
            {'name': 'SQL', 'description': 'Database query language'},
            {'name': 'PostgreSQL', 'description': 'PostgreSQL database'},
            {'name': 'MySQL', 'description': 'MySQL database'},
            {'name': 'Git', 'description': 'Version control system'},
            {'name': 'Docker', 'description': 'Containerization platform'},
            {'name': 'Linux', 'description': 'Linux operating system'},
            {'name': 'AWS', 'description': 'Amazon Web Services'},
            {'name': 'REST API', 'description': 'RESTful API design and development'},
            {'name': 'GraphQL', 'description': 'GraphQL query language'},
            {'name': 'Redis', 'description': 'Redis in-memory database'},
            {'name': 'Celery', 'description': 'Asynchronous task processing'},
            {'name': 'UI/UX Design', 'description': 'User interface and experience design'},
            {'name': 'Project Management', 'description': 'Project management and coordination'},
        ]

        created_count = 0
        updated_count = 0

        for skill_data in skills_data:
            skill, created = Skill.objects.get_or_create(
                name=skill_data['name'],
                defaults={'description': skill_data['description']}
            )
            
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Created skill: {skill.name}')
                )
            else:
                # Update description if skill already exists
                if skill.description != skill_data['description']:
                    skill.description = skill_data['description']
                    skill.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.WARNING(f'↻ Updated skill: {skill.name}')
                    )
                else:
                    self.stdout.write(
                        self.style.HTTP_INFO(f'- Skill already exists: {skill.name}')
                    )

        self.stdout.write(
            self.style.SUCCESS(
                f'\n✅ Command completed successfully!'
                f'\n📊 Summary:'
                f'\n   • Created: {created_count} skills'
                f'\n   • Updated: {updated_count} skills'
                f'\n   • Total skills in database: {Skill.objects.count()}'
            )
        )
