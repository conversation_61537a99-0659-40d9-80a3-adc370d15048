from datetime import datetime

from rest_framework import serializers
from apps.employee.models import Employee
from apps.activity.models import TimeEntry
from django.utils import timezone

from django.utils import timezone


class EmployeeSerializer(serializers.ModelSerializer):
    is_online = serializers.SerializerMethodField()
    jira_task_name = serializers.SerializerMethodField()
    activity_description = serializers.SerializerMethodField()
    unread_messages_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Employee
        fields = ['id', 'full_name', 'email', 'avatar', 'meet_id', 'commitment_time_per_day', 'created_at',
                  'is_online', 'jira_task_name', 'activity_description', 'unread_messages_count', 'screan_active']

    def get_recent_time_entry(self, obj):
        # دریافت آخرین TimeEntry فعال برای employee با استفاده از تایم‌استمپ
        current_time = timezone.now()
        time_threshold = current_time.timestamp() - 900  # 15 دقیقه به ثانیه
        return TimeEntry.objects.filter(
            employee=obj,
            end_time__isnull=True,
            last_ready__gt=time_threshold
        ).first()

    def get_is_online(self, obj):
        # بررسی اینکه آیا employee دارای TimeEntry فعال است
        return self.get_recent_time_entry(obj) is not None

    def get_jira_task_name(self, obj):
        # دریافت نام وظیفه جیرا از آخرین TimeEntry فعال
        time_entry = self.get_recent_time_entry(obj)
        return time_entry.jira_task_name if time_entry else None

    def get_activity_description(self, obj):
        # دریافت توصیف فعالیت از آخرین TimeEntry فعال
        time_entry = self.get_recent_time_entry(obj) 
        return time_entry.report_text if time_entry else None


class TimeEntrySerializer(serializers.ModelSerializer):
    class Meta:
        model = TimeEntry
        fields = ['start_time', 'end_time', 'report_text', "id"]  # مشخصات مورد نیاز برای نمایش فعالیت ناتمام


class MeSerializer(serializers.ModelSerializer):
    incomplete_activity = serializers.SerializerMethodField()  # تغییر اینجا

    class Meta:
        model = Employee
        fields = ['full_name', 'email', 'avatar', 'meet_id', 'commitment_time_per_day', 'created_at',
                  'incomplete_activity', 'screan_active']

    def get_incomplete_activity(self, obj):
        # بررسی اینکه آیا employee دارای TimeEntry با end_time خالی است
        incomplete_activity = TimeEntry.objects.filter(employee=obj, end_time__isnull=True).first()
        return TimeEntrySerializer(incomplete_activity).data if incomplete_activity else None

from apps.employee.models import DeviceToken

class DeviceTokenSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeviceToken
        fields = ['token']


class EmployeeDetailSerializer(serializers.ModelSerializer):
    position = serializers.SerializerMethodField()
    skills = serializers.SerializerMethodField()
    projects = serializers.SerializerMethodField()
    contacts = serializers.SerializerMethodField()
    score = serializers.SerializerMethodField()
    isonline = serializers.SerializerMethodField()
    last_seen = serializers.SerializerMethodField()

    class Meta:
        model = Employee
        fields = [
            'id', 'full_name', 'email', 'phone_number', 'jira_username', 'bio',
            'skills', 'typical_working_weekday_start', 'typical_working_weekday_end',
            'typical_working_weekend', 'score', 'slogan', 'projects', 'position',
            'created_at', 'city', 'country', 'contacts', 'isonline', 'last_seen'
        ]

    def get_position(self, obj):
        return obj.position.name if obj.position else None

    def get_skills(self, obj):
        return [skill.name for skill in obj.skills.all()]

    def get_projects(self, obj):
        from apps.goals.models import ProjectTopicEmployee
        project_topics = ProjectTopicEmployee.objects.filter(employee=obj).select_related('project', 'topic')

        # Create list of project objects with project name and employee role
        projects_list = []
        for pt in project_topics:
            project_obj = {
                'name': pt.project.name,
                'role': pt.topic.name
            }
            projects_list.append(project_obj)

        return projects_list

    def get_contacts(self, obj):
        contacts = obj.employee_contacts.filter(is_public=True)

        # Create a dictionary with contact types as keys and values as values
        contacts_dict = {}
        for contact in contacts:
            contacts_dict[contact.contact_type] = contact.value

        # Add email from Employee model if not in contacts
        if 'email' not in contacts_dict and obj.email:
            contacts_dict['email'] = obj.email

        # Add phone from Employee model if not in contacts
        if 'phone' not in contacts_dict and obj.phone_number:
            contacts_dict['phone'] = obj.phone_number

        # Return None if no contacts exist at all
        if not contacts_dict:
            return None

        return contacts_dict

    def get_score(self, obj):
        # Convert score from 0-100 to 1-5 scale (with decimal support)
        if obj.score is None or obj.score == 0:
            return 1.0

        # Convert 0-100 to 1-5 scale with decimal precision
        # Formula: ((score / 100) * 4) + 1
        # This maps 0->1, 25->2, 50->3, 75->4, 100->5
        score_1_to_5 = ((obj.score / 100) * 4) + 1

        # Ensure it's within 1-5 range and round to 1 decimal place
        score_1_to_5 = max(1.0, min(5.0, round(score_1_to_5, 1)))
        return score_1_to_5

    def get_isonline(self, obj):
        from apps.activity.models import TimeEntry
        return TimeEntry.objects.filter(employee=obj, end_time__isnull=True).exists()

    def get_last_seen(self, obj):
        from apps.activity.models import TimeEntry
        last_entry = TimeEntry.objects.filter(employee=obj, end_time__isnull=False).order_by('-end_time').first()
        return last_entry.end_time if last_entry else None


class EmployeeListSerializer(serializers.ModelSerializer):
    position_name = serializers.SerializerMethodField()
    isonline = serializers.BooleanField(read_only=True)
    avatar = serializers.SerializerMethodField()

    class Meta:
        model = Employee
        fields = ['id', 'full_name', 'position_name', 'isonline', 'screan_active', 'avatar']

    def get_position_name(self, obj):
        return obj.position.name if obj.position else None

    def get_avatar(self, obj):
        if obj.avatar:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.avatar.url)
            return obj.avatar.url
        return None