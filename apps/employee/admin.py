from django.contrib import admin
from django.http import HttpResponse
import datetime
from django.template.loader import render_to_string
from django.urls import reverse
from django.db.models import Sum, F, ExpressionWrapper, DurationField
import jdatetime
from django.utils import timezone
from datetime import timedelta
from config.settings import base as settings
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from unfold.admin import ModelAdmin, TabularInline
from unfold.forms import AdminPasswordChangeForm, UserChangeForm, UserCreationForm
from unfold.contrib.forms.widgets import WysiwygWidget
from unfold.decorators import display
# حذف import های اضافی

from apps.activity.models import TimeEntry
from apps.employee.models import Employee, DeviceToken
from utils.admin import project_admin_site


# کلاس EmployeeDashboard حذف شد چون در نسخه فعلی Django Unfold پشتیبانی نمی‌شود

from apps.employee.models import Employee, <PERSON><PERSON><PERSON><PERSON>, EmployeeTelegramInfo, EmployeeContact

class DeviceTokenInline(TabularInline):
    model = DeviceToken
    extra = 0
    fields = ('token', 'created_at')
    readonly_fields = ('created_at',)
    can_delete = True
    verbose_name = "Device Token"
    verbose_name_plural = "Device Tokens"


class EmployeeContactInline(TabularInline):
    model = EmployeeContact
    extra = 0
    fields = ('contact_type', 'value', 'is_primary', 'is_public')
    can_delete = True
    verbose_name = "Employee Contact"
    verbose_name_plural = "Employee Contacts"
    tab = True


@admin.register(EmployeeContact, site=project_admin_site)
class EmployeeContactAdmin(ModelAdmin):
    list_display = ('employee', 'contact_type', 'value', 'is_primary', 'is_public', 'created_at')
    list_filter = ('contact_type', 'is_primary', 'is_public', 'created_at')
    search_fields = ('employee__full_name', 'employee__email', 'value')
    ordering = ('-created_at',)
    list_editable = ('is_primary', 'is_public')

    fieldsets = (
        ('Contact Information', {
            'fields': ('employee', 'contact_type', 'value')
        }),
        ('Settings', {
            'fields': ('is_primary', 'is_public'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )
    readonly_fields = ('created_at', 'updated_at')


@admin.register(DeviceToken)
class DeviceTokenAdmin(ModelAdmin):
    list_display = ('employee_name', 'token_display', 'created_at')
    search_fields = ('token', 'employee__full_name', 'employee__email')
    list_filter = ('created_at',)
    ordering = ('-created_at',)

    @display(description="Employee")
    def employee_name(self, obj):
        if obj.employee:
            return format_html(
                '<div class="flex items-center">'
                '<div class="flex-shrink-0 h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 text-sm font-medium mr-2">{}</div>'
                '<div class="text-sm font-medium text-gray-900">{}</div>'
                '</div>',
                obj.employee.full_name[0].upper() if obj.employee.full_name else '?',
                obj.employee.full_name
            )
        return "-"

    @display(description="Token")
    def token_display(self, obj):
        return format_html(
            '<div class="flex items-center">'
            '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">'
            '{}</span></div>',
            obj.token[:20] + "..." if len(obj.token) > 20 else obj.token
        )

@admin.register(EmployeeTelegramInfo)
class EmployeeTelegramInfoAdmin(ModelAdmin):
    list_display = ('employees_list', 'chat_id', 'is_active')
    search_fields = ('chat_id', 'employees__full_name')
    list_filter = ('is_active',)
    ordering = ('chat_id',)

    def employees_list(self, obj):
        return ", ".join([e.full_name for e in obj.employees.all()])

    employees_list.short_description = "Employees"



@admin.register(Employee)
class EmployeeAdmin(ModelAdmin):
    list_display = ('avatar_thumbnail', 'full_name', 'email', 'commitment_display', 'teams_display', 'actions_display')
    search_fields = ('full_name', 'email', 'phone_number')
    list_filter = ('teams', 'created_at', 'daily_tracking', 'weekly_tracking', 'monthly_tracking')
    ordering = ['-id']
    readonly_fields = ('token', 'created_at', 'employee_dashboard')
    inlines = [DeviceTokenInline, EmployeeContactInline]

    conditional_fields = {
        "daily_tracking_time": "daily_tracking == true",
        "weekly_tracking_day": "weekly_tracking == true",
        "weekly_tracking_time": "weekly_tracking == true",
        "monthly_tracking_day": "monthly_tracking == true",
        "monthly_tracking_time": "monthly_tracking == true",
    }

    # اضافه کردن داشبورد خلاصه برای کارمند
    @display(description="Dashboard")
    def employee_dashboard(self, obj):
        # محاسبه آمار کارمند
        today = timezone.now().date()
        today_jalali = jdatetime.date.fromgregorian(date=today)

        # محاسبه شروع و پایان هفته (جلالی)
        start_of_week_jalali = today_jalali - timedelta(days=today_jalali.weekday())
        end_of_week_jalali = start_of_week_jalali + timedelta(days=6)
        start_of_week = start_of_week_jalali.togregorian()
        end_of_week = end_of_week_jalali.togregorian()

        # محاسبه شروع و پایان ماه (جلالی)
        start_of_month_jalali = today_jalali.replace(day=1)
        end_of_month_jalali = start_of_month_jalali + timedelta(days=31)
        end_of_month_jalali = end_of_month_jalali.replace(day=1) - timedelta(days=1)
        start_of_month = start_of_month_jalali.togregorian()
        end_of_month = end_of_month_jalali.togregorian()

        # ساعات فعال امروز
        todays_entries = TimeEntry.objects.filter(employee=obj, start_time__date=today)
        today_active_seconds = sum([(entry.end_time or timezone.now()) - entry.start_time for entry in todays_entries], timedelta()).total_seconds()
        today_active_hours = today_active_seconds / 3600

        # ساعات فعال هفته
        weeks_entries = TimeEntry.objects.filter(employee=obj, start_time__date__range=[start_of_week, end_of_week])
        week_active_seconds = sum([(entry.end_time or timezone.now()) - entry.start_time for entry in weeks_entries], timedelta()).total_seconds()
        week_active_hours = week_active_seconds / 3600

        # ساعات فعال ماه
        months_entries = TimeEntry.objects.filter(employee=obj, start_time__date__range=[start_of_month, end_of_month])
        month_active_seconds = sum([(entry.end_time or timezone.now()) - entry.start_time for entry in months_entries], timedelta()).total_seconds()
        month_active_hours = month_active_seconds / 3600

        # محاسبه درصد تعهد
        daily_commitment = float(obj.commitment_time_per_day or 0)
        monthly_commitment = float(obj.commitment_time_per_month or 0)

        daily_percentage = (today_active_hours / daily_commitment * 100) if daily_commitment > 0 else 0
        monthly_percentage = (month_active_hours / monthly_commitment * 100) if monthly_commitment > 0 else 0

        # تابع کمکی برای نمایش ساعت‌ها به صورت یکنواخت
        def format_hours_display(hours_float):
            hours = int(hours_float)
            minutes = int((hours_float - hours) * 60)
            return f"{hours}:{minutes:02d}"

        # نمایش داشبورد
        return format_html(
            '<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">'
            
            # کارت امروز
            '<div class="bg-white overflow-hidden shadow rounded-lg">'
            '<div class="px-4 py-5 sm:p-6">'
            '<div class="flex items-center">'
            '<div class="flex-shrink-0 bg-primary-100 rounded-md p-3">'
            '<svg class="h-6 w-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>'
            '</svg>'
            '</div>'
            '<div class="ml-5 w-0 flex-1">'
            '<dl>'
            '<dt class="text-sm font-medium text-gray-500 truncate">Today\'s Activity</dt>'
            '<dd>'
            '<div class="text-lg font-medium text-gray-900">{} hours</div>'
            '<div class="mt-1 flex items-baseline justify-between md:block lg:flex">'
            '<div class="flex items-baseline text-sm font-semibold text-{}-500">'
            '<span>{:.1f}%</span>'
            '<span class="ml-2 text-gray-400">of daily commitment</span>'
            '</div>'
            '</div>'
            '</dd>'
            '</dl>'
            '</div>'
            '</div>'
            '</div>'
            '</div>'
            
            # کارت هفته
            '<div class="bg-white overflow-hidden shadow rounded-lg">'
            '<div class="px-4 py-5 sm:p-6">'
            '<div class="flex items-center">'
            '<div class="flex-shrink-0 bg-indigo-100 rounded-md p-3">'
            '<svg class="h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>'
            '</svg>'
            '</div>'
            '<div class="ml-5 w-0 flex-1">'
            '<dl>'
            '<dt class="text-sm font-medium text-gray-500 truncate">This Week\'s Activity</dt>'
            '<dd>'
            '<div class="text-lg font-medium text-gray-900">{} hours</div>'
            '<div class="mt-1 flex items-baseline justify-between md:block lg:flex">'
            '<div class="flex items-baseline text-sm font-semibold text-gray-500">'
            '<span>{} - {}</span>'
            '</div>'
            '</div>'
            '</dd>'
            '</dl>'
            '</div>'
            '</div>'
            '</div>'
            '</div>'
            
            # کارت ماه
            '<div class="bg-white overflow-hidden shadow rounded-lg">'
            '<div class="px-4 py-5 sm:p-6">'
            '<div class="flex items-center">'
            '<div class="flex-shrink-0 bg-green-100 rounded-md p-3">'
            '<svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>'
            '</svg>'
            '</div>'
            '<div class="ml-5 w-0 flex-1">'
            '<dl>'
            '<dt class="text-sm font-medium text-gray-500 truncate">This Month\'s Activity</dt>'
            '<dd>'
            '<div class="text-lg font-medium text-gray-900">{} hours</div>'
            '<div class="mt-1 flex items-baseline justify-between md:block lg:flex">'
            '<div class="flex items-baseline text-sm font-semibold text-{}-500">'
            '<span>{:.1f}%</span>'
            '<span class="ml-2 text-gray-400">of monthly commitment</span>'
            '</div>'
            '</div>'
            '</dd>'
            '</dl>'
            '</div>'
            '</div>'
            '</div>'
            '</div>'
            
            '</div>',
            format_hours_display(today_active_hours),
            'green' if daily_percentage >= 100 else 'yellow' if daily_percentage >= 70 else 'red',
            daily_percentage,
            format_hours_display(week_active_hours),
            start_of_week_jalali.strftime('%Y/%m/%d'),
            end_of_week_jalali.strftime('%Y/%m/%d'),
            format_hours_display(month_active_hours),
            'green' if monthly_percentage >= 100 else 'yellow' if monthly_percentage >= 70 else 'red',
            monthly_percentage
        )

    @display(description="Avatar", ordering="full_name")
    def avatar_thumbnail(self, obj):
        if obj.avatar:
            return format_html(
                '<img src="{}" width="40" height="40" style="border-radius: 50%; object-fit: cover;" />',
                obj.avatar.url
            )
        return format_html(
            '<div style="width: 40px; height: 40px; border-radius: 50%; background-color: #ae64d8; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">{}</div>',
            obj.full_name[0].upper() if obj.full_name else '?'
        )

    @display(description="Commitment", ordering="commitment_time_per_day")
    def commitment_display(self, obj):
        daily = obj.commitment_time_per_day or 0
        monthly = obj.commitment_time_per_month or 0

        # تبدیل اعداد اعشاری به نمایش ساعت و دقیقه
        def format_hours_display(hours_float):
            if hours_float == 0:
                return "0:00"
            hours = int(hours_float)
            minutes = int((float(hours_float) - hours) * 60)
            return f"{hours}:{minutes:02d}"
        
        daily_display = format_hours_display(daily)
        monthly_display = format_hours_display(monthly)
        

        return format_html(
            '<div class="flex flex-col">'
            '<span class="text-sm font-medium">Daily: <span class="text-primary-500">{} hours</span></span>'
            '<span class="text-sm font-medium">Monthly: <span class="text-primary-500">{} hours</span></span>'
            '</div>',
            daily_display, monthly_display
        )

    @display(description="Teams")
    def teams_display(self, obj):
        teams = obj.teams.all()
        if not teams:
            return "-"

        team_badges = ""
        for team in teams:
            team_badges += format_html(
                '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 mr-1 mb-1">{}</span>',
                team.name
            )
        return mark_safe(team_badges)

    @display(description="Actions")
    def actions_display(self, obj):
        return format_html(
            '<div class="flex space-x-2">'
            '<a href="{}" target="_blank" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">'
            '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>'
            '</svg>Report</a>'
            '<a href="{}" target="_blank" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">'
            '<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>'
            '</svg>Details</a>'
            '</div>',
            reverse('admin:employee_export_html', args=[obj.id]),
            reverse('admin:employee_detailed_report', args=[obj.id])
        )
    

    def get_urls(self):
        from django.urls import path
        urls = super().get_urls()
        custom_urls = [
            path('export/<int:employee_id>/', self.admin_site.admin_view(self.export_employee_html), name='employee_export_html'),
            path('detailed-report/<int:employee_id>/', self.admin_site.admin_view(self.detailed_employee_report), name='employee_detailed_report'),
        ]
        return custom_urls + urls
    
    def convert_to_hours_and_minutes(self, total_seconds):
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) / 60)
        return f"{hours} hours {minutes} minutes"
        
    def export_employee_html(self, request, employee_id):
        employee = Employee.objects.get(pk=employee_id)
        
        today = timezone.now().date()
        today_jalali = jdatetime.date.fromgregorian(date=today)

        # Calculate start and end of week (Jalali)
        start_of_week_jalali = today_jalali - timedelta(days=today_jalali.weekday())
        end_of_week_jalali = start_of_week_jalali + timedelta(days=6)
        start_of_week = start_of_week_jalali.togregorian()
        end_of_week = end_of_week_jalali.togregorian()

        # Calculate start and end of month (Jalali)
        start_of_month_jalali = today_jalali.replace(day=1)
        end_of_month_jalali = start_of_month_jalali + timedelta(days=31)
        end_of_month_jalali = end_of_month_jalali.replace(day=1) - timedelta(days=1)
        start_of_month = start_of_month_jalali.togregorian()
        end_of_month = end_of_month_jalali.togregorian()

        # Today's active hours
        todays_entries = TimeEntry.objects.filter(employee=employee, start_time__date=today)
        today_active_seconds = sum([(entry.end_time or timezone.now()) - entry.start_time for entry in todays_entries], timedelta()).total_seconds()
        today_active_hours = today_active_seconds / 3600

        # Week's active hours
        weeks_entries = TimeEntry.objects.filter(employee=employee, start_time__date__range=[start_of_week, end_of_week])
        week_active_seconds = sum([(entry.end_time or timezone.now()) - entry.start_time for entry in weeks_entries], timedelta()).total_seconds()
        week_active_hours = week_active_seconds / 3600

        # Month's active hours
        months_entries = TimeEntry.objects.filter(employee=employee, start_time__date__range=[start_of_month, end_of_month])
        month_active_seconds = sum([(entry.end_time or timezone.now()) - entry.start_time for entry in months_entries], timedelta()).total_seconds()
        month_active_hours = month_active_seconds / 3600

        # Helper function to format hours consistently
        def format_hours_display(hours_float):
            hours = int(hours_float)
            minutes = int((hours_float - hours) * 60)
            return f"{hours}:{minutes:02d}"

        # Format all the times for display - ensure consistency by using the same format function
        today_hours_display = format_hours_display(today_active_hours)
        week_hours_display = format_hours_display(week_active_hours)
        month_hours_display = format_hours_display(month_active_hours)

        # Add debug variables
        debug_info = {
            'today_active_hours': today_active_hours,
            'today_hours_display': today_hours_display,
            'week_active_hours': week_active_hours,
            'week_hours_display': week_hours_display,
            'month_active_hours': month_active_hours,
            'month_hours_display': month_hours_display,
        }

        # Handle details view when clicking on a specific period
        period = request.GET.get('period')
        entries = []
        period_title = ""

        # Daily data for week view
        daily_hours = []
        weekly_hours = []

        # Create data for weekly view (showing each day of the week)
        if period == 'week' or period == 'week_day':
            specific_day = request.GET.get('day')

            # Get Jalali weekday names in correct order (starting with Saturday/شنبه)
            weekday_names = [
                'شنبه', 'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنج‌شنبه', 'جمعه'
            ]

            # For each day in the week
            for day_offset in range(7):
                current_date_jalali = start_of_week_jalali + timedelta(days=day_offset)
                current_date = current_date_jalali.togregorian()

                # Get entries for this day
                day_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__date=current_date
                )

                # Calculate total hours for this day
                day_active_seconds = sum(
                    [(entry.end_time or timezone.now()) - entry.start_time for entry in day_entries],
                    timedelta()
                ).total_seconds()
                day_active_hours = day_active_seconds / 3600

                # Use the consistent format function
                hours_display = format_hours_display(day_active_hours)

                # Add to daily_hours list
                daily_hours.append({
                    'date': current_date_jalali.strftime('%Y/%m/%d'),
                    'weekday': weekday_names[day_offset],
                    'hours': day_active_hours,
                    'hours_display': hours_display,
                    'entries': [{
                        'start': self.get_jalali_info(entry.start_time),
                        'end': self.get_jalali_info(entry.end_time) if entry.end_time else "در حال حاضر",
                        'report_text': entry.report_text
                    } for entry in day_entries]
                })

            # If specific day is selected, show entries for that day
            if specific_day and specific_day.isdigit():
                day_index = int(specific_day)
                if 0 <= day_index < 7:
                    entries = daily_hours[day_index]['entries']
                    period_title = f"فعالیت‌های روز {daily_hours[day_index]['weekday']}"
                    # Add day total hours for display in the template
                    day_hours_display = daily_hours[day_index]['hours_display']

                    # Add debug info
                    print(f"DEBUG - day_index: {day_index}, hours: {daily_hours[day_index]['hours']}, display: {day_hours_display}, entries: {len(entries)}")

                    # Add to debug_info for template
                    debug_info['day_hours'] = daily_hours[day_index]['hours']
                    debug_info['day_hours_display'] = day_hours_display
                    debug_info['entries_count'] = len(entries)

        # Create data for monthly view (showing each week of the month)
        if period == 'month' or period == 'month_week':
            specific_week = request.GET.get('week')

            # Calculate number of weeks in the month - but limit to 4 weeks max as per Iranian calendar
            days_in_month = (end_of_month_jalali - start_of_month_jalali).days + 1
            num_weeks = min(4, (days_in_month + 6) // 7)  # Limit to 4 weeks maximum

            # For each week in the month
            for week_offset in range(num_weeks):
                week_start_jalali = start_of_month_jalali + timedelta(days=week_offset * 7)
                week_end_jalali = min(week_start_jalali + timedelta(days=6), end_of_month_jalali)
                week_start = week_start_jalali.togregorian()
                week_end = week_end_jalali.togregorian()

                # Get entries for this week
                week_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__date__range=[week_start, week_end]
                )

                # Calculate total hours for this week
                week_active_seconds = sum(
                    [(entry.end_time or timezone.now()) - entry.start_time for entry in week_entries],
                    timedelta()
                ).total_seconds()
                week_active_hours = week_active_seconds / 3600

                # Format hours using the consistent format_hours_display function
                hours_display = format_hours_display(week_active_hours)

                # Add to weekly_hours list
                weekly_hours.append({
                    'start_date': week_start_jalali.strftime('%Y/%m/%d'),
                    'end_date': week_end_jalali.strftime('%Y/%m/%d'),
                    'week_number': week_offset + 1,
                    'hours': week_active_hours,
                    'hours_display': hours_display
                })

            # If specific week is selected, generate the daily data for that week
            if specific_week and specific_week.isdigit():
                week_index = int(specific_week)
                if 0 <= week_index < len(weekly_hours):
                    week_start_jalali = start_of_month_jalali + timedelta(days=week_index * 7)
                    week_end_jalali = min(week_start_jalali + timedelta(days=6), end_of_month_jalali)
                    week_start = week_start_jalali.togregorian()
                    week_end = week_end_jalali.togregorian()

                    # Get entries for this specific week for total calculation
                    specific_week_entries = TimeEntry.objects.filter(
                        employee=employee,
                        start_time__date__range=[week_start, week_end]
                    )

                    # Calculate correct total for this specific week
                    specific_week_active_seconds = sum(
                        [(entry.end_time or timezone.now()) - entry.start_time for entry in specific_week_entries],
                        timedelta()
                    ).total_seconds()
                    specific_week_active_hours = specific_week_active_seconds / 3600

                    # Ensure consistency by using the same format function
                    total_hours_display = format_hours_display(specific_week_active_hours)

                    # Update week_active_hours with the correct total for this specific week
                    week_active_hours = specific_week_active_hours

                    # Update period to 'week' and set daily_hours for this specific week
                    weekday_names = [
                        'شنبه', 'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنج‌شنبه', 'جمعه'
                    ]

                    daily_hours = []
                    # We sort days of week properly starting from Saturday (شنبه)
                    for day_offset in range((week_end_jalali - week_start_jalali).days + 1):
                        current_date_jalali = week_start_jalali + timedelta(days=day_offset)
                        current_date = current_date_jalali.togregorian()

                        # Get weekday index (0-6, where 0 is Saturday/شنبه)
                        weekday_index = (current_date_jalali.weekday() + 1) % 7  # Adjust for Iranian week (شنبه is 0)

                        # Get entries for this day
                        day_entries = TimeEntry.objects.filter(
                            employee=employee,
                            start_time__date=current_date
                        )

                        # Calculate total hours for this day
                        day_active_seconds = sum(
                            [(entry.end_time or timezone.now()) - entry.start_time for entry in day_entries],
                            timedelta()
                        ).total_seconds()
                        day_active_hours = day_active_seconds / 3600

                        # Use the consistent format function
                        hours_display = format_hours_display(day_active_hours)

                        # Add to daily_hours list
                        daily_hours.append({
                            'date': current_date_jalali.strftime('%Y/%m/%d'),
                            'weekday': weekday_names[weekday_index],
                            'hours': day_active_hours,
                            'hours_display': hours_display,
                            'entries': [{
                                'start': self.get_jalali_info(entry.start_time),
                                'end': self.get_jalali_info(entry.end_time) if entry.end_time else "در حال حاضر",
                                'report_text': entry.report_text
                            } for entry in day_entries]
                        })

                    # Sort by day of the week to ensure proper order
                    daily_hours.sort(key=lambda x: weekday_names.index(x['weekday']))

                    period = 'week'
                    period_title = f"فعالیت‌های هفته {week_index + 1}"

        if period == 'today':
            entries = [{'start': self.get_jalali_info(entry.start_time), 'end': self.get_jalali_info(entry.end_time) if entry.end_time else "در حال حاضر", 'report_text': entry.report_text} for entry in todays_entries]
            period_title = "فعالیت‌های امروز"
        elif period == 'week' and not request.GET.get('day') and not request.GET.get('week'):
            entries = [{'start': self.get_jalali_info(entry.start_time), 'end': self.get_jalali_info(entry.end_time) if entry.end_time else "در حال حاضر", 'report_text': entry.report_text} for entry in weeks_entries]
            period_title = "فعالیت‌های این هفته"
        elif period == 'month' and not request.GET.get('week'):
            entries = [{'start': self.get_jalali_info(entry.start_time), 'end': self.get_jalali_info(entry.end_time) if entry.end_time else "در حال حاضر", 'report_text': entry.report_text} for entry in months_entries]
            period_title = "فعالیت‌های این ماه"
        else:
            # No specific filtering, show only summary without details
            if not period:
                period_title = "گزارش کلی"

        html_content = render_to_string('employees_work_data_with_reports.html', {
            'employee': employee,
            'today_active_hours': today_active_hours,
            'week_active_hours': week_active_hours,
            'month_active_hours': month_active_hours,
            'today_hours_display': today_hours_display,
            'week_hours_display': week_hours_display,
            'month_hours_display': month_hours_display,
            'debug_info': debug_info,
            'today_jalali': jdatetime.date.fromgregorian(date=today).strftime('%Y/%m/%d'),
            'start_of_week_jalali': start_of_week_jalali.strftime('%Y/%m/%d'),
            'end_of_week_jalali': end_of_week_jalali.strftime('%Y/%m/%d'),
            'start_of_month_jalali': start_of_month_jalali.strftime('%Y/%m/%d'),
            'end_of_month_jalali': end_of_month_jalali.strftime('%Y/%m/%d'),
            'entries': entries,
            'period_title': period_title,
            'period': period,
            'daily_hours': daily_hours,
            'weekly_hours': weekly_hours,
            'total_hours_display': total_hours_display if 'total_hours_display' in locals() else None,
            'day_hours_display': day_hours_display if 'day_hours_display' in locals() else None,
        })
        return HttpResponse(html_content)

    # Convert date and time to Jalali and add day of the week without the year
    def get_jalali_info(self, entry_time):
        jalali_date = jdatetime.datetime.fromgregorian(datetime=entry_time)
        day_of_week = settings.DAYS_OF_WEEK_TRANSLATION[jalali_date.strftime('%A')]
        month_name = settings.MONTHS_TRANSLATION[jalali_date.strftime('%B')]
        formatted_time = jalali_date.strftime('%d - %H:%M')
        return f"{day_of_week}، {formatted_time} ({month_name})"

    def detailed_employee_report(self, request, employee_id):
        employee = Employee.objects.get(pk=employee_id)

        # Get current month and year from request or use current date
        today = timezone.now().date()
        today_jalali = jdatetime.date.fromgregorian(date=today)

        # Get month and year from request parameters or use current month/year
        requested_month = request.GET.get('month')
        requested_year = request.GET.get('year')
        # Get selected day from request parameters
        selected_day = request.GET.get('day')

        if requested_month and requested_year and requested_month.isdigit() and requested_year.isdigit():
            current_month = int(requested_month)
            current_year = int(requested_year)
        else:
            current_month = today_jalali.month
            current_year = today_jalali.year

        # Create Jalali date for the first day of the month
        first_day_of_month = jdatetime.date(current_year, current_month, 1)

        # Calculate last day of month
        if current_month == 12:  # Esfand
            # Check if it's a leap year in Jalali calendar
            last_day = 29 if first_day_of_month.isleap() else 30
        else:
            last_day = 31 if current_month <= 6 else 30

        last_day_of_month = jdatetime.date(current_year, current_month, last_day)

        # Convert to Gregorian for database queries
        start_of_month = first_day_of_month.togregorian()
        end_of_month = last_day_of_month.togregorian()

        # Get all entries for the month
        month_entries = TimeEntry.objects.filter(
            employee=employee,
            start_time__date__range=[start_of_month, end_of_month]
        ).order_by('start_time')

        # Calculate total hours for the month
        month_active_seconds = sum(
            [(entry.end_time or timezone.now()) - entry.start_time for entry in month_entries],
            timedelta()
        ).total_seconds()
        month_active_hours = month_active_seconds / 3600

        # Format hours consistently
        def format_hours_display(hours_float):
            hours = int(hours_float)
            minutes = int((hours_float - hours) * 60)
            return f"{hours}:{minutes:02d}"

        month_hours_display = format_hours_display(month_active_hours)

        # Calculate completion percentage if commitment_time_per_month exists
        completion_percentage = 0
        # if employee.commitment_time_per_month and employee.commitment_time_per_month > 0:
            # completion_percentage = min(100, round((month_active_hours / employee.commitment_time_per_month) * 100))
        if employee.commitment_time_per_month and float(employee.commitment_time_per_month) > 0:
            completion_percentage = min(100, round((month_active_hours / float(employee.commitment_time_per_month)) * 100))

        # Get month name in Persian
        month_names = [
            'فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور',
            'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند'
        ]
        current_month_name = month_names[current_month - 1]

        # Prepare navigation for previous and next months
        prev_month = current_month - 1
        prev_year = current_year
        if prev_month < 1:
            prev_month = 12
            prev_year -= 1

        next_month = current_month + 1
        next_year = current_year
        if next_month > 12:
            next_month = 1
            next_year += 1

        # Don't allow navigation to future months
        if jdatetime.date(next_year, next_month, 1) > today_jalali:
            next_month = None
            next_year = None

        # Group entries by day
        daily_entries = []

        # Get Jalali weekday names
        weekday_names = [
            'شنبه', 'یکشنبه', 'دوشنبه', 'سه‌شنبه', 'چهارشنبه', 'پنج‌شنبه', 'جمعه'
        ]

        # Create a dictionary to store entries by day
        days_in_month = (last_day_of_month - first_day_of_month).days + 1

        for day_offset in range(days_in_month):
            current_date_jalali = first_day_of_month + timedelta(days=day_offset)
            current_date = current_date_jalali.togregorian()

            # Get weekday index (0-6, where 0 is Saturday/شنبه in Iranian calendar)
            # در تقویم جلالی، شنبه روز اول هفته است (0) و جمعه روز آخر هفته (6)
            weekday_index = current_date_jalali.weekday()

            # Get entries for this day
            day_entries = TimeEntry.objects.filter(
                employee=employee,
                start_time__date=current_date
            ).order_by('start_time')

            if day_entries:
                # Calculate total hours for this day
                day_active_seconds = sum(
                    [(entry.end_time or timezone.now()) - entry.start_time for entry in day_entries],
                    timedelta()
                ).total_seconds()
                day_active_hours = day_active_seconds / 3600

                # Format hours
                hours_display = format_hours_display(day_active_hours)

                # Format entries with report text
                formatted_entries = []
                for entry in day_entries:
                    duration_seconds = ((entry.end_time or timezone.now()) - entry.start_time).total_seconds()
                    duration_hours = duration_seconds / 3600

                    formatted_entries.append({
                        'start': self.get_jalali_info(entry.start_time),
                        'end': self.get_jalali_info(entry.end_time) if entry.end_time else "در حال حاضر",
                        'report_text': entry.report_text,
                        'duration': format_hours_display(duration_hours)
                    })

                # Add to daily_entries list
                daily_entries.append({
                    'date': current_date_jalali.strftime('%Y/%m/%d'),
                    'weekday': weekday_names[weekday_index],
                    'hours': day_active_hours,
                    'hours_display': hours_display,
                    'entries': formatted_entries
                })

        # Sort daily entries by date (descending - newest first)
        daily_entries.sort(key=lambda x: jdatetime.datetime.strptime(x['date'], '%Y/%m/%d'), reverse=True)

        # Create calendar data structure
        calendar_weeks = []

        # Get the first day of the month's weekday (0 = Saturday in Jalali calendar)
        # در تقویم جلالی، شنبه روز اول هفته است (0) و جمعه روز آخر هفته (6)
        first_day_weekday = first_day_of_month.weekday()

        # Create a copy of daily entries sorted by date (ascending)
        calendar_daily_entries = sorted(
            daily_entries,
            key=lambda x: jdatetime.datetime.strptime(x['date'], '%Y/%m/%d')
        )

        # Find the maximum hours in a day for scaling the activity indicators
        max_hours = 0
        if calendar_daily_entries:
            max_hours = max(day['hours'] for day in calendar_daily_entries)

        # If max_hours is 0, set it to 1 to avoid division by zero
        if max_hours == 0:
            max_hours = 1

        # Create a dictionary for quick lookup of day data by date
        day_data_by_date = {}
        for i, day in enumerate(calendar_daily_entries):
            day_date = jdatetime.datetime.strptime(day['date'], '%Y/%m/%d')
            day_data_by_date[day_date.day] = {
                'index': i,
                'has_entries': bool(day['entries']),
                'hours': day['hours'],
                'hours_display': day['hours_display'],
                'activity_percentage': min(100, int((day['hours'] / max_hours) * 100))
            }

        # Generate calendar weeks
        current_week = []

        # Add empty days before the first day of the month
        for i in range(first_day_weekday):
            current_week.append({'is_empty': True})

        # Add days of the month
        for day in range(1, last_day + 1):
            day_date = jdatetime.date(current_year, current_month, day)
            is_today = day_date == today_jalali
            is_selected = selected_day and int(selected_day) == day

            day_info = {
                'day': day,
                'date': day_date.strftime('%Y/%m/%d'),
                'is_empty': False,
                'is_today': is_today,
                'is_selected': is_selected,
                'day_url': f"?month={current_month}&year={current_year}&day={day}",
            }

            # Add activity data if available
            if day in day_data_by_date:
                day_info.update({
                    'index': day_data_by_date[day]['index'],
                    'has_entries': day_data_by_date[day]['has_entries'],
                    'hours_display': day_data_by_date[day]['hours_display'],
                    'activity_percentage': day_data_by_date[day]['activity_percentage']
                })

            current_week.append(day_info)

            # Start a new week if we've reached the end of the week
            if len(current_week) == 7:
                calendar_weeks.append(current_week)
                current_week = []

        # Add empty days after the last day of the month
        if current_week:
            while len(current_week) < 7:
                current_week.append({'is_empty': True})
            calendar_weeks.append(current_week)

        # Handle selected day if provided
        selected_day_entries = []
        selected_day_title = ""
        selected_day_hours_display = ""
        show_day_details = False

        if selected_day and selected_day.isdigit():
            day = int(selected_day)
            if 1 <= day <= last_day:
                # Create Jalali date for the selected day
                selected_date_jalali = jdatetime.date(current_year, current_month, day)
                selected_date = selected_date_jalali.togregorian()

                # Get weekday name
                # در تقویم جلالی، شنبه روز اول هفته است (0) و جمعه روز آخر هفته (6)
                weekday_index = selected_date_jalali.weekday()
                weekday_name = weekday_names[weekday_index]

                # Get entries for this day
                day_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__date=selected_date
                ).order_by('start_time')

                # Calculate total hours for this day
                if day_entries:
                    day_active_seconds = sum(
                        [(entry.end_time or timezone.now()) - entry.start_time for entry in day_entries],
                        timedelta()
                    ).total_seconds()
                    day_active_hours = day_active_seconds / 3600
                    selected_day_hours_display = format_hours_display(day_active_hours)

                    # Format entries
                    for entry in day_entries:
                        duration_seconds = ((entry.end_time or timezone.now()) - entry.start_time).total_seconds()
                        duration_hours = duration_seconds / 3600

                        selected_day_entries.append({
                            'start': self.get_jalali_info(entry.start_time),
                            'end': self.get_jalali_info(entry.end_time) if entry.end_time else "در حال حاضر",
                            'report_text': entry.report_text,
                            'duration': format_hours_display(duration_hours)
                        })
                else:
                    # No entries for this day
                    selected_day_hours_display = "0:00"

                selected_day_title = f"فعالیت‌های روز {day} {current_month_name} ({weekday_name})"
                show_day_details = True

        html_content = render_to_string('employee_detailed_report.html', {
            'employee': employee,
            'today_jalali': today_jalali.strftime('%Y/%m/%d'),
            'current_month': current_month,
            'current_year': current_year,
            'current_month_name': current_month_name,
            'month_hours_display': month_hours_display,
            'daily_entries': daily_entries,
            'completion_percentage': completion_percentage,
            'commitment_time_per_month': employee.commitment_time_per_month,
            'prev_month': prev_month,
            'prev_year': prev_year,
            'next_month': next_month,
            'next_year': next_year,
            'calendar_weeks': calendar_weeks,
            'selected_day': selected_day,
            'selected_day_entries': selected_day_entries,
            'selected_day_title': selected_day_title,
            'selected_day_hours_display': selected_day_hours_display,
            'show_day_details': show_day_details,
        })

        return HttpResponse(html_content)


    # متد display_device_tokens حذف شد چون از inline استفاده می‌کنیم

    # تنظیم فیلدها با استفاده از امکانات Django Unfold
    fieldsets = (
        ('Personal Information', {
            'fields': (
                ('full_name', 'email'),
                'password',
                'avatar',
                ('phone_number', 'home_phone_number'),
                'home_address',
                ('jira_username', 'meet_id'),
                'token',
                'created_at',
            ),
            'classes': ('grid-col-2',),
            'description': 'Basic information about the employee including contact details and identifiers.'
        }),
        ('Financial Information', {
            'fields': (
                ('bank_card_number', 'bank_sheba_number'),
                ('monthly_salary', 'hourly_salary'),
                'salary_pattern',
                'contract_file',
            ),
            'classes': ('grid-col-2',),
            'description': 'Banking and salary information for the employee.'
        }),
        ('Work Commitments', {
            'fields': (
                ('commitment_time_per_day', 'commitment_time_per_week', 'commitment_time_per_month'),
                ('daily_tracking', 'weekly_tracking', 'monthly_tracking'),  # سه فیلد tracking کنار هم
                'daily_tracking_time',
                ('weekly_tracking_day', 'weekly_tracking_time'),  # روز و زمان اطلاع‌رسانی هفتگی کنار هم
                ('monthly_tracking_day', 'monthly_tracking_time'),  # روز و زمان اطلاع‌رسانی ماهانه کنار هم
                'report_validation_rules',
                'screan_active',
            ),
            'classes': ('grid-col-1',),
            'description': 'Work time commitments and tracking preferences.'
        }),
        ('Company & Position', {
            'fields': (
                'company',
                'position',
                'is_admin',
            ),
            'classes': ('grid-col-2',),
            'description': 'Company affiliation, position and admin privileges.'
        }),
        ('Teams & Permissions', {
            'fields': (
                'teams',
                'permission_employee',
            ),
            'description': 'Team memberships and access permissions.'
        }),
        ('Documents & Media', {
            'fields': (
                ('national_id_card_image', 'birth_certificate_image'),
                'interview_video',
            ),
            'classes': ('grid-col-2',),
            'description': 'Official documents and media files related to the employee.'
        }),
    )


# ثبت مدل‌ها در project_admin_site
project_admin_site.register(Employee, EmployeeAdmin)
project_admin_site.register(DeviceToken, DeviceTokenAdmin)
project_admin_site.register(EmployeeTelegramInfo, EmployeeTelegramInfoAdmin)