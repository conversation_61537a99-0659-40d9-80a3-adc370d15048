from rest_framework.response import Response
from rest_framework import status
from rest_framework.generics import GenericAPI<PERSON>iew
from rest_framework.views import APIView
from apps.employee.models import Employee
from apps.permissions import EmployeeTokenPermission
from ..docs import employee_signin_swagger, employee_profile_swagger
from django.utils import timezone
from apps.account.centrifugo import centrifugo_service




class EmployeeSignInView(GenericAPIView):
    """
    Employee Sign In View
    Accepts email and password in request body
    Returns token if credentials are valid, otherwise returns error
    """

    @employee_signin_swagger
    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')

        if not email or not password:
            return Response({
                'success': False,
                'message': 'Email and password are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            employee = Employee.objects.get(email=email, password=password)
            return Response({
                'success': True,
                'token': employee.token,
                'message': 'Sign in successful'
            }, status=status.HTTP_200_OK)

        except Employee.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Invalid email or password'
            }, status=status.HTTP_401_UNAUTHORIZED)


class EmployeeProfileView(GenericAPIView):
    """
    Employee Profile View
    Returns employee profile information including name, email, position and company
    Requires X-Api-Key header with valid employee token
    """
    permission_classes = [EmployeeTokenPermission]

    @employee_profile_swagger
    def get(self, request):
        employee = request.employee
        
        version = request.headers.get('version', 2)


        # Check for incomplete activity based on version and empty end_time or null is_activity_submitted
        from apps.activity.models import TimeEntry
        incomplete_activity = TimeEntry.objects.filter(
            employee=employee,
            version=version,
            end_time__isnull=True
        ).order_by('-id').first()

        if not incomplete_activity:
            incomplete_activity = TimeEntry.objects.filter(
                employee=employee,
                version=version,
                is_activity_submitted__isnull=True 
            ).order_by('-id').first()

        # Prepare incomplete activity object with id and duration
        incomplete_activity_obj = None
        if incomplete_activity:
            # Calculate duration based on last_ready timestamp (same logic as TimeEndView/TimeUpdateView)
            if incomplete_activity.last_ready is not None:
                # Calculate effective end time based on last_ready + 3.5 hours + 20 seconds
                from datetime import datetime
                effective_end_timestamp = incomplete_activity.last_ready + (3.5 * 60 * 60) + 20
                effective_end_time = datetime.utcfromtimestamp(effective_end_timestamp)
                duration_seconds = (effective_end_time - incomplete_activity.start_time).total_seconds()
            else:
                # Fallback to current time if last_ready is None (activity just started)
                current_time = timezone.now()
                duration_seconds = (current_time - incomplete_activity.start_time).total_seconds()

            hours = int(duration_seconds // 3600)
            minutes = int((duration_seconds % 3600) // 60)
            seconds = int(duration_seconds % 60)
            duration_str = f"{hours}:{minutes:02d}:{seconds:02d}"

            incomplete_activity_obj = {
                'activity_id': incomplete_activity.id,
                'duration': duration_str
            }

        # Prepare response data
        profile_data = {
            'success': True,
            'data': {
                'full_name': employee.full_name,
                'email': employee.email,
                'avatar': request.build_absolute_uri(employee.avatar.url) if employee.avatar else None,
                'position': employee.position.name if employee.position else None,
                'company': employee.company.name if employee.company else None,
                "is_admin": employee.is_admin,
                "screan_active": employee.screan_active,
                'incomplete_activity': incomplete_activity_obj,

            },
            'message': 'Profile retrieved successfully'
        }
        print(f'--employee_profile--response--> {profile_data}')
        return Response(profile_data, status=status.HTTP_200_OK)


class LiveKitScreenShareNotificationView(APIView):
    """Send LiveKit screen share notification to specific employee"""
    permission_classes = [EmployeeTokenPermission]

    # TODO: Before sending the notification, check if the target employee for screen sharing is online.
    # If not online, return an appropriate error response.
    def post(self, request):
        """Send screen share notification to target employee"""
        try:
            employee_target_id = request.data.get('employee_target_id')
            share_type = request.data.get('share_type')

            # Validate employee_target_id
            if not employee_target_id:
                return Response(
                    {'error': 'employee_target_id is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Validate share_type
            if not share_type:
                return Response(
                    {'error': 'share_type is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            if share_type not in ['publisher', 'subscriber']:
                return Response(
                    {'error': 'share_type must be either "publisher" or "subscriber"'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                employee_target_id = int(employee_target_id)
            except (ValueError, TypeError):
                return Response(
                    {'error': 'employee_target_id must be a valid integer'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Send Centrifugo notification employee_target_id
            result = centrifugo_service.notify_livekit_screen_share(70, request.employee.id, share_type)

            return Response({
                'success': True,
                'message': f'LiveKit screen share notification sent to employee {employee_target_id}',
                'employee_target_id': employee_target_id,
                'requester_employee_id': request.employee.id,
                'share_type': share_type
            })


        except Exception as e:
            return Response(
                {'error': f'Failed to send notification: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

