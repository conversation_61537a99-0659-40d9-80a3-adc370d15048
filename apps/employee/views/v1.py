import requests
import json
from django.http import JsonResponse
from django.views import View
from rest_framework.response import Response
from rest_framework import viewsets
from rest_framework import status
from rest_framework.views import APIView
from django.views.decorators.csrf import csrf_exempt
from rest_framework.permissions import IsAuthenticated
from apps.permissions import HasTokenPermission
from django.db.models import OuterR<PERSON>, Sub<PERSON><PERSON>, Count, Q, Exists
from django.db import models
from django.views.decorators.http import require_http_methods
from django.db.models import Exists, OuterRef
from rest_framework.generics import ListAPIView
from apps.employee.models import Employee
from apps.activity.models import TimeEntry
from apps.employee.serializers import EmployeeSerializer, MeSerializer, EmployeeListSerializer, EmployeeDetailSerializer
from apps.employee.docs import employee_list_swagger, employee_detail_swagger
from apps.account.centrifugo import centrifugo_service
from apps.employee.models import Employee, DeviceToken
from apps.employee.serializers import DeviceTokenSerializer
from apps.message.models import Message
from utils.pagination import NoPagination

class EmployeeListView(ListAPIView):
    # permission_classes = [HasTokenPermission]
    pagination_class = NoPagination
    def get_serializer_class(self):
        # Check for version header
        version_header = self.request.headers.get('version')
        if version_header:
            return EmployeeListSerializer
        return EmployeeSerializer

    def get_queryset(self):
        request_token = self.request.headers.get('X-Api-Key')
        current_employee = Employee.objects.filter(token=request_token).first()

        # Base queryset - common for both version and non-version requests
        teams = current_employee.teams.all()
        employees = Employee.objects.exclude(token=request_token).filter(
            Q(teams__in=teams) | Q(permission_employee=current_employee)
        ).distinct()

        # Common annotation for online status
        time_entries_with_no_end_time = TimeEntry.objects.filter(employee=OuterRef('pk'), end_time__isnull=True)

        # Check for version header
        version_header = self.request.headers.get('version')
        if version_header:
            # Simple queryset for version header - just basic info + isonline
            return employees.select_related('position').annotate(
                isonline=Exists(time_entries_with_no_end_time)
            )

        # Complex queryset for non-version requests - includes unread messages
        unread_messages_count = Message.objects.filter(
            sender=OuterRef('pk'),
            receiver=current_employee,
            is_read=False
        ).values('sender').annotate(count=Count('id')).values('count')

        return employees.annotate(
            isonline=Exists(time_entries_with_no_end_time),
            unread_messages_count=Subquery(unread_messages_count, output_field=models.IntegerField())
        ).order_by('unread_messages_count', '-isonline',)

    @employee_list_swagger
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)



class MeView(APIView):
    serializer_class = MeSerializer
    permission_classes = [HasTokenPermission]
    def get(self, request, *args, **kwargs):
        request_token = self.request.headers.get('X-Api-Key')
        employee = Employee.objects.get(token=request_token)
        serializer = MeSerializer(employee)
        return Response(serializer.data)


class EmployeeDetailView(APIView):
    permission_classes = [HasTokenPermission]

    @employee_detail_swagger
    def get(self, request, employee_id):
        try:
            employee = Employee.objects.get(id=employee_id)
            serializer = EmployeeDetailSerializer(employee, context={'request': request})
            return Response(serializer.data)
        except Employee.DoesNotExist:
            return Response(
                {'error': 'Employee not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class JiraTaksView(View):
    def get(self, request, *args, **kwargs):
        request_token = self.request.headers.get('X-Api-Key')
        employee = Employee.objects.get(token=request_token)
        jira_username = employee.jira_username  # فرض می‌کنیم که نام کاربری JIRA همان نام کاربری در Django است

        jira_url = (
            f"https://jira.nwhco.ir/rest/api/2/search?"
            f"jql=assignee={jira_username} AND (status = \"To Do\" OR status = \"In Progress\") ORDER BY Rank ASC"
            "&maxResults=-1&fields=summary"
        )

        headers = {
            'Authorization': 'Basic YWRtaW46RmdIQD8jNDI0MDIxNjk5',  # توکن احراز هویت JIRA را از تنظیمات دریافت کنید
            'Content-Type': 'application/json'
        }

        response = requests.get(jira_url, headers=headers)

        if response.status_code == 200:
            return JsonResponse(response.json())
        else:
            return JsonResponse({'error': 'Unable to fetch data from JIRA'}, status=response.status_code)


class UpdateDeviceTokenView(APIView):
    permission_classes = [HasTokenPermission]

    def post(self, request, *args, **kwargs):
        request_token = request.headers.get('X-Api-Key')
        new_token = request.data.get('device_token')

        if not new_token:
            return Response({"error": "Device token is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            employee = Employee.objects.get(token=request_token)
        except Employee.DoesNotExist:
            return Response({"error": "Invalid token."}, status=status.HTTP_404_NOT_FOUND)

        # Update or create the device token
        device_token, created = DeviceToken.objects.update_or_create(
            employee=employee,
            defaults={'token': new_token}
        )

        serializer = DeviceTokenSerializer(device_token)
        return Response(serializer.data, status=status.HTTP_200_OK if not created else status.HTTP_201_CREATED)
    

class ScreenActiveAPIView(APIView):
    
    def post(self, request, *args, **kwargs):
        request_token = request.headers.get('X-Api-Key')
        employee = Employee.objects.get(token=request_token)
        screan_active = request.data.get('screan_active')

        if screan_active is not None:
            old_status = employee.screan_active
            new_status = bool(screan_active)

            # Update employee screen sharing status
            employee.screan_active = new_status
            employee.save()

            # Send Centrifugo notification if status changed
            if old_status != new_status:
                try:
                    # centrifugo_service.notify_employee_screen_update(employee.id, new_status)
                    centrifugo_service.notify_employee_screen_update(31, new_status)
                    print(f'--screen-update-notification-sent-> Employee {employee.full_name} screen_active: {new_status}')
                except Exception as e:
                    print(f'--screen-update-notification-error-> {str(e)}')

        return Response({'screan_active': employee.screan_active})
        
        


class EmployeeScreanAPIView(APIView):
    
    def get(self, request, pk, *args, **kwargs):
        try:
            employee = Employee.objects.get(id=pk)
            
            is_online = TimeEntry.objects.filter(
                employee=employee,  
                end_time__isnull=True  
            ).exists()
            is_online = True if is_online else False

            return Response({
                "active_screan": employee.screan_active,  
                "status": is_online
            })
        except Employee.DoesNotExist:
                return Response({"active_screan": False, "status": False})

    
    