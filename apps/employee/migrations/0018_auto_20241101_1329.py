# Generated by Django 3.2.17 on 2024-11-01 13:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0017_auto_20241101_1315'),
    ]

    operations = [
        migrations.AddField(
            model_name='employee',
            name='bank_card_number',
            field=models.CharField(blank=True, max_length=16, null=True, verbose_name='شماره کارت بانکی'),
        ),
        migrations.AddField(
            model_name='employee',
            name='bank_sheba_number',
            field=models.CharField(blank=True, max_length=24, null=True, verbose_name='شماره شبا بانکی'),
        ),
        migrations.AddField(
            model_name='employee',
            name='contract_file',
            field=models.FileField(blank=True, null=True, upload_to='contracts/', verbose_name='فایل قرارداد'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='daily_tracking',
            field=models.BooleanField(default=True, help_text='کمبود تعهدی کاربر به صورت روزانه به او اطلاع داده شود'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='monthly_tracking',
            field=models.BooleanField(default=True, help_text='کمبود تعهدی کاربر به صورت ماهانه به او اطلاع داده شود'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='weekly_tracking',
            field=models.BooleanField(default=False, help_text='کمبود تعهدی کاربر به صورت هفتگی به او اطلاع داده شود'),
        ),
    ]
