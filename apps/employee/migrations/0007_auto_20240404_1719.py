# Generated by Django 3.2.17 on 2024-04-04 17:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('teams', '0002_remove_teams_employee'),
        ('employee', '0006_employee_permission_employee'),
    ]

    operations = [
        migrations.AddField(
            model_name='employee',
            name='permission_teams',
            field=models.ManyToManyField(blank=True, to='teams.Teams', verbose_name='Permission Teams'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='permission_employee',
            field=models.ManyToManyField(blank=True, related_name='_employee_employee_permission_employee_+', to='employee.Employee', verbose_name='Permission Employee'),
        ),
    ]
