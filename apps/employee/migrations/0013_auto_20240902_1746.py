# Generated by Django 3.2.17 on 2024-09-02 17:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0012_auto_20240528_1138'),
    ]

    operations = [
        migrations.AddField(
            model_name='employee',
            name='device_token',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.CreateModel(
            name='DeviceToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.Char<PERSON>ield(max_length=255, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='device_tokens', to='employee.employee')),
            ],
        ),
    ]
