# Generated by Django 5.2.1 on 2025-07-14 10:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0030_employee_position'),
    ]

    operations = [
        migrations.CreateModel(
            name='Skill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Skill Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Skill',
                'verbose_name_plural': 'Skills',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='bio',
            field=models.TextField(blank=True, null=True, verbose_name='بیوگرافی'),
        ),
        migrations.AddField(
            model_name='employee',
            name='score',
            field=models.PositiveIntegerField(default=0, verbose_name='امتیاز'),
        ),
        migrations.AddField(
            model_name='employee',
            name='slogan',
            field=models.CharField(blank=True, max_length=300, null=True, verbose_name='شعار کارمند'),
        ),
        migrations.AddField(
            model_name='employee',
            name='typical_working_weekday_end',
            field=models.TimeField(blank=True, null=True, verbose_name='ساعت پایان کار روزهای هفته'),
        ),
        migrations.AddField(
            model_name='employee',
            name='typical_working_weekday_start',
            field=models.TimeField(blank=True, null=True, verbose_name='ساعت شروع کار روزهای هفته'),
        ),
        migrations.AddField(
            model_name='employee',
            name='typical_working_weekend',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='برنامه کاری آخر هفته'),
        ),
        migrations.AddField(
            model_name='employee',
            name='skills',
            field=models.ManyToManyField(blank=True, to='employee.skill', verbose_name='مهارت\u200cها'),
        ),
    ]
