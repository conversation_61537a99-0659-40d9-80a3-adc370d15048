# Generated by Django 5.2.1 on 2025-07-10 18:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0028_employee_password_employeetelegraminfo'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeTelegramInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('chat_id', models.<PERSON>r<PERSON><PERSON>(max_length=100, verbose_name='Telegram Chat ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('employees', models.ManyToManyField(related_name='telegram_infos', to='employee.employee')),
            ],
        ),
    ]
