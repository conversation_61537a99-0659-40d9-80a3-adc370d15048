# Generated by Django 5.2.1 on 2025-05-13 14:23

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0023_employee_activity_report_sms'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='employee',
            name='activity_report_sms',
        ),
        migrations.AddField(
            model_name='employee',
            name='commitment_time_per_week',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='employee',
            name='daily_tracking_time',
            field=models.TimeField(blank=True, help_text='ساعت اطلاع\u200cرسانی روزانه (مثلا 18:00)', null=True, verbose_name='زمان اطلاع\u200cرسانی روزانه'),
        ),
        migrations.AddField(
            model_name='employee',
            name='monthly_tracking_day',
            field=models.PositiveSmallIntegerField(blank=True, help_text='روز ماه برای اطلاع\u200cرسانی ماهانه (1 تا 31)', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(31)], verbose_name='روز اطلاع\u200cرسانی ماهانه'),
        ),
        migrations.AddField(
            model_name='employee',
            name='weekly_tracking_day',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(0, 'شنبه'), (1, 'یکشنبه'), (2, 'دوشنبه'), (3, 'سه\u200cشنبه'), (4, 'چهارشنبه'), (5, 'پنجشنبه'), (6, 'جمعه')], help_text='روز هفته برای اطلاع\u200cرسانی هفتگی', null=True, verbose_name='روز اطلاع\u200cرسانی هفتگی'),
        ),
    ]
