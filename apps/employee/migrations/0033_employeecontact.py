# Generated by Django 5.2.1 on 2025-07-15 00:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0032_employee_city_employee_country'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeContact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contact_type', models.CharField(choices=[('email', 'Email'), ('phone', 'Phone'), ('whatsapp', 'WhatsApp'), ('telegram', 'Telegram'), ('github', 'GitHub'), ('linkedin', 'LinkedIn'), ('instagram', 'Instagram'), ('twitter', 'Twitter'), ('facebook', 'Facebook'), ('skype', 'Skype'), ('discord', 'Discord'), ('slack', 'Slack'), ('youtube', 'YouTube'), ('website', 'Website'), ('other', 'Other')], max_length=20, verbose_name='نوع تماس')),
                ('value', models.CharField(max_length=255, verbose_name='مقدار')),
                ('is_primary', models.BooleanField(default=False, verbose_name='اصلی')),
                ('is_public', models.BooleanField(default=True, verbose_name='عمومی')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_contacts', to='employee.employee', verbose_name='کارمند')),
            ],
            options={
                'verbose_name': 'اطلاعات تماس کارمند',
                'verbose_name_plural': 'اطلاعات تماس کارمندان',
                'ordering': ['-is_primary', 'contact_type'],
                'unique_together': {('employee', 'contact_type', 'value')},
            },
        ),
    ]
