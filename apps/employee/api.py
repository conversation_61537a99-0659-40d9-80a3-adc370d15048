from django.http import JsonResponse
from django.utils import timezone
from datetime import timedelta
import jdatetime
from django.conf import settings

from apps.activity.models import TimeEntry
from apps.employee.models import Employee

def day_details_api(request):
    """
    API endpoint to get details for a specific day's activities.
    
    Query parameters:
    - date: The date in Jalali format (YYYY/MM/DD)
    - day_index: The index of the day (optional)
    - employee_id: The ID of the employee (required)
    """
    # Get query parameters
    date_str = request.GET.get('date')
    day_index = request.GET.get('day_index')
    employee_id = request.GET.get('employee_id')
    
    # Validate parameters
    if not date_str or not employee_id:
        return JsonResponse({
            'error': 'Missing required parameters: date and employee_id'
        }, status=400)
    
    try:
        # Parse the Jalali date
        year, month, day = map(int, date_str.split('/'))
        jalali_date = jdatetime.date(year, month, day)
        
        # Convert to Gregorian for database query
        gregorian_date = jalali_date.togregorian()
        
        # Get the employee
        employee = Employee.objects.get(pk=employee_id)
        
        # Get entries for this day
        day_entries = TimeEntry.objects.filter(
            employee=employee,
            start_time__date=gregorian_date
        ).order_by('start_time')
        
        # Format hours consistently
        def format_hours_display(hours_float):
            hours = int(hours_float)
            minutes = int((hours_float - hours) * 60)
            return f"{hours}:{minutes:02d}"
        
        # Calculate total hours for this day
        if day_entries:
            day_active_seconds = sum(
                [(entry.end_time or timezone.now()) - entry.start_time for entry in day_entries],
                timedelta()
            ).total_seconds()
            day_active_hours = day_active_seconds / 3600
            total_hours = format_hours_display(day_active_hours)
        else:
            total_hours = "0:00"
        
        # Format entries for response
        formatted_entries = []
        for entry in day_entries:
            duration_seconds = ((entry.end_time or timezone.now()) - entry.start_time).total_seconds()
            duration_hours = duration_seconds / 3600
            
            # Get formatted times
            start_time = get_jalali_info(entry.start_time)
            end_time = get_jalali_info(entry.end_time) if entry.end_time else "در حال حاضر"
            
            formatted_entries.append({
                'title': 'گزارش فعالیت',
                'start_time': start_time,
                'end_time': end_time,
                'duration': format_hours_display(duration_hours),
                'description': entry.report_text
            })
        
        # Prepare response data
        response_data = {
            'date': date_str,
            'total_hours': total_hours,
            'entries': formatted_entries
        }
        
        return JsonResponse(response_data)
        
    except (ValueError, jdatetime.JalaliDateError):
        return JsonResponse({
            'error': 'Invalid date format. Expected YYYY/MM/DD'
        }, status=400)
    except Employee.DoesNotExist:
        return JsonResponse({
            'error': 'Employee not found'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'error': str(e)
        }, status=500)

def get_jalali_info(entry_time):
    """Convert date and time to Jalali format with day of week."""
    jalali_date = jdatetime.datetime.fromgregorian(datetime=entry_time)
    
    # Get day of week and month name translations
    try:
        day_of_week = settings.DAYS_OF_WEEK_TRANSLATION[jalali_date.strftime('%A')]
        month_name = settings.MONTHS_TRANSLATION[jalali_date.strftime('%B')]
    except (AttributeError, KeyError):
        # Fallback if translations are not available
        day_of_week = jalali_date.strftime('%A')
        month_name = jalali_date.strftime('%B')
    
    formatted_time = jalali_date.strftime('%d - %H:%M')
    return f"{day_of_week}، {formatted_time} ({month_name})"