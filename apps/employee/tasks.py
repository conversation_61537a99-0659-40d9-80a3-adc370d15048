import requests
import jdatetime
import pytz
from datetime import datetime, time
from django.conf import settings
from django.utils import timezone
from config.celery import app
from apps.employee.models import Employee


def _call_sms_report_api(report_type):
    """
    فراخوانی API ارسال گزارش SMS
    
    Args:
        report_type (str): نوع گزارش (Daily_Motivation, weekly_Motivation, monthly_Motivation)
    """
    # آدرس API
    api_url = f"https://timee.nwhco.ir/api/activity/sendSmsReport"
    
    # پارامترهای درخواست
    params = {
        'token': 'uy4844qwiweqe48dgrt5fdfdfdf5',
        'type': report_type
    }
    
    try:
        # فراخوانی API
        response = requests.get(api_url, params=params)
        
        # بررسی پاسخ
        if response.status_code == 200:
            print(f"گزارش {report_type} با موفقیت ارسال شد: {response.json()}")
        else:
            print(f"خطا در ارسال گزارش {report_type}: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"خطا در فراخوانی API ارسال گزارش {report_type}: {str(e)}")


@app.task
def send_daily_tracking_reports():
    """
    ارسال گزارش‌های روزانه به کارمندانی که daily_tracking آنها فعال است
    و ساعت فعلی با ساعت daily_tracking_time آنها مطابقت دارد
    """
    # زمان فعلی
    now = timezone.now()
    current_hour = now.hour
    
    # پیدا کردن کارمندانی که daily_tracking آنها فعال است و ساعت آنها با ساعت فعلی مطابقت دارد
    employees = Employee.objects.filter(
        daily_tracking=True,
        daily_tracking_time__hour=current_hour,
        daily_tracking_time__minute=0  # فقط کارمندانی که دقیقه آنها 0 است (در ابتدای ساعت)
    )
    
    if employees.exists():
        print(f"ارسال گزارش روزانه به {employees.count()} کارمند در ساعت {current_hour}:00")
        _call_sms_report_api("Daily_Motivation")
    else:
        print(f"هیچ کارمندی برای ارسال گزارش روزانه در ساعت {current_hour}:00 یافت نشد")


@app.task
def send_weekly_tracking_reports():
    """
    ارسال گزارش‌های هفتگی به کارمندانی که weekly_tracking آنها فعال است
    و روز و ساعت فعلی با weekly_tracking_day و weekly_tracking_time آنها مطابقت دارد
    """
    # تبدیل تاریخ میلادی به جلالی برای تعیین روز هفته
    today_jalali = jdatetime.date.today()
    current_weekday = today_jalali.weekday()  # 0=شنبه، 1=یکشنبه، ...
    
    # زمان فعلی
    now = timezone.now()
    current_hour = now.hour
    
    # پیدا کردن کارمندانی که weekly_tracking آنها فعال است و روز هفته مطابقت دارد
    employees_with_day = Employee.objects.filter(
        weekly_tracking=True,
        weekly_tracking_day=current_weekday
    )
    
    # کارمندانی که زمان مشخص کرده‌اند و باید در ساعت فعلی گزارش دریافت کنند
    employees_with_time = employees_with_day.filter(
        weekly_tracking_time__hour=current_hour,
        weekly_tracking_time__minute=0  # فقط دقیقه 0 هر ساعت
    )
    
    # کارمندانی که زمان مشخص نکرده‌اند و باید در ساعت 12 ظهر گزارش دریافت کنند
    if current_hour == 12:
        employees_without_time = employees_with_day.filter(
            weekly_tracking_time__isnull=True
        )
        # ترکیب هر دو گروه کارمندان
        employees = list(employees_with_time) + list(employees_without_time)
    else:
        employees = employees_with_time
    
    if employees:
        print(f"ارسال گزارش هفتگی به {len(employees)} کارمند در روز {current_weekday} (شنبه=0) ساعت {current_hour}:00")
        _call_sms_report_api("weekly_Motivation")
    else:
        print(f"هیچ کارمندی برای ارسال گزارش هفتگی در روز {current_weekday} (شنبه=0) ساعت {current_hour}:00 یافت نشد")


@app.task
def send_monthly_tracking_reports():
    """
    ارسال گزارش‌های ماهانه به کارمندانی که monthly_tracking آنها فعال است
    و روز و ساعت فعلی با monthly_tracking_day و monthly_tracking_time آنها مطابقت دارد
    """
    # تاریخ و زمان فعلی
    now = timezone.now()
    current_day = now.day
    current_hour = now.hour
    
    # پیدا کردن کارمندانی که monthly_tracking آنها فعال است و روز ماه مطابقت دارد
    employees_with_day = Employee.objects.filter(
        monthly_tracking=True,
        monthly_tracking_day=current_day
    )
    
    # کارمندانی که زمان مشخص کرده‌اند و باید در ساعت فعلی گزارش دریافت کنند
    employees_with_time = employees_with_day.filter(
        monthly_tracking_time__hour=current_hour,
        monthly_tracking_time__minute=0  # فقط دقیقه 0 هر ساعت
    )
    
    # کارمندانی که زمان مشخص نکرده‌اند و باید در ساعت 12 ظهر گزارش دریافت کنند
    if current_hour == 12:
        employees_without_time = employees_with_day.filter(
            monthly_tracking_time__isnull=True
        )
        # ترکیب هر دو گروه کارمندان
        employees = list(employees_with_time) + list(employees_without_time)
    else:
        employees = employees_with_time
    
    if employees:
        print(f"ارسال گزارش ماهانه به {len(employees)} کارمند در روز {current_day} ماه ساعت {current_hour}:00")
        _call_sms_report_api("monthly_Motivation")
    else:
        print(f"هیچ کارمندی برای ارسال گزارش ماهانه در روز {current_day} ماه ساعت {current_hour}:00 یافت نشد")
