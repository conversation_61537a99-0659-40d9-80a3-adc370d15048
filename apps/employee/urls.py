from django.urls import path, include
from rest_framework import routers

from .views import EmployeeListView, MeView, JiraTaksView, UpdateDeviceTokenView, ScreenActiveAPIView, EmployeeScreanAPIView, EmployeeDetailView
from .views.v2 import EmployeeSignInView, EmployeeProfileView, LiveKitScreenShareNotificationView
from .api import day_details_api



urlpatterns_v2 = [
    path('v2/employees/signin/', EmployeeSignInView.as_view(), name='employee-signin'),
    path('v2/employees/profile/', EmployeeProfileView.as_view(), name='employee-profile'),
    path('v2/employees/livekit-screen-share/', LiveKitScreenShareNotificationView.as_view(), name='livekit-screen-share'),
]

urlpatterns = [
    path('me/', MeView.as_view(),name='me'),
    path('me/jira_taks/', JiraTaksView.as_view(),name='me'),
    path('employees/list/', EmployeeListView.as_view(),name='employee list'),
    path('employees/<int:employee_id>/', EmployeeDetailView.as_view(), name='employee_detail'),
    path('update-device-token/', UpdateDeviceTokenView.as_view(), name='update_device_token'),
    path('update-screan-active/', ScreenActiveAPIView.as_view(), name='update_screan_active'),
    path('employees-screan/<int:pk>/', EmployeeScreanAPIView.as_view(), name='employee_screan'),
    path('day-details/', day_details_api, name='day_details_api'),
] + urlpatterns_v2
