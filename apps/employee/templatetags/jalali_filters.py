import jdatetime
from django import template

register = template.Library()

@register.filter
def to_jalali(value, arg=None):
    if value:
        jalali_date = jdatetime.datetime.fromgregorian(datetime=value)
        if arg:
            return jalali_date.strftime(arg)
        else:
            return jalali_date.strftime('%Y-%m-%d')
    else:
        return ''

@register.filter
def divide(value, arg):
    try:
        return int(value) // int(arg)
    except (ValueError, ZeroDivisionError):
        return None

@register.filter
def modulo(value, arg):
    try:
        return int(value) % int(arg)
    except (ValueError, ZeroDivisionError):
        return None

@register.filter
def multiply(value, arg):
    try:
        return int(value) * int(arg)
    except (ValueError, TypeError):
        return None