from decimal import Decimal

from django.db import models
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from rest_framework.authtoken.models import Token
import uuid

from apps.activity.models import TimeEntry
from apps.teams.models import Teams


class Skill(models.Model):
    name = models.CharField(max_length=100, unique=True, verbose_name='Skill Name')
    description = models.TextField(null=True, blank=True, verbose_name='Description')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Skill'
        verbose_name_plural = 'Skills'
        ordering = ['name']

    def __str__(self):
        return self.name

class Employee(models.Model):
    # Weekday choices for weekly tracking
    WEEKDAY_CHOICES = [
        (0, 'شنبه'),
        (1, 'یکشنبه'),
        (2, 'دوشنبه'),
        (3, 'سه‌شنبه'),
        (4, 'چهارشنبه'),
        (5, 'پنجشنبه'),
        (6, 'جمعه'),
    ]

    full_name = models.CharField(max_length=200)
    email = models.EmailField(unique=True)
    password = models.CharField(max_length=100, null=True, blank=True)
    avatar = models.ImageField(upload_to='user_avatars/', null=True, blank=True)
    # type = models.CharField(max_length=32, choices=USER_TYPES)
    meet_id = models.CharField(max_length=100, null=True, blank=True)
    phone_number = models.CharField(max_length=100, null=True, blank=True)
    home_phone_number = models.CharField(max_length=100, null=True, blank=True, verbose_name="شماره تلفن منزل")
    home_address = models.CharField(max_length=300, null=True, blank=True, verbose_name="آدرس منزل")
    jira_username = models.CharField(max_length=100, null=True, blank=True)

    commitment_time_per_day = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True, help_text='مقدار تعهد روزانه به ساعت (مثال: 7.5 برای 7 ساعت و 30 دقیقه)')
    commitment_time_per_week = models.DecimalField(max_digits=6, decimal_places=2, null=True, blank=True, help_text='مقدار تعهد هفتگی به ساعت')
    commitment_time_per_month = models.DecimalField(max_digits=7, decimal_places=2, null=True, blank=True, help_text='مقدار تعهد ماهانه به ساعت')

    monthly_salary = models.PositiveIntegerField(null=True, blank=True, verbose_name="حقوق ماهیانه")
    hourly_salary = models.PositiveIntegerField(null=True, blank=True, verbose_name="حقوق به ازای هر ساعت کار")

    bank_card_number = models.CharField(max_length=16, null=True, blank=True, verbose_name="شماره کارت بانکی")
    bank_sheba_number = models.CharField(max_length=24, null=True, blank=True, verbose_name="شماره شبا بانکی")
    contract_file = models.FileField(upload_to='contracts/', null=True, blank=True, verbose_name="فایل قرارداد")
    interview_video = models.FileField(upload_to='interview_videos/', null=True, blank=True, verbose_name="فیلم مصاحبه کاری")

    # National ID card and birth certificate images
    national_id_card_image = models.ImageField(upload_to='id_cards/', null=True, blank=True, verbose_name="تصویر کارت ملی")
    birth_certificate_image = models.ImageField(upload_to='birth_certificates/', null=True, blank=True, verbose_name="تصویر شناسنامه")


    # Tracking settings
    daily_tracking = models.BooleanField(default=True, help_text='کمبود تعهدی کاربر به صورت روزانه به او اطلاع داده شود')
    daily_tracking_time = models.TimeField(null=True, blank=True, help_text='ساعت اطلاع‌رسانی روزانه (مثلا 18:00)', verbose_name='زمان اطلاع‌رسانی روزانه')
    
    weekly_tracking = models.BooleanField(default=False, help_text='کمبود تعهدی کاربر به صورت هفتگی به او اطلاع داده شود')
    weekly_tracking_day = models.PositiveSmallIntegerField(
        choices=WEEKDAY_CHOICES, 
        null=True, 
        blank=True, 
        help_text='روز هفته برای اطلاع‌رسانی هفتگی',
        verbose_name='روز اطلاع‌رسانی هفتگی'
    )
    weekly_tracking_time = models.TimeField(
        null=True, 
        blank=True, 
        help_text='ساعت اطلاع‌رسانی هفتگی (مثلا 18:00)', 
        verbose_name='زمان اطلاع‌رسانی هفتگی'
    )
    
    monthly_tracking = models.BooleanField(default=True, help_text='کمبود تعهدی کاربر به صورت ماهانه به او اطلاع داده شود')
    monthly_tracking_day = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(31)],
        null=True, 
        blank=True, 
        help_text='روز ماه برای اطلاع‌رسانی ماهانه (1 تا 31)',
        verbose_name='روز اطلاع‌رسانی ماهانه'
    )
    monthly_tracking_time = models.TimeField(
        null=True, 
        blank=True, 
        help_text='ساعت اطلاع‌رسانی ماهانه (مثلا 18:00)', 
        verbose_name='زمان اطلاع‌رسانی ماهانه'
    )

    report_validation_rules = models.TextField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    token = models.CharField("Token", max_length=60, default=None, blank=True)
    teams = models.ManyToManyField(Teams, verbose_name='Teams', blank=True)
    permission_employee = models.ManyToManyField('self', verbose_name='Permission Employee', blank=True)

    salary_pattern = models.ForeignKey('payroll.SalaryCalculationPattern', on_delete=models.SET_NULL, null=True, blank=True)
    screan_active = models.BooleanField(default=True, help_text='آیا صفحه نمایش کاربر به اشتراک گذاشته شود؟')


    company = models.ForeignKey('companies.Company', on_delete=models.SET_NULL, null=True, blank=True,
                               related_name='employees', verbose_name='شرکت')
    position = models.ForeignKey('companies.Position', on_delete=models.SET_NULL, null=True, blank=True,
                                related_name='employees', verbose_name='پوزیشن')
    is_admin = models.BooleanField(default=False, verbose_name='مدیر سیستم')

    # New fields
    bio = models.TextField(null=True, blank=True, verbose_name='بیوگرافی')
    skills = models.ManyToManyField(Skill, blank=True, verbose_name='مهارت‌ها')
    typical_working_weekday_start = models.TimeField(null=True, blank=True, verbose_name='ساعت شروع کار روزهای هفته')
    typical_working_weekday_end = models.TimeField(null=True, blank=True, verbose_name='ساعت پایان کار روزهای هفته')
    typical_working_weekend = models.CharField(max_length=200, null=True, blank=True, verbose_name='برنامه کاری آخر هفته')
    score = models.PositiveIntegerField(default=0, verbose_name='امتیاز')
    slogan = models.CharField(max_length=300, null=True, blank=True, verbose_name='شعار کارمند')
    city = models.CharField(max_length=100, null=True, blank=True, verbose_name='شهر')
    country = models.CharField(max_length=100, null=True, blank=True, verbose_name='کشور')

    class Meta:
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.full_name


    @property
    def device_tokens(self):
        return self.device_tokens.all()  # رابطه معکوس با مدل DeviceToken

    def get_connected_employees(self):
        """
        Get all employees who have access to this employee's information
        Based on team membership and permission relationships
        """
        from django.db.models import Q

        teams = self.teams.all()
        return Employee.objects.filter(
            Q(teams__in=teams) | Q(permission_employee=self)
        ).distinct().exclude(id=self.id)

    def save(self, *args, **kwargs):
        if not self.token:
            self.token = str(uuid.uuid4())
        if not self.meet_id:
            self.meet_id = str(uuid.uuid4())  # تنظیم خودکار meet_id به یک UUID
        super().save(*args, **kwargs)





class EmployeeContact(models.Model):
    class ContactType(models.TextChoices):
        EMAIL = 'email', 'Email'
        PHONE = 'phone', 'Phone'
        WHATSAPP = 'whatsapp', 'WhatsApp'
        TELEGRAM = 'telegram', 'Telegram'
        GITHUB = 'github', 'GitHub'
        LINKEDIN = 'linkedin', 'LinkedIn'
        INSTAGRAM = 'instagram', 'Instagram'
        TWITTER = 'twitter', 'Twitter'
        FACEBOOK = 'facebook', 'Facebook'
        SKYPE = 'skype', 'Skype'
        DISCORD = 'discord', 'Discord'
        SLACK = 'slack', 'Slack'
        YOUTUBE = 'youtube', 'YouTube'
        WEBSITE = 'website', 'Website'
        OTHER = 'other', 'Other'

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='employee_contacts', verbose_name='کارمند')
    contact_type = models.CharField(max_length=20, choices=ContactType.choices, verbose_name='نوع تماس')
    value = models.CharField(max_length=255, verbose_name='مقدار')
    is_primary = models.BooleanField(default=False, verbose_name='اصلی')
    is_public = models.BooleanField(default=True, verbose_name='عمومی')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'اطلاعات تماس کارمند'
        verbose_name_plural = 'اطلاعات تماس کارمندان'
        ordering = ['-is_primary', 'contact_type']
        unique_together = ('employee', 'contact_type', 'value')

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_contact_type_display()}: {self.value}"


class DeviceToken(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='device_tokens')
    token = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.employee.full_name} - {self.token}"



class EmployeeTelegramInfo(models.Model):
    employees = models.ManyToManyField('employee.Employee', related_name='telegram_infos')
    chat_id = models.CharField(max_length=100, verbose_name='Telegram Chat ID')
    is_active = models.BooleanField(default=True, verbose_name='Is Active')

    def __str__(self):
        return f"Telegram Chat: {self.chat_id}"