from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status


# Employee Sign In API Documentation
employee_signin_swagger = swagger_auto_schema(
    operation_description="Employee Sign In API - Authenticate employee with email and password",
    operation_summary="Employee Authentication",
    tags=['v2'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['email', 'password'],
        properties={
            'email': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_EMAIL,
                description='Employee email address',
                example='<EMAIL>'
            ),
            'password': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Employee password',
                example='mypassword123'
            ),
        },
        example={
            'email': '<EMAIL>',
            'password': 'mypassword123'
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Sign in successful",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=True
                    ),
                    'token': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Employee authentication token',
                        example='abc123def456ghi789'
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Success message',
                        example='Sign in successful'
                    ),
                }
            ),
            examples={
                'application/json': {
                    'success': True,
                    'token': 'abc123def456ghi789',
                    'message': 'Sign in successful'
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Bad request - Missing email or password",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=False
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Email and password are required'
                    ),
                }
            ),
            examples={
                'application/json': {
                    'success': False,
                    'message': 'Email and password are required'
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Unauthorized - Invalid credentials",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=False
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Invalid email or password'
                    ),
                }
            ),
            examples={
                'application/json': {
                    'success': False,
                    'message': 'Invalid email or password'
                }
            }
        ),
    }
)


# Employee Profile API Documentation
employee_profile_swagger = swagger_auto_schema(
    operation_description="Get Employee Profile - Returns employee information including name, email, position and company",
    operation_summary="Get Employee Profile",
    tags=['v2'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        ),
        openapi.Parameter(
            'version',
            openapi.IN_HEADER,
            description="API version for filtering incomplete activities (default: 2)",
            type=openapi.TYPE_STRING,
            required=False,
            example="2"
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Profile retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=True
                    ),
                    'data': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'full_name': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Employee full name',
                                example='John Doe'
                            ),
                            'email': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_EMAIL,
                                description='Employee email address',
                                example='<EMAIL>'
                            ),
                            'avatar': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                format=openapi.FORMAT_URI,
                                description='Employee avatar image URL (absolute URL)',
                                example='http://localhost:8000/media/user_avatars/avatar.jpg',
                                nullable=True
                            ),
                            'position': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Employee position name',
                                example='Senior Frontend Developer',
                                nullable=True
                            ),
                            'company': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Company name',
                                example='NewHorizon',
                                nullable=True
                            ),
                            'is_admin': openapi.Schema(
                                type=openapi.TYPE_BOOLEAN,
                                description='Is employee admin',
                                example=True
                            ),
                            'screan_active': openapi.Schema(
                                type=openapi.TYPE_BOOLEAN,
                                description='Is employee screen active',
                                example=True
                            ),
                            'incomplete_activity': openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                description='Incomplete activity object (based on version header with empty end_time or null is_activity_submitted)',
                                nullable=True,
                                properties={
                                    'activity_id': openapi.Schema(
                                        type=openapi.TYPE_INTEGER,
                                        description='ID of the incomplete activity',
                                        example=123
                                    ),
                                    'duration': openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        description='Duration of the activity in HH:MM:SS format',
                                        example='2:45:30'
                                    ),
                                }
                            ),


                        }
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Success message',
                        example='Profile retrieved successfully'
                    ),
                }
            ),
            examples={
                'application/json': {
                    'success': True,
                    'data': {
                        'full_name': 'John Doe',
                        'email': '<EMAIL>',
                        'avatar': 'http://localhost:8000/media/user_avatars/john_doe.jpg',
                        'position': 'Senior Frontend Developer',
                        'company': 'NewHorizon',
                        'is_admin': True,
                        'screan_active': True,
                        'incomplete_activity': {
                            'activity_id': 123,
                            'duration': '2:45:30'
                        }
                    },
                    'message': 'Profile retrieved successfully'
                }
            }
        ),
        status.HTTP_403_FORBIDDEN: openapi.Response(
            description="Permission denied - Invalid or missing token",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=False
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Permission denied - Invalid or missing token'
                    ),
                }
            ),
            examples={
                'application/json': {
                    'success': False,
                    'message': 'Permission denied - Invalid or missing token'
                }
            }
        ),
    }
)


# Employee List API Documentation
employee_list_swagger = swagger_auto_schema(
    operation_description="Get list of employees - Returns different data based on version header",
    operation_summary="Get Employee List",
    tags=['Employee API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        ),
        openapi.Parameter(
            'version',
            openapi.IN_HEADER,
            description="API version - when present, returns simplified employee list with id, name and position only",
            type=openapi.TYPE_STRING,
            required=False,
            example="2"
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Employee list retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Employee ID',
                            example=1
                        ),
                        'full_name': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Employee full name',
                            example='John Doe'
                        ),
                        'position_name': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Employee position name',
                            example='Senior Developer',
                            nullable=True
                        ),
                        'isonline': openapi.Schema(
                            type=openapi.TYPE_BOOLEAN,
                            description='Whether employee is currently online (has active time entry)',
                            example=True
                        ),
                        'screan_active': openapi.Schema(
                            type=openapi.TYPE_BOOLEAN,
                            description='Whether employee screen is active',
                            example=True
                        ),
                        'avatar': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            format=openapi.FORMAT_URI,
                            description='Employee avatar image URL (absolute URL)',
                            example='http://127.0.0.1:8000/media/user_avatars/avatar.jpg',
                            nullable=True
                        ),
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        'id': 1,
                        'full_name': 'John Doe',
                        'position_name': 'Senior Developer',
                        'isonline': True,
                        'screan_active': True,
                        'avatar': 'http://127.0.0.1:8000/media/user_avatars/john_doe.jpg'
                    },
                    {
                        'id': 2,
                        'full_name': 'Jane Smith',
                        'position_name': 'UI/UX Designer',
                        'isonline': False,
                        'screan_active': False,
                        'avatar': 'http://127.0.0.1:8000/media/user_avatars/jane_smith.jpg'
                    }
                ]
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Unauthorized - Invalid or missing token",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Authentication credentials were not provided.'
                    ),
                }
            )
        ),
    }
)


# Employee Detail API Documentation
employee_detail_swagger = swagger_auto_schema(
    operation_description="Get detailed information about a specific employee",
    operation_summary="Get Employee Details",
    tags=['Employee API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        ),
        openapi.Parameter(
            'employee_id',
            openapi.IN_PATH,
            description="Employee ID",
            type=openapi.TYPE_INTEGER,
            required=True,
            example=1
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Employee details retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(
                        type=openapi.TYPE_INTEGER,
                        description='Employee ID',
                        example=1
                    ),
                    'full_name': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Employee full name',
                        example='John Doe'
                    ),
                    'email': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        format=openapi.FORMAT_EMAIL,
                        description='Employee email address',
                        example='<EMAIL>'
                    ),
                    'phone_number': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Employee phone number',
                        example='+1234567890',
                        nullable=True
                    ),
                    'jira_username': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Jira username',
                        example='john.doe',
                        nullable=True
                    ),
                    'bio': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Employee biography',
                        example='Experienced software developer with 5+ years in web development',
                        nullable=True
                    ),
                    'skills': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        description='List of employee skills',
                        example=['Python', 'Django', 'JavaScript', 'React']
                    ),
                    'typical_working_weekday_start': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Typical weekday start time (HH:MM:SS)',
                        example='09:00:00',
                        nullable=True
                    ),
                    'typical_working_weekday_end': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Typical weekday end time (HH:MM:SS)',
                        example='17:30:00',
                        nullable=True
                    ),
                    'typical_working_weekend': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Weekend working schedule',
                        example='Available for urgent tasks only',
                        nullable=True
                    ),
                    'score': openapi.Schema(
                        type=openapi.TYPE_NUMBER,
                        format=openapi.FORMAT_FLOAT,
                        description='Employee score (converted from 0-100 to 1-5 scale with decimal precision)',
                        example=4.2,
                        minimum=1.0,
                        maximum=5.0
                    ),
                    'slogan': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Employee slogan',
                        example='Code with passion, deliver with precision',
                        nullable=True
                    ),
                    'projects': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'name': openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description='Project name',
                                    example='E-commerce Platform'
                                ),
                                'role': openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description='Employee role in this project (derived from topic name)',
                                    example='Backend Developer'
                                ),
                            }
                        ),
                        description='List of projects employee is working on with their roles',
                        example=[
                            {'name': 'E-commerce Platform', 'role': 'Backend Developer'},
                            {'name': 'Mobile App Backend', 'role': 'API Developer'}
                        ]
                    ),
                    'position': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Employee position name',
                        example='Senior Developer',
                        nullable=True
                    ),
                    'created_at': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        format=openapi.FORMAT_DATETIME,
                        description='Employee creation date',
                        example='2023-01-15T10:30:00Z'
                    ),
                    'city': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Employee city',
                        example='Tehran',
                        nullable=True
                    ),
                    'country': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Employee country',
                        example='Iran',
                        nullable=True
                    ),
                    'contacts': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        description='Employee contact information (only public contacts). Returns null if no contacts exist.',
                        nullable=True,
                        properties={
                            'email': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Email address',
                                example='<EMAIL>'
                            ),
                            'phone': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Phone number',
                                example='+1234567890'
                            ),
                            'whatsapp': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='WhatsApp number',
                                example='+1234567890'
                            ),
                            'telegram': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Telegram username',
                                example='@johndoe'
                            ),
                            'github': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='GitHub profile URL',
                                example='https://github.com/johndoe'
                            ),
                            'linkedin': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='LinkedIn profile URL',
                                example='https://linkedin.com/in/johndoe'
                            ),
                            'instagram': openapi.Schema(
                                type=openapi.TYPE_STRING,
                                description='Instagram profile URL',
                                example='https://instagram.com/johndoe'
                            ),
                        },
                        example={
                            'email': '<EMAIL>',
                            'phone': '+1234567890',
                            'github': 'https://github.com/johndoe',
                            'linkedin': 'https://linkedin.com/in/johndoe'
                        }
                    ),
                    'isonline': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Whether employee is currently online',
                        example=True
                    ),
                    'last_seen': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        format=openapi.FORMAT_DATETIME,
                        description='Last time employee was seen (last end_time)',
                        example='2023-12-01T17:30:00Z',
                        nullable=True
                    ),
                }
            ),
            examples={
                'application/json': {
                    'id': 1,
                    'full_name': 'John Doe',
                    'email': '<EMAIL>',
                    'phone_number': '+1234567890',
                    'jira_username': 'john.doe',
                    'bio': 'Experienced software developer with 5+ years in web development',
                    'skills': ['Python', 'Django', 'JavaScript', 'React'],
                    'typical_working_weekday_start': '09:00:00',
                    'typical_working_weekday_end': '17:30:00',
                    'typical_working_weekend': 'Available for urgent tasks only',
                    'score': 4.2,
                    'slogan': 'Code with passion, deliver with precision',
                    'projects': [
                        {'name': 'E-commerce Platform', 'role': 'Backend Developer'},
                        {'name': 'Mobile App Backend', 'role': 'API Developer'}
                    ],
                    'position': 'Senior Developer',
                    'created_at': '2023-01-15T10:30:00Z',
                    'city': 'Tehran',
                    'country': 'Iran',
                    'contacts': {
                        'email': '<EMAIL>',
                        'phone': '+1234567890',
                        'github': 'https://github.com/johndoe',
                        'linkedin': 'https://linkedin.com/in/johndoe',
                        'telegram': '@johndoe'
                    },
                    'isonline': True,
                    'last_seen': '2023-12-01T17:30:00Z'
                }
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Employee not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'error': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Employee not found'
                    ),
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Unauthorized - Invalid or missing token",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Authentication credentials were not provided.'
                    ),
                }
            )
        ),
    }
)
