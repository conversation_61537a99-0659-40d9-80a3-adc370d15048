from django.core.management.base import BaseCommand
from apps.teams.models import Teams


class Command(BaseCommand):
    help = 'Create standard software development teams'

    def handle(self, *args, **options):
        # لیست تیم‌های استاندارد در شرکت‌های نرم‌افزاری
        software_teams = [
            'Frontend Development',
            'Backend Development', 
            'Mobile Development',
            'DevOps',
            'Quality Assurance (QA)',
            'UI/UX Design',
            'Product Management',
            'Data Science',
            'Machine Learning',
            'Security',
            'Database Administration',
            'System Administration',
            'Technical Writing',
            'Business Analysis',
            'Project Management',
            'Scrum Master',
            'Architecture',
            'Research & Development',
            'Support & Maintenance',
            'Marketing Technology'
        ]

        created_count = 0
        existing_count = 0

        for team_name in software_teams:
            team, created = Teams.objects.get_or_create(name=team_name)
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Created team: {team_name}')
                )
            else:
                existing_count += 1
                self.stdout.write(
                    self.style.WARNING(f'- Team already exists: {team_name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'\n📊 Summary:'
                f'\n   • Created: {created_count} teams'
                f'\n   • Already existed: {existing_count} teams'
                f'\n   • Total teams: {Teams.objects.count()}'
            )
        )
