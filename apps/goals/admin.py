from django.urls import path, reverse_lazy

from django.utils.translation import gettext_lazy as _
from apps.goals.models import Project, Topic, TopicProject, Target, Comment, ProjectTopicEmployee
from apps.employee.models import Employee
from unfold.admin import ModelAdmin, TabularInline
from utils.admin import project_admin_site
from apps.goals.views import OverviewBasedView, ProjectDetailView

class TargetInline(TabularInline):
    model = Target
    extra = 0
    fields = ('name', 'description', 'is_completed', 'due_date')
    tab = True


class CommentInline(TabularInline):
    model = Comment
    extra = 0
    fields = ('text', 'created_at')
    readonly_fields = ('created_at',)
    tab = True


class ProjectTopicEmployeeInline(TabularInline):
    model = ProjectTopicEmployee
    extra = 0
    fields = ('employee', 'topic')
    auto_complete_fields = ['employee', ]
    fk_name = 'project'
    tab = True


class TopicProjectInline(TabularInline):
    model = TopicProject
    extra = 0
    fields = ('topic', 'created_at')
    readonly_fields = ('created_at',)
    tab = True
    
    # Add custom attributes for the inline form
    classes = ('topic_project-tab',)
    
    # Override formfield_for_foreignkey to filter out already selected topics
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        formfield = super().formfield_for_foreignkey(db_field, request, **kwargs)
        
        # Only customize the topic field
        if db_field.name == 'topic':
            # Add custom CSS classes and data attributes for JavaScript and Django Unfold
            formfield.widget.attrs.update({
                'class': 'topic-select unfold-select',
                'data-controller': 'topic-select unfold-topic-select',
                'data-unfold-component': 'select',
                'data-action': 'change->topic-select#handleChange',
            })
        
        return formfield



class ProjectAdmin(ModelAdmin):
    list_display = ('name', 'description', 'created_at', 'progress')
    list_filter = ('created_at',)
    readonly_fields = ('created_at', 'updated_at')
    
    # Define inlines with tabs
    inlines = [TopicProjectInline, ProjectTopicEmployeeInline, CommentInline]
    # Organize fieldsets
    fieldsets = (
        (_('Project Information'), {
            'fields': ('name', 'description', 'icon'),
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
        }),
    )
    
    latest_by = 'created_at'
    ordering = ['-created_at']

    
    class Media:
        js = ('js/topic_select_controller.js', 'js/topic_project_inline.js', 'js/topic_project_unfold.js',)
        css = {
            'all': ('css/topic_project_inline.css',)
        }

    def get_urls(self):
        return super().get_urls() + [
            path(
                "custom-url-path",
                self.admin_site.admin_view(OverviewBasedView.as_view(model_admin=self)),
                name="custom_view",
            ),
        ]


class TopicProjectEmployeeTopicInline(TabularInline):
    model = ProjectTopicEmployee
    extra = 0
    fields = ('employee', 'project', 'created_at')
    readonly_fields = ('created_at',)
    fk_name = 'topic'
    tab = True


class TopicAdmin(ModelAdmin):
    list_display = ('name', 'description', 'created_at')
    search_fields = ('name', 'description')
    list_filter = ('created_at',)
    readonly_fields = ('created_at', 'updated_at')
    
    # Define inlines with tabs
    inlines = [TopicProjectInline, TopicProjectEmployeeTopicInline]
    
    # Organize fieldsets
    fieldsets = (
        (_('Topic Information'), {
            'fields': ('name', 'description'),
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
        }),
    )
    
    latest_by = 'created_at'
    ordering = ['-created_at']
    
    def has_delete_permission(self, request, obj=None):
        """Remove delete permission for all users"""
        return False
    


class TopicProjectAdmin(ModelAdmin):
    list_display = ('topic', 'project', 'created_at', 'progress')
    search_fields = ('topic__name', 'project__name')
    list_filter = ('topic', 'project', 'created_at')
    readonly_fields = ('created_at', 'updated_at')
    
    # Define inlines with tabs
    inlines = [TargetInline]
    
    # Organize fieldsets
    fieldsets = (
        (_('Topic Project Information'), {
            'fields': ('topic', 'project'),
        }),
        (_('Metadata'), {
            'fields': ('created_at', 'updated_at'),
        }),
    )
    
    latest_by = 'created_at'
    ordering = ['-created_at']


class TargetAdmin(ModelAdmin):
    list_display = ('name', 'topic_project', 'is_completed', 'due_date', 'created_at')
    search_fields = ('name', 'description', 'topic_project__topic__name', 'topic_project__project__name')
    list_filter = ('is_completed', 'topic_project__topic', 'topic_project__project', 'created_at', 'due_date')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('Target Information'), {'fields': ('name', 'description', 'topic_project', 'is_completed', 'due_date')}),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )
    latest_by = 'created_at'
    ordering = ['-created_at']


class CommentAdmin(ModelAdmin):
    list_display = ('project', 'text', 'created_at')
    search_fields = ('text', 'project__name')
    list_filter = ('project', 'created_at')
    fieldsets = (
        (_('Comment Information'), {'fields': ('text', 'project',)}),
    )
    latest_by = 'created_at'
    ordering = ['-created_at']


class ProjectTopicEmployeeAdmin(ModelAdmin):
    list_display = ('employee', 'project', 'topic', 'created_at')
    search_fields = ('employee__full_name', 'employee__email', 'topic__name', 'project__name')
    list_filter = ('topic', 'project', 'employee', 'created_at')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (_('Project Topic Employee Information'), {'fields': ('employee', 'project', 'topic')}),
        (_('Metadata'), {'fields': ('created_at', 'updated_at')}),
    )
    latest_by = 'created_at'
    ordering = ['-created_at']


# Register with the project_admin_site (Django Unfold)
project_admin_site.register(Project, ProjectAdmin)
project_admin_site.register(Topic, TopicAdmin)
project_admin_site.register(TopicProject, TopicProjectAdmin)
project_admin_site.register(Target, TargetAdmin)
project_admin_site.register(Comment, CommentAdmin)
project_admin_site.register(ProjectTopicEmployee, ProjectTopicEmployeeAdmin)