from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status


# Projects List API Documentation
projects_list_swagger = swagger_auto_schema(
    operation_description="Get list of all projects with id and name only",
    operation_summary="Get Projects List",
    tags=['v2'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Projects retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=True
                    ),
                    'data': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description='Project ID',
                                    example=1
                                ),
                                'name': openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description='Project name',
                                    example='Mobile App Development'
                                ),
                            }
                        )
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Success message',
                        example='Projects retrieved successfully'
                    ),
                }
            ),
            examples={
                'application/json': {
                    'success': True,
                    'data': [
                        {'id': 1, 'name': 'Mobile App Development'},
                        {'id': 2, 'name': 'Web Platform'},
                        {'id': 3, 'name': 'API Integration'}
                    ],
                    'message': 'Projects retrieved successfully'
                }
            }
        ),
        status.HTTP_403_FORBIDDEN: openapi.Response(
            description="Permission denied - Invalid or missing token",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=False
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Permission denied - Invalid or missing token'
                    ),
                }
            )
        ),
    }
)


# Project Targets API Documentation
project_targets_swagger = swagger_auto_schema(
    operation_description="Get list of targets for a specific project",
    operation_summary="Get Project Targets",
    tags=['v2'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        ),
        openapi.Parameter(
            'project_id',
            openapi.IN_PATH,
            description="Project ID",
            type=openapi.TYPE_INTEGER,
            required=True,
            example=1
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Project targets retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=True
                    ),
                    'data': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(
                                    type=openapi.TYPE_INTEGER,
                                    description='Target ID',
                                    example=1
                                ),
                                'name': openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    description='Target name',
                                    example='Complete user authentication'
                                ),
                            }
                        )
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Success message',
                        example='Project targets retrieved successfully'
                    ),
                }
            ),
            examples={
                'application/json': {
                    'success': True,
                    'data': [
                        {
                            'id': 1,
                            'name': 'Complete user authentication'
                        },
                        {
                            'id': 2,
                            'name': 'Design user interface'
                        }
                    ],
                    'message': 'Project targets retrieved successfully'
                }
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Project not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=False
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Project not found'
                    ),
                }
            )
        ),
        status.HTTP_403_FORBIDDEN: openapi.Response(
            description="Permission denied - Invalid or missing token",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'success': openapi.Schema(
                        type=openapi.TYPE_BOOLEAN,
                        description='Success status',
                        example=False
                    ),
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description='Error message',
                        example='Permission denied - Invalid or missing token'
                    ),
                }
            )
        ),
    }
)
