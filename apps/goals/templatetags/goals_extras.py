from django import template
from django.utils.safestring import mark_safe
from itertools import chain

register = template.Library()

@register.filter
def map(value, arg):
    """
    Apply a method to each item in a list
    Usage: {{ my_list|map:"method_name" }}
    """
    if not value:
        return []
    
    result = []
    for item in value:
        if hasattr(item, arg):
            method = getattr(item, arg)
            if callable(method):
                result.append(method())
            else:
                result.append(method)
    return result

@register.filter
def flatten(value):
    """
    Flatten a list of lists into a single list
    Usage: {{ list_of_lists|flatten }}
    """
    if not value:
        return []
    
    return list(chain.from_iterable(value))

@register.filter
def multiply(value, arg):
    """
    Multiply the value by the argument
    Usage: {{ value|multiply:2 }}
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def divide(value, arg):
    """
    Divide the value by the argument
    Usage: {{ value|divide:2 }}
    """
    try:
        return float(value) / float(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def add(value, arg):
    """
    Add the argument to the value
    Usage: {{ value|add:2 }}
    """
    try:
        return float(value) + float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def subtract(value, arg):
    """
    Subtract the argument from the value
    Usage: {{ value|subtract:2 }}
    """
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def filter_by_project(topic_projects, project):
    """
    Filter topic projects by project
    Usage: {{ topic.topic_projects.all|filter_by_project:project }}
    Returns the first topic project that matches the project
    """
    if not topic_projects:
        return None
    
    for topic_project in topic_projects:
        if topic_project.project == project:
            return topic_project
    
    return None