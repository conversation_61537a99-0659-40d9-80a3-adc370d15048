from rest_framework.response import Response
from rest_framework import status
from rest_framework.generics import GenericAPIView
from apps.goals.models import Project, Target
from apps.permissions import EmployeeTokenPermission
from apps.goals.docs import *

class ProjectsListAPIView(GenericAPIView):
    """
    API View to get list of projects with id and name only
    """
    permission_classes = [EmployeeTokenPermission]
    pagination_class = None

    @projects_list_swagger
    def get(self, request):
        projects = Project.objects.all().values('id', 'name')

        return Response({
            'success': True,
            'data': list(projects),
            'message': 'Projects retrieved successfully'
        }, status=status.HTTP_200_OK)


class ProjectTargetsAPIView(GenericAPIView):
    """
    API View to get list of targets for a specific project
    """
    permission_classes = [EmployeeTokenPermission]
    pagination_class = None

    @project_targets_swagger
    def get(self, request, project_id):
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Project not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Get targets for this project through TopicProject relationship
        targets = Target.objects.filter(
            topic_project__project=project
        ).distinct().values('id', 'name')

        return Response({
            'success': True,
            'data': list(targets),
            'message': 'Project targets retrieved successfully'
        }, status=status.HTTP_200_OK)
