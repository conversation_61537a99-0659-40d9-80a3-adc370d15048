import json
import random
from functools import lru_cache

from django.contrib.humanize.templatetags.humanize import intcomma
from django.http import JsonResponse
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.views.generic import RedirectView, TemplateView
from unfold.views import UnfoldModelAdminViewMixin


class HomeView(RedirectView):
    pattern_name = "admin:index"


class OverviewBasedView(UnfoldModelAdminViewMixin, TemplateView):
    title = _("Projects Overview")
    template_name = "goals/overview.html"

    def has_permission(self):
        return True
    
    def get_template_names(self):
        # Check if project_id is in the request GET parameters
        project_id = self.request.GET.get('project_id')
        if project_id:
            self.title = _("Project Details")
            return ["goals/project_detail.html"]
        return ["goals/overview.html"]
    
    def get_color_for_topic(self, topic_id):
        """
        Generate a consistent color based on topic ID
        """
        # List of nice colors for topics
        colors = [
            '#6366f1',  # Indigo
            '#8b5cf6',  # Violet
            '#ec4899',  # Pink
            '#ef4444',  # Red
            '#f97316',  # Orange
            '#eab308',  # Yellow
            '#22c55e',  # Green
            '#14b8a6',  # Teal
            '#0ea5e9',  # Sky
            '#6366f1',  # Indigo
            '#a855f7',  # Purple
            '#d946ef',  # Fuchsia
        ]
        
        # Use modulo to get a consistent color based on topic ID
        return colors[topic_id % len(colors)]
    
    def post(self, request, *args, **kwargs):
        from django.shortcuts import redirect
        from django.contrib import messages
        from apps.goals.forms import CommentForm
        
        # Get project_id from request
        project_id = request.GET.get('project_id')
        if project_id:
            # Process comment form
            form = CommentForm(request.POST)
            if form.is_valid():
                form.save()
                messages.success(request, _("Comment added successfully!"))
            else:
                messages.error(request, _("Error adding comment. Please try again."))
            
            # Redirect back to the project detail page with modal parameter
            return redirect(f"{request.path}?project_id={project_id}&modal=true")
        
        return self.get(request, *args, **kwargs)
        
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from apps.goals.models import Project, Target, Topic, TopicProject, ProjectTopicEmployee, Comment
        from apps.employee.models import Employee
        from django.db.models import Count, Sum, Avg, Q
        from django.shortcuts import get_object_or_404
        from apps.goals.forms import CommentForm
        
        # Check if project_id is in the request GET parameters
        project_id = self.request.GET.get('project_id')
        
        if project_id:
            # Get project by ID from URL
            project = get_object_or_404(Project, id=project_id)
            
            # Get topic_projects for this project
            topic_projects = project.topic_projects.all()
            
            # Get all topics for this project with their progress
            topics = Topic.objects.filter(topic_projects__project=project).distinct()
            
            # Calculate progress for each topic in this project
            for topic in topics:
                topic_project = TopicProject.objects.filter(topic=topic, project=project).first()
                if topic_project:
                    topic.progress = topic_project.progress
                    # Add color code for the topic (for visual distinction)
                    topic.color_code = self.get_color_for_topic(topic.id)
                else:
                    topic.progress = 0
                    topic.color_code = '#6366f1'  # Default color
            
            # Get all targets for this project
            targets = Target.objects.filter(topic_project__project=project)
            
            # Get all comments for this project
            comments = project.comments.all().order_by('created_at')
            
            # Create comment form
            comment_form = CommentForm(initial={'project': project.id})
            
            # Calculate statistics
            total_topics = topics.count()
            total_topic_projects = topic_projects.count()
            total_targets = targets.count()
            completed_targets = targets.filter(is_completed=True).count()
            
            # Calculate project progress
            project_progress = project.progress
            
            # Get employees for this project
            employees = Employee.objects.filter(project_topics__project=project).distinct()
            total_employees = employees.count()
            
            # Add data to context
            context.update({
                'project': project,
                'topic_projects': topic_projects,
                'topics': topics,
                'employees': employees,
                'total_topics': total_topics,
                'total_topic_projects': total_topic_projects,
                'total_targets': total_targets,
                'completed_targets': completed_targets,
                'project_progress': project_progress,
                'total_employees': total_employees,
                'comments': comments,
                'comment_form': comment_form,
            })
        else:
            # Get all projects
            projects = Project.objects.all()
            
            # Calculate statistics
            total_projects = projects.count()
            total_targets = Target.objects.count()
            completed_targets = Target.objects.filter(is_completed=True).count()
            
            # Calculate overall progress
            overall_progress = 0
            if total_targets > 0:
                overall_progress = (completed_targets / total_targets) * 100
            
            # Add data to context
            context.update({
                'projects': projects,
                'total_projects': total_projects,
                'total_targets': total_targets,
                'completed_targets': completed_targets,
                'overall_progress': overall_progress,
            })
        
        return context


@csrf_exempt
@require_POST
def update_target_status(request):
    """
    API endpoint to update target status (completed/not completed)
    """
    from apps.goals.models import Target
    from django.shortcuts import get_object_or_404
    
    target_id = request.POST.get('target_id')
    
    if not target_id:
        return JsonResponse({'success': False, 'error': 'Target ID is required'}, status=400)
    
    try:
        target = get_object_or_404(Target, id=target_id)
        
        # Toggle the status
        target.is_completed = not target.is_completed
        target.save()
        
        # Get the updated topic progress
        topic_project = target.topic_project
        topic_progress = topic_project.progress
        
        # Get the updated project progress
        project = topic_project.project
        project_progress = project.progress
        
        return JsonResponse({
            'success': True, 
            'target_id': target.id,
            'is_completed': target.is_completed,
            'topic_progress': topic_progress,
            'project_progress': project_progress
        })
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


class ProjectDetailView(UnfoldModelAdminViewMixin, TemplateView):
    title = _("Project Details")
    template_name = "goals/project_detail.html"
    
    def has_permission(self):
        return True
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from apps.goals.models import Project, Target, Topic, TopicProject, ProjectTopicEmployee, Comment
        from apps.employee.models import Employee
        from django.db.models import Count, Sum, Avg, Q
        from django.shortcuts import get_object_or_404
        
        # Get project by ID from URL
        project_id = self.kwargs.get('project_id')
        project = get_object_or_404(Project, id=project_id)
        
        # Get topic_projects for this project
        topic_projects = project.topic_projects.all()
        
        # Get all topics for this project with their progress
        topics = Topic.objects.filter(topic_projects__project=project).distinct()
        
        # Calculate progress for each topic in this project
        for topic in topics:
            topic_project = TopicProject.objects.filter(topic=topic, project=project).first()
            if topic_project:
                topic.progress = topic_project.progress
            else:
                topic.progress = 0
        
        # Get all targets for this project
        targets = Target.objects.filter(topic_project__project=project)
        
        # Get all comments for this project
        comments = project.comments.all().order_by('created_at')
        
        # Calculate statistics
        total_topics = topics.count()
        total_topic_projects = topic_projects.count()
        total_targets = targets.count()
        completed_targets = targets.filter(is_completed=True).count()
        
        # Calculate project progress
        project_progress = project.progress
        
        # Get employees for this project
        employees = Employee.objects.filter(project_topics__project=project).distinct()
        total_employees = employees.count()
        
        # Add data to context
        context.update({
            'project': project,
            'topic_projects': topic_projects,
            'topics': topics,
            'employees': employees,
            'total_topics': total_topics,
            'total_topic_projects': total_topic_projects,
            'total_targets': total_targets,
            'completed_targets': completed_targets,
            'project_progress': project_progress,
            'total_employees': total_employees,
            'comments': comments,
        })
        
        return context
