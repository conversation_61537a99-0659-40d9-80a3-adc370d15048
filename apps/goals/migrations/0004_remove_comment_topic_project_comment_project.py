# Generated by Django 5.1.8 on 2025-04-18 03:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('goals', '0003_remove_comment_created_by_remove_comment_topic_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='comment',
            name='topic_project',
        ),
        migrations.AddField(
            model_name='comment',
            name='project',
            field=models.ForeignKey(default='1', on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='goals.project', verbose_name='Project'),
            preserve_default=False,
        ),
    ]
