# Generated by Django 5.1.8 on 2025-04-18 02:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0022_alter_employee_permission_employee'),
        ('goals', '0002_remove_project_members_remove_task_assigned_to'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='comment',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='comment',
            name='topic',
        ),
        migrations.RemoveField(
            model_name='project',
            name='color_code',
        ),
        migrations.RemoveField(
            model_name='project',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='topic',
            name='color_code',
        ),
        migrations.RemoveField(
            model_name='topic',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='topic',
            name='project',
        ),
        migrations.CreateModel(
            name='ProjectTopicEmployee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_topics', to='employee.employee', verbose_name='Employee')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='topic_employees', to='goals.project', verbose_name='Project')),
                ('topic', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='project_employees', to='goals.topic', verbose_name='Topic')),
            ],
            options={
                'verbose_name': 'Project Topic Employee',
                'verbose_name_plural': 'Project Topic Employees',
                'ordering': ['-created_at'],
                'unique_together': {('employee', 'project', 'topic')},
            },
        ),
        migrations.CreateModel(
            name='TopicProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='topic_projects', to='goals.project', verbose_name='Project')),
                ('topic', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='topic_projects', to='goals.topic', verbose_name='Topic')),
            ],
            options={
                'verbose_name': 'Topic Project',
                'verbose_name_plural': 'Topic Projects',
                'ordering': ['-created_at'],
                'unique_together': {('topic', 'project')},
            },
        ),
        migrations.CreateModel(
            name='Target',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_completed', models.BooleanField(default=False, verbose_name='Completed')),
                ('due_date', models.DateTimeField(blank=True, null=True, verbose_name='Due Date')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('topic_project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='targets', to='goals.topicproject', verbose_name='Topic Project')),
            ],
            options={
                'verbose_name': 'Target',
                'verbose_name_plural': 'Targets',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='comment',
            name='topic_project',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='goals.topicproject', verbose_name='Topic Project'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='project',
            name='topics',
            field=models.ManyToManyField(related_name='projects', through='goals.TopicProject', to='goals.topic'),
        ),
        migrations.DeleteModel(
            name='Task',
        ),
    ]
