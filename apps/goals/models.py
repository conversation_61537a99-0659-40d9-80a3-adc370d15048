from django.db import models
from django.utils.translation import gettext_lazy as _
from apps.account.models import User
from apps.employee.models import Employee


class Project(models.Model):
    """
    Top-level organization unit for goal management
    """
    name = models.CharField(_("Name"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    icon = models.ImageField(_("Icon"), upload_to='goals/project-icons/', null=True, blank=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    topics = models.ManyToManyField('Topic', through='TopicProject', related_name='projects')
    
    class Meta:
        verbose_name = _('Project')
        verbose_name_plural = _('Projects')
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        """Override save method to automatically create TopicProject connections"""
        is_new = self.pk is None
        super().save(*args, **kwargs)
        
        # Only add default topics if this is a new project
        if is_new:
            # Create TopicProject connections for specific topic IDs
            default_topic_ids = [2, 3, 4, 6, 8]
            
            for topic_id in default_topic_ids:
                try:
                    topic = Topic.objects.get(id=topic_id)
                    # Use get_or_create to avoid duplicate entries
                    TopicProject.objects.get_or_create(topic=topic, project=self)
                except Topic.DoesNotExist:
                    # Skip if the topic doesn't exist
                    pass
    
    @property
    def progress(self):
        """Calculate overall project progress based on topics progress"""
        topic_projects = self.topic_projects.all()
        if not topic_projects:
            return 0
        
        total_progress = sum(tp.progress for tp in topic_projects)
        return total_progress / topic_projects.count() if topic_projects.count() > 0 else 0


class Topic(models.Model):
    """
    Work categories (like backend, frontend, UI/UX design) that can be used across multiple projects
    """
    name = models.CharField(_("Name"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    
    class Meta:
        verbose_name = _('Topic')
        verbose_name_plural = _('Topics')
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name


class TopicProject(models.Model):
    """
    Intermediate model connecting Topics to Projects in a many-to-many relationship
    """
    topic = models.ForeignKey(Topic, on_delete=models.CASCADE, related_name='topic_projects',
                             verbose_name=_("Topic"))
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='topic_projects',
                               verbose_name=_("Project"))
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    
    class Meta:
        verbose_name = _('Topic Project')
        verbose_name_plural = _('Topic Projects')
        ordering = ['-created_at']
        unique_together = ('topic', 'project')
    
    def __str__(self):
        return f"{self.topic.name} in {self.project.name}"
    
    @property
    def progress(self):
        """Calculate topic progress in this project based on completed targets"""
        targets = self.targets.all()
        if not targets:
            return 0
        
        completed_targets = targets.filter(is_completed=True).count()
        return (completed_targets / targets.count()) * 100 if targets.count() > 0 else 0


class Target(models.Model):
    """
    Specific activities with completion status (True/False)
    """
    name = models.CharField(_("Name"), max_length=200)
    description = models.TextField(_("Description"), blank=True)
    topic_project = models.ForeignKey(TopicProject, on_delete=models.CASCADE, related_name='targets',
                             verbose_name=_("Topic Project"))
    is_completed = models.BooleanField(_("Completed"), default=False)
    due_date = models.DateTimeField(_("Due Date"), null=True, blank=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    
    class Meta:
        verbose_name = _('Target')
        verbose_name_plural = _('Targets')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.topic_project})"


class Comment(models.Model):
    """
    Comments for projects to record notes and progress details
    """
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='comments',
                             verbose_name=_("Project"))
    text = models.TextField(_("Text"))
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    
    class Meta:
        verbose_name = _('Comment')
        verbose_name_plural = _('Comments')
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Comment on {self.project.name}"


class ProjectTopicEmployee(models.Model):
    """
    Model to track which employees are working on which projects and topics
    """
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='project_topics',
                            verbose_name=_("Employee"))
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='topic_employees',
                               verbose_name=_("Project"))
    topic = models.ForeignKey(Topic, on_delete=models.CASCADE, related_name='project_employees',
                             verbose_name=_("Topic"))
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)
    
    class Meta:
        verbose_name = _('Project Topic Employee')
        verbose_name_plural = _('Project Topic Employees')
        ordering = ['-created_at']
        unique_together = ('employee', 'project', 'topic')
    
    def __str__(self):
        return f"{self.employee.full_name} working on {self.topic.name} in {self.project.name}"
