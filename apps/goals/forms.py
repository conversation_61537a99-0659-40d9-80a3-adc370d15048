from django import forms
from unfold.widgets import UnfoldAdminTextInputWidget
from .models import Comment

class CommentForm(forms.ModelForm):
    """
    Form for creating comments with Unfold styling
    """
    class Meta:
        model = Comment
        fields = ['text', 'project']
        widgets = {
            'text': forms.Textarea(attrs={
                'class': 'w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
                'rows': 3,
                'placeholder': 'Write your comment here...',
            }),
            'project': forms.HiddenInput(),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Optionally use UnfoldAdminTextInputWidget for the 'text' field as a single-line input
        self.fields['text'].widget = UnfoldAdminTextInputWidget(attrs={
            'placeholder': 'Write your comment here...',
            'style': 'width: 100%; height: 60px;',  # اینجا اندازه height رو بیشتر کردیم
        })
