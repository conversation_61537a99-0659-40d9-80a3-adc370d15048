from django.core.management.base import BaseCommand
from django.db import transaction
from apps.goals.models import Project, Topic, TopicProject, Target, Comment, ProjectTopicEmployee


class Command(BaseCommand):
    help = 'Deletes all existing Project and Topic records and related data'


    def handle(self, *args, **options):

        with transaction.atomic():
            topic_project_count = TopicProject.objects.all().count()
            TopicProject.objects.all().delete()
            
            # Then delete the main models
            project_count = Project.objects.all().count()
            Project.objects.all().delete()
            
            topic_count = Topic.objects.all().count()
            Topic.objects.all().delete()
