{% extends "admin/base.html" %}

{% load admin_urls i18n unfold static goals_extras %}

{% block title %}
    {{ project.name }} | {% trans 'Project Details' %} | {{ site_title|default:_('Django site admin') }}
{% endblock %}

{% block branding %}
    {% include "unfold/helpers/site_branding.html" %}
{% endblock %}

{% block breadcrumbs %}{% if not is_popup %}
    <div class="px-4 lg:px-8">
        <div class="container mb-6 mx-auto -my-3 lg:mb-12">
            <ul class="flex flex-wrap">
                {% url 'admin:index' as link %}
                {% trans 'Home' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:goals_project_changelist' as link %}
                {% trans 'Projects' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}
                
                {% url 'admin:custom_view' as link %}
                {% trans 'Projects Overview' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% trans 'Project Details' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with name=name %}
            </ul>
        <!-- Back Button -->
        <div class="relative mt-3 ">
            <a href="{% if request.GET.targets %}?project_id={{ project.id }}{% else %}{% url 'admin:custom_view' %}{% endif %}" 
               class="p-1 rounded-full bg-purple-900 text-purple-500 absolute -top-2 left-0 w-10 h-10 flex items-center justify-center bg-gray-800 hover:bg-gray-700 text-white rounded-full transition-colors shadow-md" 
               title="{% if request.GET.targets %}{% trans 'Back to Project' %}{% else %}{% trans 'Back to Projects' %}{% endif %}"
               onclick="{% if request.GET.targets %}window.location.href='?project_id={{ project.id }}'; return false;{% else %}window.location.href='{% url 'admin:custom_view' %}'; return false;{% endif %}">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
            </a>
        </div>

        </div>        
    </div>

{% endif %}{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we're on the custom URL path
            if (window.location.pathname.includes('admin/goals/project/custom-url-path')) {
                // Find all tab items
                const tabItems = document.querySelectorAll('.unfold-nav-item');
                
                // Loop through them to find the "Custom page" tab
                tabItems.forEach(function(item) {
                    // Check if this is the Custom page tab
                    if (item.textContent.trim().includes('Custom page')) {
                        // Add active class
                        item.classList.add('active');
                        
                        // Find the link inside and add active class to it too
                        const link = item.querySelector('a');
                        if (link) {
                            link.classList.add('active');
                        }
                    } else {
                        // Remove active class from other tabs
                        item.classList.remove('active');
                        
                        // Find the link inside and remove active class
                        const link = item.querySelector('a');
                        if (link) {
                            link.classList.remove('active');
                        }
                    }
                });
            }
        });
    </script>
    <style>
        body {
            background-color: #1f2937; /* Dark background for the entire page */
        }
        
        .topic-card {
            transition: all 0.3s ease;
        }
        
        .topic-card:hover {
            transform: translateY(-5px);
        }
        
        .progress-circle {
            transition: all 0.5s ease-out;
        }
        
        .topic-card:hover .progress-circle {
            filter: drop-shadow(0 0 5px rgba(174, 100, 216, 0.5));
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .topic-grid-item {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
        }
        
        .topic-grid-item:nth-child(1) { animation-delay: 0s; }
        .topic-grid-item:nth-child(2) { animation-delay: 0.1s; }
        .topic-grid-item:nth-child(3) { animation-delay: 0.2s; }
        .topic-grid-item:nth-child(4) { animation-delay: 0.3s; }
        .topic-grid-item:nth-child(5) { animation-delay: 0.4s; }
        .topic-grid-item:nth-child(6) { animation-delay: 0.5s; }
        .topic-grid-item:nth-child(7) { animation-delay: 0.6s; }
        .topic-grid-item:nth-child(8) { animation-delay: 0.7s; }
        .topic-grid-item:nth-child(9) { animation-delay: 0.8s; }
        
        /* Modal Styles */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }
        
        .modal-backdrop.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-content {
            background-color: #1f2937;
            border-radius: 0.5rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            display: flex;
            flex-direction: column;
            transform: translateY(20px);
            transition: transform 0.3s ease;
            border: 1px solid #374151;
        }
        
        .modal-backdrop.active .modal-content {
            transform: translateY(0);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #374151;
        }
        
        .modal-body {
            padding: 1.5rem;
            overflow-y: auto;
            flex-grow: 1;
        }
        
        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #374151;
            display: flex;
            justify-content: flex-end;
        }
        
        .comment-item {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
            background-color: #2d3748;
            border: 1px solid #4b5563;
            transition: all 0.2s ease;
        }
        
        .comment-item:hover {
            transform: translateX(5px);
            border-color: #6366f1;
        }
        
        .comment-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .comment-date {
            font-size: 0.875rem;
            color: #9ca3af;
        }
        
        .comment-text {
            color: #e5e7eb;
            white-space: pre-line;
        }
        
        .no-comments {
            text-align: center;
            padding: 2rem;
            color: #9ca3af;
        }
        
        /* Toggle switch animations */
        .toggle-dot {
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .toggle-target-status:focus .toggle-dot {
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
        }
        
        .toggle-target-status:active .toggle-dot {
            transform: scale(0.9);
        }
        
        /* Status label animations */
        .status-label {
            transition: all 0.3s ease;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        
        .animate-bounce {
            animation: bounce 0.5s ease-in-out;
        }
        
        /* Toast notifications */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Add Target button animation */
        .add-target-btn {
            transition: all 0.3s ease;
        }
        
        .add-target-btn:hover svg {
            transform: rotate(90deg);
            transition: transform 0.3s ease;
        }
        
        /* Pulse animation for loading states */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .animate-pulse {
            animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        
        /* Ping animation for loading states */
        @keyframes ping {
            0% { transform: scale(1); opacity: 1; }
            75%, 100% { transform: scale(1.2); opacity: 0.5; }
        }
        
        .animate-ping {
            animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
        }
    </style>
{% endblock %}

{% block content %}
    {% include "unfold/helpers/messages.html" %}

    {% tab_list "goals" %}

    <div class="container mx-auto px-4 py-8">
        
        <!-- Project Header -->
        <div class="flex items-center justify-between mb-8 mt-10">
            <div class="flex items-center">
                {% if project.icon %}
                    <img src="{{ project.icon.url }}" alt="{{ project.name }}" class="w-16 h-16 rounded-full mr-4 object-cover">
                {% else %}
                    <div class="w-16 h-16 rounded-full mr-4 flex items-center justify-center bg-primary-700 text-white text-2xl font-bold">
                        {{ project.name|slice:":1" }}
                    </div>
                {% endif %}
                <div>
                    <h1 class="text-3xl font-bold text-white">{{ project.name }}</h1>
                    <p class="text-gray-400">{{ project.description }}</p>
                </div>
            </div>
            <div class="flex space-x-4">

                <button id="commentsButton" 
                    class="px-4 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-md transition-colors flex items-center group relative">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                    </svg>
                    <span>{% trans "Comments" %}</span>
                    {% with comment_count=comments|length %}
                        <span class="ml-1 bg-indigo-700 text-white text-xs font-medium px-2.3/ py-0.5 rounded-full flex items-center justify-center min-w-[24px]">
                            {{ comment_count }}
                        </span>
                        <!-- Tooltip -->
                        <div class="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white text-xs rounded py-1 px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
                            {{ comment_count }} {% if comment_count == 1 %}{% trans "comment" %}{% else %}{% trans "comments" %}{% endif %}
                        </div>
                    {% endwith %}
                </button>
                <a href="{% url 'admin:goals_project_change' project.pk %}" 
                    class="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-md transition-colors">
                {% trans "Edit Project" %}
                </a>
            </div>
        </div>
        
        <!-- Project Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- Total Employees -->
            <div class="bg-gray-800 rounded-lg shadow p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-900 text-primary-500">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                    <div class="mx-4">
                        <h4 class="text-2xl font-semibold text-gray-200">{{ total_employees }}</h4>
                        <div class="text-gray-400">{% trans "Employees" %}</div>
                    </div>
                </div>
            </div>
            
            <!-- Total Tasks -->
            <div class="bg-gray-800 rounded-lg shadow p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-900 text-blue-500">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="mx-4">
                        <h4 class="text-2xl font-semibold text-gray-200">{{ total_targets }}</h4>
                        <div class="text-gray-400">{% trans "Total Targets" %}</div>
                    </div>
                </div>
            </div>
            
            <!-- Completed Tasks -->
            <div class="bg-gray-800 rounded-lg shadow p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-900 text-green-500">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="mx-4">
                        <h4 class="text-2xl font-semibold text-gray-200">{{ completed_targets }}</h4>
                        <div class="text-gray-400">{% trans "Completed Targets" %}</div>
                    </div>
                </div>
            </div>
            
            <!-- Project Progress -->
            <div class="bg-gray-800 rounded-lg shadow p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-900 text-purple-500">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="mx-4">
                        <h4 class="text-2xl font-semibold text-gray-200 project-progress-text">
                            {% if request.GET.targets %}
                                {% with selected_topic_id=request.GET.targets|add:"0" %}
                                    {% for topic in topics %}
                                        {% if topic.pk == selected_topic_id %}
                                            {{ topic.progress|floatformat:1 }}
                                        {% endif %}
                                    {% endfor %}
                                {% endwith %}
                            {% else %}
                                {{ project_progress|floatformat:1 }}
                            {% endif %}%
                        </h4>
                        <div class="text-gray-400">
                            {% trans "Progress" %}
                        </div>
                    </div>
                </div>
                <div class="mt-4 w-full bg-gray-700 rounded-full h-2.5">
                    <div class="bg-primary-500 h-2.5 rounded-full project-progress-bar" style="width: {% if request.GET.targets %}{% with selected_topic_id=request.GET.targets|add:'0' %}{% for topic in topics %}{% if topic.pk == selected_topic_id %}{{ topic.progress }}{% endif %}{% endfor %}{% endwith %}{% else %}{{ project_progress }}{% endif %}%"></div>
                </div>
            </div>
        </div>
        
        <!-- Project Topics -->
        {% if request.GET.targets %}
            {% with selected_topic_id=request.GET.targets|add:"0" %}
                {% for topic in topics %}
                    {% if topic.pk == selected_topic_id %}
                        <div class="flex items-center justify-between mb-6">
                            <h2 class="text-xl font-bold text-white">{% trans "Targets for" %}: {{ topic.name }}</h2>
                            <div class="flex space-x-3">
                                <a href="{% url 'admin:goals_target_add' %}?topic_project={{ topic_project.id }}" 
                                   class="add-target-btn px-4 py-2 bg-purple-600 hover:bg-purple-700 text-purple-500 text-sm rounded transition-colors flex items-center shadow-md hover:shadow-lg transform hover:-translate-y-1">
                                    <svg class="w-4 h-4 mr-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    {% trans "Add Target" %}
                                </a>
                
                            </div>
                        </div>
                        
                        <div class="bg-gray-800 rounded-lg shadow-lg border border-gray-700 overflow-hidden">
                            <div class="p-4 bg-gray-900 border-b border-gray-700 flex justify-between items-center">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full mr-3" style="background-color: {{ topic.color_code|default:'#6366f1' }}"></div>
                                    <h3 class="text-lg font-bold text-white">{{ topic.name }}</h3>
                                </div>
                                <div class="text-sm text-gray-400">
                                    <span class="topic-progress">{{ topic.progress|floatformat:0 }}</span>% {% trans "Complete" %}
                                    {% with topic_project=topic.topic_projects.all|filter_by_project:project %}
                                        {% if topic_project.targets.all.count == 1 and topic_project.targets.all.0.is_completed %}
                                            (100%)
                                        {% endif %}
                                    {% endwith %}
                                </div>
                            </div>
                            
                            <div class="p-4">
                                {% with topic_project=topic.topic_projects.all|filter_by_project:project %}
                                    {% if topic_project.targets.all %}
                                        <table class="w-full text-left">
                                            <thead class="text-xs text-gray-400 uppercase bg-gray-700">
                                                <tr>
                                                    <th class="px-4 py-3 rounded-tl-lg">{% trans "Name" %}</th>
                                                    <th class="px-4 py-3">{% trans "Description" %}</th>
                                                    <th class="px-4 py-3">{% trans "Due Date" %}</th>
                                                    <th class="px-4 py-3 rounded-tr-lg text-center">{% trans "Status" %}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for target in topic_project.targets.all %}
                                                    <tr class="border-b border-gray-700 hover:bg-gray-700 transition-colors">
                                                        <td class="px-4 py-3 font-medium text-white">{{ target.name }}</td>
                                                        <td class="px-4 py-3 text-gray-300">{{ target.description|default:"-"|truncatechars:50 }}</td>
                                                        <td class="px-4 py-3 text-gray-300">
                                                            {% if target.due_date %}
                                                                {{ target.due_date|date:"M d, Y" }}
                                                            {% else %}
                                                                -
                                                            {% endif %}
                                                        </td>
                                                        <td class="px-4 py-3">
                                                            <div class="flex justify-center items-center">
                                                                <button 
                                                                    data-target-id="{{ target.id }}" 
                                                                    class="toggle-target-status w-8 h-8 rounded-full transition-all duration-300 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-md flex items-center justify-center mx-auto
                                                                    {% if target.is_completed %}
                                                                        bg-green-500 hover:bg-green-600
                                                                    {% else %}
                                                                        bg-gray-100 hover:bg-gray-200 border-2 border-gray-300
                                                                    {% endif %}"
                                                                    title="{% if target.is_completed %}{% trans 'Mark as not done' %}{% else %}{% trans 'Mark as done' %}{% endif %}">
                                                                    <span class="sr-only">{% if target.is_completed %}{% trans 'Mark as not done' %}{% else %}{% trans 'Mark as done' %}{% endif %}</span>
                                                                    {% if target.is_completed %}
                                                                        <svg class="w-5 h-5 text-white toggle-dot" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                                        </svg>
                                                                    {% else %}
                                                                        <span class="toggle-dot w-4 h-4 rounded-full"></span>
                                                                    {% endif %}
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    {% else %}
                                        <div class="text-center py-8 text-gray-400">
                                            {% trans "No targets found for this topic." %}
                                            <div class="mt-4">
                                                <a href="{% url 'admin:goals_target_add' %}?topic={{ topic.pk }}" 
                                                   class="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white text-sm rounded transition-colors">
                                                    {% trans "Add Target" %}
                                                </a>
                                            </div>
                                        </div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            {% endwith %}
        {% else %}
            <h2 class="text-xl font-bold mb-6 text-white">{% trans "Topics" %}</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for topic in topics %}
                    <div class="topic-grid-item bg-gray-800 rounded-lg shadow-md overflow-hidden transition-all hover:shadow-lg border border-gray-700 topic-card">
                        <div class="p-6 flex flex-col h-full">
                        <!-- Header -->
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 rounded-full mr-3" style="background-color: {{ topic.color_code }}"></div>
                            <h3 class="text-lg font-bold text-white">{{ topic.name }}</h3>
                        </div>
                        
                        <!-- Description -->
                        <div class="mb-4 flex-grow-0">
                            <p class="text-gray-400 text-sm line-clamp-2">{{ topic.description|default:"No description" }}</p>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="mb-2 flex justify-between items-center">
                            <span class="text-sm text-gray-400">{% trans "Progress" %}</span>
                            <span class="text-sm font-medium text-gray-300">{{ topic.progress|floatformat:0 }}%</span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-2.5 mb-4">
                            <div class="h-2.5 rounded-full" style="width: {{ topic.progress }}%; background-color: {{ topic.color_code }}"></div>
                        </div>
                        
                        <!-- Task Count -->
                        <div class="text-sm text-gray-400 mb-4">
                            {% with target_count=topic.targets.all|length %}
                                {{ target_count }} {% trans "targets" %}
                            {% endwith %}
                        </div>
                        
                        <!-- Spacer to push button to bottom -->
                        <div class="flex-grow"></div>
                        
                        <!-- Actions -->
                        <div class="flex justify-center mt-2">
                            <a href="?project_id={{ project.id }}&targets={{ topic.pk }}" 
                               class="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white text-sm rounded transition-colors w-20 text-center">
                                {% trans "Targets" %}
                            </a>
                        </div>
                    </div>
                </div>
            {% empty %}
                <div class="col-span-full text-center py-12">
                    <p class="text-gray-400 text-lg">
                        {% trans "No topics found for this project. Create your first topic to get started." %}
                    </p>
                    <a href="{% url 'admin:goals_topic_add' %}?project={{ project.pk }}" 
                       class="mt-4 inline-block px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-md transition-colors">
                        {% trans "Create Topic" %}
                    </a>
                </div>
            {% endfor %}
        </div>
        {% endif %}
        
        <!-- Comments Modal -->
        <div id="commentsModal" class="modal-backdrop">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="text-xl font-bold text-white">{% trans "Project Comments" %}</h3>
                    <button id="closeCommentsModal" class="text-gray-400 hover:text-white">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="modal-body">
                    {% if comments %}
                        {% for comment in comments %}
                            <div class="comment-item">
                                <div class="comment-header">
                                    <div class="comment-date">{{ comment.created_at|date:"F j, Y, g:i a" }}</div>
                                </div>
                                <div class="comment-text">{{ comment.text }}</div>
                            </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-comments">
                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"></path>
                            </svg>
                            <p>{% trans "No comments yet. Add the first comment!" %}</p>
                        </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <form method="post" class="w-full">
                        {% csrf_token %}
                        <div class="flex flex-col space-y-3">
                            {{ comment_form.project }}
                            <div class="w-full">
                                <div class="mb-2 text-gray-300 font-medium">{% trans "Add your comment" %}</div>
                                {{ comment_form.text }}
                                {% if comment_form.text.errors %}
                                    <div class="text-red-500 text-sm mt-1">
                                        {{ comment_form.text.errors }}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" 
                                        class="px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-md transition-colors">
                                    {% trans "Add Comment" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- JavaScript for Modal -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const commentsButton = document.getElementById('commentsButton');
                const commentsModal = document.getElementById('commentsModal');
                const closeCommentsModal = document.getElementById('closeCommentsModal');
                const commentForm = document.getElementById('commentForm');
                
                // Function to add query parameter to URL
                function addQueryParam(name, value) {
                    const url = new URL(window.location.href);
                    url.searchParams.set(name, value);
                    window.history.pushState({}, '', url);
                }
                
                // Function to remove query parameter from URL
                function removeQueryParam(name) {
                    const url = new URL(window.location.href);
                    url.searchParams.delete(name);
                    window.history.pushState({}, '', url);
                }
                
                // Check if modal should be open on page load (from URL parameter)
                function checkModalParam() {
                    const urlParams = new URLSearchParams(window.location.search);
                    if (urlParams.get('modal') === 'true') {
                        openModal();
                    }
                }
                
                // Function to open modal
                function openModal() {
                    commentsModal.classList.add('active');
                    document.body.style.overflow = 'hidden'; // Prevent scrolling behind modal
                    addQueryParam('modal', 'true');
                    
                    // Scroll to the bottom of the modal body after a short delay to ensure modal is rendered
                    setTimeout(() => {
                        const modalBody = commentsModal.querySelector('.modal-body');
                        if (modalBody) {
                            modalBody.scrollTop = modalBody.scrollHeight;
                        }
                    }, 100);
                }
                
                // Function to close modal
                function closeModal() {
                    commentsModal.classList.remove('active');
                    document.body.style.overflow = ''; // Restore scrolling
                    removeQueryParam('modal');
                }
                
                // Open modal when comments button is clicked
                commentsButton.addEventListener('click', function() {
                    openModal();
                });
                
                // Close modal when close button is clicked
                closeCommentsModal.addEventListener('click', function() {
                    closeModal();
                });
                
                // Close modal when clicking outside the modal content
                commentsModal.addEventListener('click', function(event) {
                    if (event.target === commentsModal) {
                        closeModal();
                    }
                });
                
                // Prevent closing when clicking inside the modal content
                const modalContent = commentsModal.querySelector('.modal-content');
                modalContent.addEventListener('click', function(event) {
                    event.stopPropagation();
                });
                
                // Close modal with Escape key
                document.addEventListener('keydown', function(event) {
                    if (event.key === 'Escape' && commentsModal.classList.contains('active')) {
                        closeModal();
                    }
                });
                
                // The form now submits directly to the server using the standard form submission
                // No need for JavaScript form handling
                
                // Check modal parameter on page load
                checkModalParam();
                
                // Target status toggle functionality
                const toggleButtons = document.querySelectorAll('.toggle-target-status');
                toggleButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        const targetId = this.getAttribute('data-target-id');
                        const toggleDot = this.querySelector('.toggle-dot');
                        
                        // Create form data
                        const formData = new FormData();
                        formData.append('target_id', targetId);
                        
                        // Show loading state with animation
                        this.classList.add('animate-pulse');
                        if (toggleDot) {
                            toggleDot.classList.add('animate-ping');
                        }
                        
                        // Send AJAX request
                        fetch('/api/targets/update-status/', {
                            method: 'POST',
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Reset loading state
                                this.classList.remove('animate-pulse');
                                
                                // Update toggle appearance with animation
                                if (data.is_completed) {
                                    // Change to completed state
                                    this.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'border-2', 'border-gray-300');
                                    this.classList.add('bg-green-500', 'hover:bg-green-600');
                                    this.title = '{% trans "Mark as not done" %}';
                                    
                                    // Replace empty circle with checkmark
                                    this.innerHTML = `
                                        <span class="sr-only">{% trans "Mark as not done" %}</span>
                                        <svg class="w-5 h-5 text-white toggle-dot" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    `;
                                } else {
                                    // Change to not completed state
                                    this.classList.remove('bg-green-500', 'hover:bg-green-600');
                                    this.classList.add('bg-gray-100', 'hover:bg-gray-200', 'border-2', 'border-gray-300');
                                    this.title = '{% trans "Mark as done" %}';
                                    
                                    // Replace checkmark with empty circle
                                    this.innerHTML = `
                                        <span class="sr-only">{% trans "Mark as done" %}</span>
                                        <span class="toggle-dot w-4 h-4 rounded-full"></span>
                                    `;
                                }
                                
                                // Update topic progress with animation
                                const topicProgressElement = document.querySelector('.topic-progress');
                                if (topicProgressElement) {
                                    // Check if there's only one task
                                    const isSingleTask = data.total_targets === 1;
                                    
                                    // If there's only one task and it's completed, show 100%
                                    let newProgress;
                                    if (isSingleTask && data.is_completed) {
                                        newProgress = 100;
                                    } else {
                                        newProgress = Math.round(data.topic_progress);
                                    }
                                    
                                    // Animate the progress change
                                    const currentProgress = parseInt(topicProgressElement.textContent);
                                    
                                    // Simple animation for progress change
                                    let step = currentProgress < newProgress ? 1 : -1;
                                    let current = currentProgress;
                                    
                                    const progressInterval = setInterval(() => {
                                        if (current === newProgress) {
                                            clearInterval(progressInterval);
                                            topicProgressElement.textContent = newProgress;
                                            return;
                                        }
                                        current += step;
                                        topicProgressElement.textContent = current;
                                    }, 20);
                                }
                                
                                // Update project/topic progress with animation
                                const projectProgressBar = document.querySelector('.project-progress-bar');
                                const projectProgressText = document.querySelector('.project-progress-text');
                                
                                if (projectProgressBar && projectProgressText) {
                                    // Check if there's only one task
                                    const isSingleTask = data.total_targets === 1;
                                    
                                    // If there's only one task and it's completed, show 100%
                                    let progressValue;
                                    if (isSingleTask && data.is_completed) {
                                        progressValue = 100;
                                    } else {
                                        progressValue = data.topic_progress;
                                    }
                                    
                                    // Animate width change
                                    projectProgressBar.style.transition = 'width 1s ease-in-out';
                                    projectProgressBar.style.width = progressValue + '%';
                                    
                                    // Animate text change
                                    projectProgressText.textContent = progressValue.toFixed(1) + '%';
                                    projectProgressText.classList.add('text-primary-400');
                                    setTimeout(() => {
                                        projectProgressText.classList.remove('text-primary-400');
                                        // Reload the page after UI updates are complete
                                        window.location.reload();
                                    }, 1000);
                                } else {
                                    // If no progress elements to animate, reload immediately
                                    window.location.reload();
                                }
                            } else {
                                // Reset loading state
                                this.classList.remove('animate-pulse');
                                if (toggleDot) {
                                    toggleDot.classList.remove('animate-ping');
                                }
                                
                                // Create and show toast notification instead of alert
                                const toast = document.createElement('div');
                                toast.className = 'fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50';
                                toast.style.animation = 'fadeIn 0.3s ease-out forwards';
                                toast.textContent = 'Error: ' + (data.error || 'Failed to update target status');
                                document.body.appendChild(toast);
                                
                                setTimeout(() => {
                                    toast.style.animation = 'fadeIn 0.3s ease-out reverse forwards';
                                    setTimeout(() => {
                                        document.body.removeChild(toast);
                                    }, 300);
                                }, 3000);
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            
                            // Reset loading state
                            this.classList.remove('animate-pulse');
                            if (toggleDot) {
                                toggleDot.classList.remove('animate-ping');
                            }
                            
                            // Create and show toast notification
                            const toast = document.createElement('div');
                            toast.className = 'fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50';
                            toast.style.animation = 'fadeIn 0.3s ease-out forwards';
                            toast.textContent = 'An error occurred. Please try again.';
                            document.body.appendChild(toast);
                            
                            setTimeout(() => {
                                toast.style.animation = 'fadeIn 0.3s ease-out reverse forwards';
                                setTimeout(() => {
                                    document.body.removeChild(toast);
                                }, 300);
                            }, 3000);
                        });
                    });
                });
                
                // No need for getCookie function since we're using csrf_exempt
            });
        </script>
    </div>
{% endblock %}