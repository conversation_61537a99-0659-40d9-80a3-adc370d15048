{% extends "admin/base.html" %}

{% load admin_urls i18n unfold static goals_extras %}

{% block title %}
    {% trans 'Projects Overview' %} | {{ site_title|default:_('Django site admin') }}
{% endblock %}

{% block branding %}
    {% include "unfold/helpers/site_branding.html" %}
{% endblock %}

{% block breadcrumbs %}{% if not is_popup %}
    <div class="px-4 lg:px-8">
        <div class="container mb-6 mx-auto -my-3 lg:mb-12">
            <ul class="flex flex-wrap">
                {% url 'admin:index' as link %}
                {% trans 'Home' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% url 'admin:goals_project_changelist' as link %}
                {% trans 'Projects' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with link=link name=name %}

                {% trans 'Projects Overview' as name %}
                {% include 'unfold/helpers/breadcrumb_item.html' with name=name %}
            </ul>
        </div>
    </div>
{% endif %}{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we're on the custom URL path
            if (window.location.pathname.includes('admin/goals/project/custom-url-path')) {
                // Find all tab items
                const tabItems = document.querySelectorAll('.unfold-nav-item');
                
                // Loop through them to find the "Custom page" tab
                tabItems.forEach(function(item) {
                    // Check if this is the Custom page tab
                    if (item.textContent.trim().includes('Custom page')) {
                        // Add active class
                        item.classList.add('active');
                        
                        // Find the link inside and add active class to it too
                        const link = item.querySelector('a');
                        if (link) {
                            link.classList.add('active');
                        }
                    } else {
                        // Remove active class from other tabs
                        item.classList.remove('active');
                        
                        // Find the link inside and remove active class
                        const link = item.querySelector('a');
                        if (link) {
                            link.classList.remove('active');
                        }
                    }
                });
            }
        });
    </script>
    <style>
        body {
            background-color: #1f2937; /* Dark background for the entire page */
        }
        
        .project-card {
            transition: all 0.3s ease;
        }
        
        .project-card:hover {
            transform: translateY(-5px);
        }
        
        .progress-circle {
            transition: all 0.5s ease-out;
        }
        
        .project-card:hover .progress-circle {
            filter: drop-shadow(0 0 5px rgba(174, 100, 216, 0.5));
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .project-grid-item {
            animation: fadeIn 0.5s ease-out forwards;
            opacity: 0;
        }
        
        .project-grid-item:nth-child(1) { animation-delay: 0s; }
        .project-grid-item:nth-child(2) { animation-delay: 0.1s; }
        .project-grid-item:nth-child(3) { animation-delay: 0.2s; }
        .project-grid-item:nth-child(4) { animation-delay: 0.3s; }
        .project-grid-item:nth-child(5) { animation-delay: 0.4s; }
        .project-grid-item:nth-child(6) { animation-delay: 0.5s; }
        .project-grid-item:nth-child(7) { animation-delay: 0.6s; }
        .project-grid-item:nth-child(8) { animation-delay: 0.7s; }
        .project-grid-item:nth-child(9) { animation-delay: 0.8s; }
    </style>
{% endblock %}

{% block content %}
    {% include "unfold/helpers/messages.html" %}
    {% tab_list "goals" %}
    
    <div class="container mx-auto px-4 py-8">
        {% comment %} <h1 class="text-2xl font-bold mb-6 text-white">{% trans "Projects Overview" %}</h1>
        
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- Total Projects -->
            <div class="bg-gray-800 rounded-lg shadow p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-primary-900 text-primary-500">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                        </svg>
                    </div>
                    <div class="mx-4">
                        <h4 class="text-2xl font-semibold text-gray-200">{{ total_projects }}</h4>
                        <div class="text-gray-400">{% trans "Total Projects" %}</div>
                    </div>
                </div>
            </div>
            
            <!-- Total Tasks -->
            <div class="bg-gray-800 rounded-lg shadow p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-900 text-blue-500">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="mx-4">
                        <h4 class="text-2xl font-semibold text-gray-200">{{ total_tasks }}</h4>
                        <div class="text-gray-400">{% trans "Total Tasks" %}</div>
                    </div>
                </div>
            </div>
            
            <!-- Completed Tasks -->
            <div class="bg-gray-800 rounded-lg shadow p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-900 text-green-500">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <div class="mx-4">
                        <h4 class="text-2xl font-semibold text-gray-200">{{ completed_tasks }}</h4>
                        <div class="text-gray-400">{% trans "Completed Tasks" %}</div>
                    </div>
                </div>
            </div>
            
            <!-- Overall Progress -->
            <div class="bg-gray-800 rounded-lg shadow p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-900 text-purple-500">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="mx-4">
                        <h4 class="text-2xl font-semibold text-gray-200">{{ overall_progress|floatformat:1 }}%</h4>
                        <div class="text-gray-400">{% trans "Overall Progress" %}</div>
                    </div>
                </div>
                <div class="mt-4 w-full bg-gray-700 rounded-full h-2.5">
                    <div class="bg-primary-500 h-2.5 rounded-full" style="width: {{ overall_progress }}%"></div>
                </div>
            </div>
        </div>
         {% endcomment %}
        <h2 class="text-xl font-bold mb-6 text-white">{% trans "All Projects" %}</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for project in projects %}
                <div class="project-grid-item bg-gray-800 rounded-lg shadow-md overflow-hidden transition-all hover:shadow-lg border border-gray-700 project-card">
                    <div class="p-6 flex flex-col items-center">
                        <!-- Progress Circle -->
                        <div class="mb-4 relative progress-circle">
                            <svg class="w-32 h-32" viewBox="0 0 100 100">
                                <!-- Background Circle -->
                                <circle 
                                    cx="50" cy="50" r="40" 
                                    fill="none" 
                                    stroke="currentColor" 
                                    class="text-gray-700" 
                                    stroke-width="8"
                                />
                                
                                <!-- Progress Circle -->
                                {% with progress=project.progress|default:0 %}
                                {% comment %}
                                    The dasharray value (251.2) is the circumference of the circle (2 * π * r)
                                    where r=40 (the radius of our circle)
                                    2 * 3.14159 * 40 = 251.2
                                {% endcomment %}
                                {% with dasharray=251.2 %}
                                <circle 
                                    cx="50" cy="50" r="40" 
                                    fill="none" 
                                    stroke="#8b5cf6" 
                                    stroke-width="8"
                                    stroke-linecap="round"
                                    stroke-dasharray="{{ dasharray }}"
                                    {% comment %}
                                        The stroke-dashoffset formula calculates how much of the circle to show:
                                        - When progress is 0%, offset = dasharray (circle is empty)
                                        - When progress is 100%, offset = 0 (circle is full)
                                        - For any progress value in between, we calculate proportionally
                                          dasharray + (dasharray * progress / -100)
                                    {% endcomment %}
                                    stroke-dashoffset="{{ dasharray|multiply:progress|divide:-100|add:dasharray }}"
                                    transform="rotate(-90 50 50)"
                                />
                                {% endwith %}
                                {% endwith %}
                                
                                <!-- Percentage Text -->
                                <text 
                                    x="50" y="50" 
                                    text-anchor="middle" 
                                    dy="7" 
                                    font-size="20"
                                    font-weight="bold"
                                    fill="currentColor"
                                    class="text-gray-300"
                                >
                                    {{ project.progress|floatformat:0 }}%
                                </text>
                            </svg>
                        </div>
                        
                        <!-- Project Name -->
                        <h2 class="text-xl font-bold text-center mb-2 text-white">
                            {{ project.name }}
                        </h2>
                        
                        <!-- Target Count -->
                        <div class="text-sm text-gray-400 mb-4">
                            {% with topic_projects=project.topic_projects.all %}
                                {% with targets_count=0 %}
                                    {% for tp in topic_projects %}
                                        {% with tp_targets_count=tp.targets.count %}
                                            {% with targets_count=targets_count|add:tp_targets_count %}
                                                {% if forloop.last %}
                                                    {{ targets_count|floatformat:"0" }} {% trans "targets" %}
                                                {% endif %}
                                            {% endwith %}
                                        {% endwith %}
                                    {% empty %}
                                        0 {% trans "targets" %}
                                    {% endfor %}
                                {% endwith %}
                            {% endwith %}
                        </div>
                        
                        <!-- View Project Button -->
                        <a href="{% url 'admin:custom_view' %}?project_id={{ project.id }}" 
                           class="mt-2 px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-md transition-colors">
                            {% trans "View Project" %}
                        </a>
                    </div>
                </div>
            {% empty %}
                <div class="col-span-full text-center py-12">
                    <p class="text-gray-400 text-lg">
                        {% trans "No projects found. Create your first project to get started." %}
                    </p>
                    <a href="{% url 'admin:goals_project_add' %}" 
                       class="mt-4 inline-block px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-md transition-colors">
                        {% trans "Create Project" %}
                    </a>
                </div>
            {% endfor %}
        </div>
    </div>
{% endblock %}
