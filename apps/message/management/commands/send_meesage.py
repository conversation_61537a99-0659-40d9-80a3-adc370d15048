
from apps.message.models import Message
from apps.employee.models import Employee
from apps.meet.views import send_notification

from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = "Create a new message between two employees"

    def handle(self, *args, **kwargs):
        r = Employee.objects.get(id=31)
        s = Employee.objects.get(id=35)
        Message.objects.create(sender=s, receiver=r, content="مرسی")