from django.db import models

from apps.employee.models import Employee


class Message(models.Model):
    sender = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='sent_messages', verbose_name='from_employee')
    receiver = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='received_messages', verbose_name='to_employee')
    content = models.TextField(verbose_name='content')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='created_at')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='updated_at')
    status = models.BooleanField(default=True)
    is_read = models.BooleanField(default=False, verbose_name='is_read')  # فیلد جدید
     
    def __str__(self):
        return f"Message from {self.sender} to {self.receiver}"
    
    
    
class Task(models.Model):
    created_by = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='created_tasks', verbose_name='created_by')
    assigned_to = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='assigned_tasks', verbose_name='assigned_to')
    content = models.TextField(verbose_name='content')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='created_at')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='updated_at')
    is_completed = models.BooleanField(default=False, verbose_name='is_completed')
    
    def __str__(self):
        return f"Task for {self.assigned_to} by {self.created_by} status: {is_completed}"