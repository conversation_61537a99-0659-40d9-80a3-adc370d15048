
from rest_framework import serializers
from .models import Message



class MessageSerializer(serializers.ModelSerializer):
    sender_status = serializers.SerializerMethodField()

    class Meta:
        model = Message
        # fields = ['id', 'content', 'created_at', 'sender_status']
        fields = ['id', 'sender', 'receiver', 'content', 'created_at','sender_status', 'updated_at']
        read_only_fields = ['sender', 'created_at', 'updated_at']

    def get_sender_status(self, obj):
        employee = self.context.get('employee')
        return "current" if obj.sender == employee else "other"