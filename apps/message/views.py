import time

from django.shortcuts import render
from django.db import models, transaction
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status

from apps.message.models import Message
from apps.message.serializers import MessageSerializer
from apps.employee.models import Employee

from apps.meet.views import send_notification



class MessageHistoryAPIView(APIView):

    def get(self, request, other_user_id, *args, **kwargs):
        request_token = self.request.headers.get('X-Api-Key')
        employee = Employee.objects.filter(token=request_token).first()
        if not employee:
            return Response({"message": "not employee"}, status=status.HTTP_404_NOT_FOUND)
        
        messages = Message.objects.filter(
            (models.Q(sender=employee, receiver_id=other_user_id) |
             models.Q(sender_id=other_user_id, receiver=employee))
        ).order_by('-created_at')
        messages.filter(receiver=employee, is_read=False).update(is_read=True)

        serializer = MessageSerializer(messages, many=True, context={'employee': employee})
        return Response(serializer.data)

class SendMessageAPIView(APIView):
    def post(self, request, *args, **kwargs):
        request_token = self.request.headers.get('X-Api-Key')
        employee = Employee.objects.filter(token=request_token).first()
        if not employee:
            return Response({"message": "not employee"}, status=status.HTTP_404_NOT_FOUND)

        serializer = MessageSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(sender=employee)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RequestScreenAPIView(APIView):
    def post(self, request, *args, **kwargs):
        employee = Employee.objects.filter(id=request.data['user_id']).first()
        if not employee:
            return Response({"message": "not employee"}, status=status.HTTP_404_NOT_FOUND)
        
        
        for token in employee.device_tokens.all():
            send_notification(token.token, request.data)

        # print(f'----------request---> {request.data}')
        return Response({"message": "ok"}, status=status.HTTP_200_OK)


class LongPollingAPIView(APIView):
    def get(self, request, other_user_id, *args, **kwargs):
        request_token = self.request.headers.get('X-Api-Key')
        employee_receiver = Employee.objects.get(token=request_token)
        employee_sender = Employee.objects.get(id=int(other_user_id))
        if not employee_receiver and not employee_sender:
            return Response({"message": "not employee"}, status=status.HTTP_404_NOT_FOUND)
        
        timeout = 30  # Maximum wait time in seconds
        poll_interval = 1  # Interval to check for new data
        start_time = time.time()


        while time.time() - start_time < timeout:
            last_message = Message.objects.filter(
                sender=employee_sender,
                receiver=employee_receiver,
                is_read=False  # پیام‌های خوانده نشده
            ).order_by('-created_at').first()
            if last_message:
                with transaction.atomic():
                    last_message.is_read = True
                    last_message.save(update_fields=['is_read'])
                # بازگرداندن آخرین پیام جدید
                return Response({
                        'id': last_message.id,
                        'content': last_message.content,
                        "sender_status": "other",
                        'created_at': last_message.created_at,
                    }, status=status.HTTP_200_OK)

            time.sleep(poll_interval)

        return Response({}, status=status.HTTP_204_NO_CONTENT)
    
