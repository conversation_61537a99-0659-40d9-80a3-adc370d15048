from django.contrib import admin

from ajaxdatatable.admin import AjaxDatatable
from .models import Message, Task


@admin.register(Message)
class MessageAdmin(AjaxDatatable):
    list_display = ('sender', 'receiver', 'content', 'created_at', 'updated_at', 'status')
    search_fields = ('sender__name', 'receiver__name', 'content')
    list_filter = ('status', 'created_at')
    readonly_fields = ('created_at', 'updated_at')
    

@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    list_display = ('created_by', 'assigned_to', 'content', 'created_at', 'updated_at', 'is_completed')
    search_fields = ('created_by__name', 'assigned_to__name', 'content')
    list_filter = ('is_completed', 'created_at')
    readonly_fields = ('created_at', 'updated_at')    
    

