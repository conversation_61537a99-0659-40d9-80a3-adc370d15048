from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Message
from apps.employee.models import Employee
from apps.meet.views import send_notification


@receiver(post_save, sender=Message)
def message_created_handler(sender, instance, created, **kwargs):
    if created:  
        receiver = instance.receiver
        sender = instance.sender
        
        data = {
            "notification_type": "alert_message",
            "receiver_id": receiver.id,
            "fullname": sender.full_name,  
            "avatar": sender.avatar.url if sender.avatar else None,  
            "content": instance.content,
        }        
        for token in receiver.device_tokens.all():
            send_notification(token.token, data)

            