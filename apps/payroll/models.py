import random
import string
from django.db import models
from django.db.models.signals import pre_save
from django.dispatch import receiver
from apps.employee.models import Employee
from datetime import datetime
import jdatetime
from decimal import Decimal
import re


class Constant(models.Model):
    title = models.CharField(max_length=100, unique=True)
    value = models.DecimalField(max_digits=10, decimal_places=2)
    slug = models.CharField(max_length=3, unique=True, blank=True)

    def generate_unique_slug(self):
        slug = ''.join(random.choices(string.ascii_uppercase, k=3))
        while Constant.objects.filter(slug=slug).exists():
            slug = ''.join(random.choices(string.ascii_uppercase, k=3))
        return slug

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = self.generate_unique_slug()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title} ({self.slug}): {self.value}"

    class Meta:
        verbose_name = "ثابت"
        verbose_name_plural = "ثابت‌ها"


class SalaryCalculationPattern(models.Model):
    title = models.CharField(max_length=100, unique=True)
    formula = models.TextField(help_text="Enter the formula using slugs of constants or other fields.")

    def __str__(self):
        return self.title

    def calculate_salary(self, context):
        """
        Evaluates the formula using values from the context dictionary.
        The context should map slugs or identifiers to actual values.
        """
        try:
            return eval(self.formula, {}, context)
        except Exception as e:
            print(f"Error calculating salary: {e}")
            return None

    class Meta:
        verbose_name = "الگوی محاسبه"
        verbose_name_plural = "الگوهای محاسبه"

class Payslip(models.Model):
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name="payslips")
    month = models.PositiveSmallIntegerField()
    year = models.PositiveSmallIntegerField()
    salary_amount = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True)
    calculation_details = models.TextField(blank=True, null=True)
    balance_adjustment = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    bonus = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    created_at = models.DateTimeField(auto_now_add=True)
    payment_status = models.BooleanField(default=False)  # False: پرداخت نشده, True: پرداخت شده


    class Meta:
        constraints = [
            models.UniqueConstraint(fields=['employee', 'month', 'year'], name='unique_payslip_per_employee_per_month')
        ]
        ordering = ['-year', '-month']
        verbose_name = "فیش حقوق"
        verbose_name_plural = "فیش های حقوق"

    def __str__(self):
        return f"فیش حقوقی {self.employee.full_name} برای {self.month}/{self.year} - مبلغ: {self.salary_amount}"

    def calculate_hours_worked_shamsi(self):
        start_date = jdatetime.date(self.year, self.month, 1).togregorian()
        end_date = jdatetime.date(self.year, self.month, jdatetime.j_days_in_month[self.month - 1]).togregorian()
        time_entries = self.employee.timeentry_set.filter(start_time__date__gte=start_date, start_time__date__lte=end_date,end_time__isnull=False)
        hours_worked = sum([(entry.end_time - entry.start_time).total_seconds() / 3600 for entry in time_entries])
        return Decimal(hours_worked)

    def calculate_salary(self):
        CALCULATION_VARIABLES = {
            "ct": {
                "description": "ساعات تعهد ماهانه",
                "default_value": Decimal("0.00"),
            },
            "cd": {
                "description": "ساعات تعهد روزانه",
                "default_value": Decimal("0.00"),
            },
            "tm": {
                "description": "مقدار ساعت انجام شده در ماه",
                "default_value": Decimal("0.00"),
            },
            "ms": {
                "description": "حقوق درج شده در قرارداد به ازای یکماه فعالیت",
                "default_value": Decimal("0.00"),
            },
            "hs": {
                "description": "حقوق درج شده در قرارداد به ازای یک ساعت فعالیت",
                "default_value": Decimal("0.00"),
            }
            # Other constants
        }

        if not self.employee.salary_pattern:
            return None

        # Gather constants and variables
        constants = {const.slug: Decimal(const.value) for const in Constant.objects.all()}
        hours_worked = self.calculate_hours_worked_shamsi()

        # Load calculation variables from configuration
        calculation_variables = {
            key: config["default_value"] for key, config in CALCULATION_VARIABLES.items()
        }
        calculation_variables["ct"] = Decimal(self.employee.commitment_time_per_month or 0)
        calculation_variables["cd"] = Decimal(self.employee.commitment_time_per_day or 0)
        calculation_variables["tm"] = hours_worked
        calculation_variables["ms"] = Decimal(self.employee.monthly_salary or 0)
        calculation_variables["hs"] = Decimal(self.employee.hourly_salary or 0)

        context = {**constants, **calculation_variables}

        # Identify used variables in the formula
        formula = self.employee.salary_pattern.formula
        used_variables = set(re.findall(r'\b[a-z]{2}\b', formula))  # Find two-letter variable slugs

        # Calculate base salary
        base_salary = self.employee.salary_pattern.calculate_salary(context)

        # Calculate final salary, including balance adjustment and bonus
        final_salary = base_salary + self.balance_adjustment + self.bonus

        # Generate calculation details HTML only for used variables
        calculation_details = "<strong>جدول محاسبه حقوق:</strong><br>"
        calculation_details += "<table style='width:100%; border: 1px solid #ddd; border-collapse: collapse;'>"
        calculation_details += "<tr><th style='border: 1px solid #ddd; padding: 8px;'>مقدار</th><th style='border: 1px solid #ddd; padding: 8px;'>توضیح</th></tr>"

        for key, value in context.items():
            if key in used_variables:
                description = CALCULATION_VARIABLES.get(key, {}).get("description", "بدون توضیح")
                calculation_details += f"<tr><td style='border: 1px solid #ddd; padding: 8px;'>{value}</td><td style='border: 1px solid #ddd; padding: 8px;'>{description}</td></tr>"

        # Add balance adjustment and bonus to calculation details
        calculation_details += f"<tr><td style='border: 1px solid #ddd; padding: 8px;'>{self.balance_adjustment}</td><td style='border: 1px solid #ddd; padding: 8px;'>مقدار تنظیمی برای بدهکاری یا بستانکاری</td></tr>"
        calculation_details += f"<tr><td style='border: 1px solid #ddd; padding: 8px;'>{self.bonus}</td><td style='border: 1px solid #ddd; padding: 8px;'>پاداش اضافه شده به حقوق</td></tr>"
        calculation_details += "</table>"
        calculation_details += f"<br><strong>حقوق نهایی:</strong> {final_salary:.2f} تومان"

        # Save calculation details in the field
        self.calculation_details = calculation_details
        return final_salary
    def save(self, *args, **kwargs):
        # بروزرسانی salary_amount و calculation_details قبل از ذخیره
        self.salary_amount = self.calculate_salary()
        super().save(*args, **kwargs)


class MonthlySalaryReport(models.Model):
    class Meta:
        verbose_name = "گزارش حقوق پرداختی ماهانه"
        verbose_name_plural = "گزارش حقوق پرداختی ماهانه"

    def __str__(self):
        return "گزارش حقوق پرداختی ماهانه"