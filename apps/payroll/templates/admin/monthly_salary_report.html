<h2 style="text-align: center; margin-bottom: 20px;">گزارش حقوق پرداختی ماهانه</h2>
<form method="get" style="text-align: center; margin-bottom: 20px;">
  {{ form.as_p }}
  <button type="submit" class="btn btn-primary">نمایش گزارش</button>
</form>

{% if payslips %}
  <h3 style="text-align: center;">گزارش حقوق پرداختی برای ماه {{ form.cleaned_data.month }} و سال {{ form.cleaned_data.year }}</h3>
  <table style="width: 100%; border: 1px solid #ddd; border-collapse: collapse; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <thead style="background-color: #f2f2f2;">
      <tr>
        <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">کارمند</th>
        <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">مبلغ حقوق</th>
        <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">شماره شبا</th>
        <th style="border: 1px solid #ddd; padding: 10px; text-align: left;">شماره موبایل</th>
      </tr>
    </thead>
    <tbody>
      {% for payslip in payslips %}
        <tr>
          <td style="border: 1px solid #ddd; padding: 10px;">{{ payslip.employee.full_name }}</td>
          <td style="border: 1px solid #ddd; padding: 10px;">{{ payslip.salary_amount }}</td>
          <td style="border: 1px solid #ddd; padding: 10px;">{{ payslip.employee.bank_sheba_number }}</td>
          <td style="border: 1px solid #ddd; padding: 10px;">{{ payslip.employee.phone_number }}</td>
        </tr>
      {% endfor %}
      <tr>
        <td style="border: 1px solid #ddd; padding: 10px; font-weight: bold;">جمع کل</td>
        <td style="border: 1px solid #ddd; padding: 10px; font-weight: bold;">{{ total_salary }}</td>
        <td colspan="2" style="border: 1px solid #ddd; padding: 10px;"></td> <!-- برای جمع کل خالی گذاشتن -->
      </tr>
    </tbody>
  </table>
{% endif %}

<style>
  body {
    font-family: Arial, sans-serif;
    background-color: #f9f9f9;
    color: #333;
    margin: 0;
    padding: 20px;
  }

  h2, h3 {
    color: #4A4A4A;
  }

  .btn-primary {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
  }

  .btn-primary:hover {
    background-color: #0056b3;
  }
</style>
