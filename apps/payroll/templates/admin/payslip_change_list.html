{% extends 'admin/change_list_ajax.html' %}
{% load admin_urls %}
{% load i18n %}

{% block layout-buttons %}
    {% include 'admin/list_navigate_tools.html' %}
    {% if has_add_permission %}
        <button type="button" class="float-right btn bg-indigo-400 legitRipple mr-3 ml-3 btn btn-light"
                data-toggle="modal" data-target="#create_payslip_modal">
            ایجاد انبوه فیش حقوقی برای تمامی کارمندان <i class="icon-plus3 ml-2"></i>
        </button>
    {% endif %}
{% endblock %}

{% block content %}
    {{ block.super }}


    <div id="create_payslip_modal" class="modal fade" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ایجاد فیش حقوقی ماهانه برای تمامی کارمندان</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal-body">
                    <label for="month">ماه</label>
                    <div class="col">
                                       <input type="number" class="form-control" id="month" name="month" required>
                    </div>

                    <label class="mt-2" for="year">سال</label>
                    <div class="col">
                        <input type="number" class="form-control" id="year" >
                    </div>
                </div>

                <div class="modal-footer mt-2">
                    <button type="button" class="btn btn-link" data-dismiss="modal">بستن</button>
                    <button type="button" class="btn btn-primary" onclick="createPayslips()">ایجاد انبوه فیش حقوقی</button>
                </div>
            </div>
        </div>
    </div>


{% endblock %}
{% block scripts %}
{{ block.super }}
<script>
    function createPayslips() {
        const month = document.getElementById('month').value;
        const year = document.getElementById('year').value;
        const submitButton = document.querySelector("#create_payslip_modal .btn-primary");

        // غیرفعال کردن دکمه
        submitButton.disabled = true;
        submitButton.textContent = "در حال ارسال...";

        fetch("", {
            method: "POST",
            headers: {
                "X-CSRFToken": "{{ csrf_token }}",
                "Content-Type": "application/json"
            },
            body: JSON.stringify({ month, year })
        })
        .then(response => response.json())
        .then(data => {
            // فعال کردن دکمه پس از دریافت پاسخ
            submitButton.disabled = false;
            submitButton.textContent = "ایجاد فیش حقوقی";

            if (data.success) {
                alert("فیش حقوقی برای تمامی کارمندان ایجاد شد");
                location.reload();
            } else {
                alert("خطا در ایجاد فیش حقوقی: " + (data.error || "مشکلی رخ داده است."));
            }
        })
        .catch(error => {
            console.error("Error:", error);
            alert("در ارسال درخواست مشکلی پیش آمده است. لطفا دوباره تلاش کنید.");

            // فعال کردن دکمه در صورت بروز خطا
            submitButton.disabled = false;
            submitButton.textContent = "ایجاد فیش حقوقی";
        });
    }
</script>
{% endblock %}

