import json

from django.contrib import admin
from django.http import HttpResponse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from ajaxdatatable.admin import AjaxDatatable
from .models import Constant, SalaryCalculationPattern, Payslip, MonthlySalaryReport
from django.urls import path
from .views import monthly_salary_report_view
from ..employee.models import Employee


class ConstantAdmin(AjaxDatatable):
    list_display = ('title', 'slug', 'value')
    search_fields = ('title', 'slug')
    list_filter = ('title',)
    ordering = ('slug',)
    list_display_links = None  # غیرفعال کردن لینک بر روی عنوان

    # اختیاری: اضافه کردن عنوان و توضیحات راهنما برای صفحه مدیریت ثابت‌ها
    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}
        extra_context['title'] = 'مدیریت ثابت‌ها'
        extra_context[
            'help_text'] = 'در این بخش می‌توانید ثابت‌های مورد استفاده در فرمول‌های محاسبه حقوق را مشاهده و مدیریت کنید.'
        return super().changelist_view(request, extra_context=extra_context)


admin.site.register(Constant, ConstantAdmin)


class SalaryCalculationPatternAdmin(AjaxDatatable):
    list_display = ('title', 'formula')
    search_fields = ('title', 'formula')
    ordering = ('title',)

    # راهنمای فرمول در صفحه تغییر برای استفاده از اسلاگ‌ها
    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)

        # دریافت اسلاگ‌های موجود از مدل Constant
        constants = Constant.objects.all()
        table_rows = "".join([f"<tr><td>{constant.slug}</td><td>{constant.title}</td></tr>" for constant in constants])

        # اضافه کردن متغیرهای `ct` و `tm` به جدول
        custom_variables = {
            "ct": "ساعات تعهد ماهانه",
            "cd": "ساعات تعهد روزانه",
            "tm": "مقدار ساعت انجام شده در ماه",
            "ms": "حقوق درج شده در قرارداد به ازای یکماه فعالیت",
            "hs": "حقوق درج شده در قرارداد به ازای یک ساعت فعالیت",
        }
        custom_rows = "".join(
            [f"<tr><td>{slug}</td><td>{description}</td></tr>" for slug, description in custom_variables.items()])

        # ترکیب ثابت‌ها و متغیرهای اضافی
        help_text = f"""
        <div style="margin-bottom: 10px;">
            <strong>راهنما:</strong> از اسلاگ‌های سه حرفی ثابت‌ها می‌توانید در فرمول استفاده کنید. اسلاگ‌های موجود به شرح زیر هستند:
            <table style="border-collapse: collapse; width: 100%; margin-top: 5px;">
                <thead>
                    <tr>
                        <th style="border: 1px solid #ddd; padding: 8px;">Slug</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">عنوان ثابت</th>
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                    {custom_rows}
                </tbody>
            </table>
        </div>
        """

        form.base_fields['formula'].help_text = mark_safe(help_text)
        return form


admin.site.register(SalaryCalculationPattern, SalaryCalculationPatternAdmin)

from django.contrib import messages
from django.shortcuts import redirect
from django.http import JsonResponse


class PayslipAdmin(AjaxDatatable):
    list_display = (
    'employee', 'month', 'year', 'salary_amount', 'created_at', 'payment_status', 'view_calculation_details_link')
    search_fields = ('employee__full_name', 'month', 'year')
    list_filter = ('year', 'month', 'employee')
    ordering = ('-year', '-month', 'employee')
    actions = ['mark_as_paid', 'mark_as_unpaid']
    change_list_template = 'admin/payslip_change_list.html'

    def changelist_view(self, request, extra_context=None):
        # بررسی اینکه آیا درخواست POST شامل داده‌های `month` و `year` است
        if request.method == "POST" and 'month' in request.body.decode('utf-8') and 'year' in request.body.decode('utf-8'):
            try:
                data = json.loads(request.body)
                month = int(data.get("month"))
                year = int(data.get("year"))

                if not month or not year:
                    return JsonResponse({"success": False, "error": "ماه و سال باید مشخص شوند."}, status=400)

                employees = Employee.objects.all()
                for employee in employees:
                    Payslip.objects.create(employee=employee, month=month, year=year, salary_amount=0.00)

                return JsonResponse({"success": True})
            except (json.JSONDecodeError, ValueError, TypeError) as e:
                return JsonResponse({"success": False, "error": "داده‌های نادرست ارسال شده‌اند."}, status=400)

        # اگر درخواست مربوط به ایجاد فیش حقوقی نبود، از `changelist_view` اصلی استفاده کنید
        return super().changelist_view(request, extra_context=extra_context)

    def mark_as_paid(self, request, queryset):
        queryset.update(payment_status=True)  # تغییر به پرداخت شده
        self.message_user(request, "وضعیت پرداخت به 'پرداخت شده' تغییر کرد.")

    def mark_as_unpaid(self, request, queryset):
        queryset.update(payment_status=False)  # تغییر به پرداخت نشده
        self.message_user(request, "وضعیت پرداخت به 'پرداخت نشده' تغییر کرد.")

    mark_as_paid.short_description = "تغییر وضعیت به پرداخت شده"
    mark_as_unpaid.short_description = "تغییر وضعیت به پرداخت نشده"

    def view_calculation_details_link(self, obj):
        if obj.calculation_details:
            return format_html('<a target="_blank" href="{}">مشاهده جزئیات محاسبه</a>',
                               f'/admin/payroll/payslip/{obj.id}/calculation-details/')
        return "-"

    view_calculation_details_link.short_description = "جزئیات محاسبه"

    # افزودن URL سفارشی برای نمایش جزئیات محاسبه
    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('<int:payslip_id>/calculation-details/', self.admin_site.admin_view(self.view_calculation_details),
                 name='payslip-calculation-details'),
        ]
        return custom_urls + urls

    def view_calculation_details(self, request, payslip_id):
        payslip = self.get_object(request, payslip_id)
        return HttpResponse(payslip.calculation_details)


admin.site.register(Payslip, PayslipAdmin)


class MonthlySalaryReportAdmin(admin.ModelAdmin):
    change_list_template = "admin/monthly_salary_report.html"  # استفاده از قالب سفارشی

    def changelist_view(self, request, extra_context=None):
        # هدایت به ویوی سفارشی برای نمایش گزارش حقوق
        return monthly_salary_report_view(request)


admin.site.register(MonthlySalaryReport, MonthlySalaryReportAdmin)
