# Generated by Django 3.2.17 on 2024-11-01 14:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payroll', '0003_auto_20241101_0737'),
    ]

    operations = [
        migrations.CreateModel(
            name='MonthlySalaryReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
            ],
            options={
                'verbose_name': 'گزارش حقوق پرداختی ماهانه',
                'verbose_name_plural': 'گزارش حقوق پرداختی ماهانه',
            },
        ),
        migrations.AlterModelOptions(
            name='constant',
            options={'verbose_name': 'ثابت', 'verbose_name_plural': 'ثابت\u200cها'},
        ),
        migrations.AlterModelOptions(
            name='payslip',
            options={'ordering': ['-year', '-month'], 'verbose_name': 'فیش حقوق', 'verbose_name_plural': 'فیش های حقوق'},
        ),
        migrations.AlterModelOptions(
            name='salarycalculationpattern',
            options={'verbose_name': 'الگوی محاسبه', 'verbose_name_plural': 'الگوهای محاسبه'},
        ),
        migrations.AddField(
            model_name='payslip',
            name='payment_status',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterUniqueTogether(
            name='payslip',
            unique_together=set(),
        ),
        migrations.AddConstraint(
            model_name='payslip',
            constraint=models.UniqueConstraint(fields=('employee', 'month', 'year'), name='unique_payslip_per_employee_per_month'),
        ),
    ]
