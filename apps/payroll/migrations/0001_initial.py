# Generated by Django 3.2.17 on 2024-11-01 06:35

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Constant',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('value', models.DecimalField(decimal_places=2, max_digits=10)),
                ('slug', models.Char<PERSON>ield(blank=True, max_length=3, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='SalaryCalculationPattern',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, unique=True)),
                ('formula', models.TextField(help_text='Enter the formula using slugs of constants or other fields.')),
            ],
        ),
    ]
