# Generated by Django 3.2.17 on 2024-11-01 07:37

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payroll', '0002_payslip'),
    ]

    operations = [
        migrations.AddField(
            model_name='payslip',
            name='balance_adjustment',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='payslip',
            name='bonus',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10),
        ),
        migrations.AddField(
            model_name='payslip',
            name='calculation_details',
            field=models.TextField(blank=True, null=True),
        ),
    ]
