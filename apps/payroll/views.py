# views.py
from django.shortcuts import render
from django import forms
from .models import Payslip
from django.db.models import Sum

from ..employee.models import Employee


class MonthlySalaryReportForm(forms.Form):
    month = forms.IntegerField(label="ماه", min_value=1, max_value=12)
    year = forms.IntegerField(label="سال", min_value=1300, max_value=1500)

def monthly_salary_report_view(request):
    form = MonthlySalaryReportForm(request.GET or None)
    payslips = []
    total_salary = 0

    if form.is_valid():
        month = form.cleaned_data['month']
        year = form.cleaned_data['year']
        payslips = Payslip.objects.filter(month=month, year=year)
        total_salary = payslips.aggregate(total=Sum('salary_amount'))['total'] or 0

    context = {
        'form': form,
        'payslips': payslips,
        'total_salary': total_salary,
    }
    return render(request, 'admin/monthly_salary_report.html', context)
