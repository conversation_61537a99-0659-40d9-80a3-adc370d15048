# from celery import shared_task

# from .fcm_notification import send_notification
# from .models import PushMessage


# @shared_task(ignore_result=True)
# def send_push_notifications(obj_id):
#     obj = PushMessage.objects.get(id=obj_id)
#     ids = obj.users.values_list('fcm', flat=True)
#     ids = list(ids)
#     response = send_notification(ids, title=obj.title, body=obj.message, url=obj.url)
#     PushMessage.objects.filter(id=obj_id).update(
#         result=response
#     )
