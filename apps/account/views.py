import datetime

from django.contrib.auth.models import update_last_login
from django.db import transaction
from django.http import JsonResponse
from rest_framework.authtoken.views import ObtainAuthToken
from rest_framework.authtoken.models import Token
from rest_framework.generics import CreateAP<PERSON>View, GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from django.conf import settings

from .models import User
# from .serializer import UserSerializer, GuestUserSerializer
from .centrifugo import centrifugo_service


# class AccountAuthToken(ObtainAuthToken):

#     def post(self, request, *args, **kwargs):
#         serializer = self.serializer_class(data=request.data,
#                                            context={'request': request})
#         serializer.is_valid(raise_exception=True)
#         user = serializer.validated_data['user']
#         token, created = Token.objects.get_or_create(user=user)
#         return Response({
#             'token': token.key,
#             'user_id': user.pk,
#             'email': user.email
#         })


# class SignupView(CreateAPIView):
#     serializer_class = UserSerializer

#     def create(self, request, *args, **kwargs):
#         serializer = self.get_serializer(data=request.data)
#         serializer.is_valid(raise_exception=True)
#         user = self.perform_create(serializer)
#         # headers = self.get_success_headers(serializer.data)

#         return Response({
#             'token': self.generate_login_token(user)
#         }, status=200)

#     def perform_create(self, serializer):
#         lat, lon = serializer.validated_data.pop('lat'), serializer.validated_data.pop('lon')
#         avatar_url = serializer.validated_data.pop('avatar', None)
#         avatar = self.save_avatar(avatar_url)
#         country, city = self.get_country_city_from_point(lat, lon)
#         timezone = serializer.validated_data.pop('timezone')
#         # create user
#         user_obj: User = serializer.save(
#             avatar=avatar,
#             country=country,
#             city=city,
#         )
#         # log user login
#         user_obj.login_history.create(
#             lat=lat,
#             lon=lon,
#             country=country,
#             city=city,
#             ip=self.get_client_ip(),
#             timezone=timezone,
#         )

#         return user_obj

#     @staticmethod
#     def get_country_city_from_point(lat, lon) -> tuple:
#         from geopy.geocoders import Nominatim
#         try:
#             geolocator = Nominatim(user_agent="geoapiExercises", timeout=5)
#             location = geolocator.reverse(f"{lat},{lon}")
#             country_code = location.raw['address'].get('country_code')
#             city = location.raw['address'].get('city') or location.raw['address'].get('state')
#             return country_code, city

#         except Exception as e:
#             import logging
#             logger = logging.getLogger(__name__)
#             logger.error(f"account/views.py:76 {e} lat: {lat} | lon: {lon}")
#             print(e)
#             return '', ''

#     @staticmethod
#     def save_avatar(url: str = None):
#         if not url:
#             return None

#         from django.core.files import File as DjangoFile
#         from filer.models.imagemodels import Image
#         from filer.models.foldermodels import Folder
#         import requests
#         from secrets import token_urlsafe
#         try:
#             name = token_urlsafe(5) + ".png"

#             with open(f"/tmp/{name}", "wb") as f:
#                 f.write(requests.get(url).content)

#             file_obj = DjangoFile(open(f"/tmp/{name}", 'rb'), name=name)
#             folder, res = Folder.objects.get_or_create(name="users_avatar")

#             obj, created = Image.objects.get_or_create(
#                 original_filename=file_obj.name,
#                 file=file_obj,
#                 folder=folder,
#                 is_public=True
#             )

#         except Exception as e:
#             # log error
#             return None

#         return obj

#     def get_client_ip(self):
#         request = self.request
#         x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
#         if x_forwarded_for:
#             ip = x_forwarded_for.split(',')[0]
#         else:
#             ip = request.META.get('REMOTE_ADDR')
#         return ip

#     @staticmethod
#     def generate_login_token(user):
#         token, created = Token.objects.update_or_create(user=user)
#         return token.key


# class GuestAuthView(SignupView):
#     serializer_class = GuestUserSerializer

#     def perform_create(self, serializer):
#         device_id = serializer.validated_data.get('mobile_device_id')
#         lat, lon = serializer.validated_data.pop('lat'), serializer.validated_data.pop('lon')
#         country, city = self.get_country_city_from_point(lat, lon)
#         timezone = serializer.validated_data.pop('timezone')
#         api_version = serializer.validated_data.pop('api_version')
#         serializer_data = dict(serializer.validated_data)

#         serializer_data['country'] = country
#         serializer_data['city'] = city
#         serializer_data['email'] = None

#         obj, created = GuestUser.objects.update_or_create(
#             defaults=serializer_data,
#             mobile_device_id=device_id,
#         )

#         # log user login
#         obj.login_history.create(
#             lat=lat,
#             lon=lon,
#             country=country,
#             city=city,
#             ip=self.get_client_ip(),
#             timezone=timezone,
#             api_v=api_version,
#         )
#         update_last_login(None, obj)

#         return obj


class HelloUser(APIView):
    permission_classes = (IsAuthenticated,)

    def get(self, request):
        return Response({
            "ok": self.request.user.mobile_device_id
        })

from apps.permissions import EmployeeTokenPermission
class CentrifugoTokenView(GenericAPIView):
    """Get JWT token for Centrifugo connection"""
    permission_classes = [EmployeeTokenPermission]
    
    def get(self, request):
        """Generate Centrifugo JWT token for authenticated user"""
        try:
            print(f'---> {request.employee}')

            # Get client_id from query params or generate new one
            client_id = request.GET.get('client_id')

            # Generate JWT token for Centrifugo
            jwt_token = centrifugo_service.generate_token(request.employee.id, client_id=client_id)
            response = {
                'token': jwt_token,
                'user_id': request.employee.id,
                'ws_url': settings.CENTRIFUGO_WS_URL,
                'expires_in': 24 * 3600,  # 24 hours in seconds
                'client_id': client_id,
            }
            print(f'---CentrifugoTokenView--> {response}')
            return Response(response)

        except Exception as e:
            return Response(
                {'error': f'Failed to generate token: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )






