from apps.account.models import User


def simple_middleware(get_response):
    # One-time configuration and initialization.

    def middleware(request):
        # Code to be executed for each request before
        # the view (and later middleware) are called.
        #
        if "127.0.0.1" in request.get_host():
            user = User.objects.first()
            request.META['HTTP_AUTHORIZATION'] = "Token " + user.auth_token.key

            # set persian header to django translation
            request.META['HTTP_ACCEPT_LANGUAGE'] = "fa"

        response = get_response(request)

        # Code to be executed for each request/response after
        # the view is called.

        return response

    return middleware
