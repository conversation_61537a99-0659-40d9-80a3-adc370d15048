from django.contrib.auth.models import (
    AbstractBaseUser,
    BaseUserManager,
    Permission,
    PermissionsMixin,
)
from django.contrib.auth.models import _user_has_perm, AbstractUser  # type: ignore
from django.db import models
from django.utils.translation import gettext as _
from phonenumber_field.modelfields import PhoneNumberField

from apps.account.validators import validate_possible_number


class UserManager(BaseUserManager):
    def create_user(
            self, email, password=None, is_staff=False, is_active=True, **extra_fields
    ):
        """Create a user instance with the given email and password."""
        email = UserManager.normalize_email(email)
        # Google OAuth2 backend send unnecessary username field
        extra_fields.pop("username", None)

        user = self.model(
            email=email, username=email, is_active=is_active, is_staff=is_staff, **extra_fields
        )
        if password:
            user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        return self.create_user(
            email, password, is_staff=True, is_superuser=True, **extra_fields
        )

    def staff(self):
        return self.get_queryset().filter(is_staff=True)


class User(AbstractUser):
    email = models.EmailField(verbose_name=_('email'), unique=True, null=True, blank=True)
    first_name = models.CharField(verbose_name=_('first name'), max_length=254, null=True)
    last_name = models.CharField(verbose_name=_('last name'), max_length=254, null=True)
    phone_number = PhoneNumberField(
        verbose_name=_('phone number'), null=True, blank=True, max_length=254, validators=[
            validate_possible_number
        ])
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    date_joined = models.DateTimeField(auto_now_add=True)
    is_publisher = models.BooleanField(default=False, verbose_name=_('is publisher'))

    objects = UserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = []

    def __str__(self):
        return self.email

    def is_guest(self):
        return self.email and len(self.email)

    class Meta:
        ordering = ("email",)
        verbose_name = _("user")
        verbose_name_plural = _("All users")


class AdminUser(User):
    class Meta:
        ordering = ("email",)
        proxy = True
        verbose_name = _("admin")
        verbose_name_plural = _('admins')
