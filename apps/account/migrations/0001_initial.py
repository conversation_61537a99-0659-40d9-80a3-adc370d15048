# Generated by Django 3.2.19 on 2023-05-28 17:02

import apps.account.validators
import django.contrib.auth.models
from django.db import migrations, models
import phonenumber_field.modelfields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, unique=True, verbose_name='email')),
                ('first_name', models.Char<PERSON>ield(max_length=254, null=True, verbose_name='first name')),
                ('last_name', models.CharField(max_length=254, null=True, verbose_name='last name')),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(blank=True, max_length=254, null=True, region=None, validators=[apps.account.validators.validate_possible_number], verbose_name='phone number')),
                ('is_staff', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('date_joined', models.DateTimeField(auto_now_add=True)),
                ('is_publisher', models.BooleanField(default=False, verbose_name='is publisher')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='', verbose_name='avatar')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'All users',
                'ordering': ('email',),
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='AdminUser',
            fields=[
            ],
            options={
                'verbose_name': 'admin',
                'verbose_name_plural': 'admins',
                'ordering': ('email',),
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('account.user',),
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
    ]
