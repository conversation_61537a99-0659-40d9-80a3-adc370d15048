from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from import_export.fields import Field
from import_export.resources import ModelResource

from apps.account.models import User, AdminUser
from unfold.admin import ModelAdmin, TabularInline
from unfold.forms import AdminPasswordChangeForm, UserChangeForm, UserCreationForm
from utils.admin import project_admin_site


class UserAdmin(BaseUserAdmin, ModelAdmin):
    form = UserChangeForm
    add_form = UserCreationForm
    change_password_form = AdminPasswordChangeForm
    
    search_fields = (
        'email', 'first_name', 'last_name', 'phone_number',
    )

    list_display = ('email', 'date_joined', 'last_login')
    list_filter = ('is_active',)

    ordering = ('-date_joined',)
    readonly_fields = ('date_joined', 'last_login',)
    exclude = ('password', 'user_permissions')
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )
    latest_by = 'date_joined'
    fieldsets = (
        (_('Personal info'),
         {'fields': ('password', 'first_name', 'last_name', 'phone_number', 'email')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined',)}),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups')}),
    )

    def get_queryset(self, request):
        return User.objects.all()


class AdminUserAdmin(UserAdmin):
    """Admin interface for AdminUser model."""

    def get_queryset(self, request):
        return AdminUser.objects.all()


# Register with the default admin site (for backward compatibility if needed)
admin.site.register(User, UserAdmin)

# Register with the project_admin_site (Django Unfold)
project_admin_site.register(User, UserAdmin)
project_admin_site.register(AdminUser, AdminUserAdmin)
