from dj_language.models import Language
from rest_framework import serializers
from .models import User


class UserSerializer(serializers.ModelSerializer):
    lat = serializers.CharField(max_length=255)
    lon = serializers.CharField(max_length=255)
    avatar = serializers.Char<PERSON><PERSON>(required=False, label="~ Avatar")
    phone_number = serializers.CharField(required=False, label="~ Phone number")
    social_auth_data = serializers.JSONField(required=False, default={})

    class Meta:
        model = User
        fields = (
            'email', 'first_name', 'last_name', 'mobile_device_id',
            'lat', 'lon', 'phone_number', 'avatar', 'social_auth_data',
        )


# class GuestUserSerializer(serializers.ModelSerializer):
#     lat = serializers.CharField(max_length=255)
#     lon = serializers.Char<PERSON>ield(max_length=255)
#     language = serializers.CharField(required=False)
#     mobile_device_id = serializers.Char<PERSON>ield()
#     timezone = serializers.Char<PERSON><PERSON>(required=False)
#     api_version = serializers.Char<PERSON><PERSON>(required=False)

#     class Meta:
#         model = GuestUser
#         fields = (
#             'lat', 'lon', 'mobile_device_id', 'language', 'cpu_type', 'cpu_type',
#             'model_name', 'os_platform', 'screen_size', 'device_brand', 'timezone', 'api_version', 'fcm'
#         )

#     def validate(self, attrs):
#         language_code = attrs['language']
#         lang = Language.objects.filter(code=language_code, status=True).first()
#         if not lang:
#             raise serializers.ValidationError({'language': 'this Language is not avalaible'})
#         attrs['language'] = lang
#         return attrs
