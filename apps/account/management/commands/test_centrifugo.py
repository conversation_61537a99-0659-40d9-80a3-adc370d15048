from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from apps.account.centrifugo import centrifugo_service
from apps.employee.models import Employee


class Command(BaseCommand):
    help = 'Test Centrifugo notifications for employee online/offline status'

    def add_arguments(self, parser):
        parser.add_argument(
            'action',
            choices=['online', 'offline', 'list', 'token', 'screen', 'livekit'],
            help='Action to perform: online, offline, list, token, screen, or livekit'
        )
        parser.add_argument(
            '--employee-id',
            type=int,
            help='Employee ID for online/offline notifications'
        )
        parser.add_argument(
            '--user-id',
            type=int,
            help='User ID for token generation'
        )
        parser.add_argument(
            '--requester-id',
            type=int,
            help='Requester employee ID for livekit screen share'
        )
        parser.add_argument(
            '--share-type',
            choices=['publisher', 'subscriber'],
            default='publisher',
            help='Share type for livekit screen share (publisher/subscriber)'
        )
        parser.add_argument(
            '--screen-active',
            type=bool,
            default=True,
            help='Screen sharing status for screen action (true/false)'
        )

    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'list':
            self.list_employees()
        elif action == 'token':
            self.generate_token(options.get('user_id'))
        elif action in ['online', 'offline', 'screen', 'livekit']:
            employee_id = options.get('employee_id')
            if not employee_id:
                raise CommandError('--employee-id is required for online/offline/screen/livekit actions')

            if action == 'online':
                self.test_online_notification(employee_id)
            elif action == 'offline':
                self.test_offline_notification(employee_id)
            elif action == 'screen':
                screen_active = options.get('screen_active', True)
                self.test_screen_update_notification(employee_id, screen_active)
            else:  # livekit
                requester_id = options.get('requester_id', 70)  # Default to employee 70
                share_type = options.get('share_type', 'publisher')  # Default to publisher
                self.test_livekit_screen_share_notification(employee_id, requester_id, share_type)
        else:
            raise CommandError('Invalid action')

    def list_employees(self):
        """List all employees with their IDs and basic info"""
        self.stdout.write(self.style.SUCCESS('=== Employee List ==='))
        employees = Employee.objects.all().order_by('id')
        
        if not employees.exists():
            self.stdout.write(self.style.WARNING('No employees found'))
            return
        
        for emp in employees:
            teams = ', '.join([team.name for team in emp.teams.all()]) if emp.teams.exists() else 'No teams'
            self.stdout.write(
                f"ID: {emp.id:3d} | Name: {emp.full_name:25s} | Email: {emp.email or 'N/A':30s} | Teams: {teams}"
            )
        
        self.stdout.write(f"\nTotal employees: {employees.count()}")

    def generate_token(self, user_id):
        """Generate Centrifugo token for a user"""
        if not user_id:
            raise CommandError('--user-id is required for token generation')
        
        try:
            # Check if user exists
            employee = Employee.objects.get(id=user_id)
            
            self.stdout.write(self.style.SUCCESS(f'=== Generating Centrifugo Token ==='))
            self.stdout.write(f"User: {employee.full_name} (ID: {employee.id})")
            
            # Generate token
            token = centrifugo_service.generate_token(user_id)
            
            self.stdout.write(self.style.SUCCESS('\n=== Token Generated Successfully ==='))
            self.stdout.write(f"Token: {token}")
            self.stdout.write(f"Channel: teamby:user#{user_id}")
            self.stdout.write(f"Expires: 24 hours from now")
            
            # Show connection info
            self.stdout.write(self.style.WARNING('\n=== Connection Info ==='))
            self.stdout.write("Use this token to connect to Centrifugo WebSocket")
            self.stdout.write("Example JavaScript code:")
            self.stdout.write(f"""
const centrifuge = new Centrifuge('ws://localhost:8000/connection/websocket', {{
    token: '{token}'
}});

const sub = centrifuge.subscribe('teamby:user#{user_id}', function(message) {{
    console.log('Received:', message.data);
}});

centrifuge.connect();
            """)
            
        except Employee.DoesNotExist:
            raise CommandError(f'Employee with ID {user_id} not found')
        except Exception as e:
            raise CommandError(f'Error generating token: {str(e)}')

    def test_online_notification(self, employee_id):
        """Test online notification for an employee"""
        try:
            employee = Employee.objects.get(id=employee_id)
            
            self.stdout.write(self.style.SUCCESS(f'=== Testing Online Notification ==='))
            self.stdout.write(f"Employee: {employee.full_name} (ID: {employee.id})")
            
            # Show who will receive the notification
            self.show_notification_recipients(employee)
            
            # Send notification
            self.stdout.write(self.style.WARNING('\n=== Sending Notification ==='))
            result = centrifugo_service.notify_user_online(employee_id)
            
            if result:
                self.stdout.write(self.style.SUCCESS('✅ Online notification sent successfully!'))
            else:
                self.stdout.write(self.style.ERROR('❌ Failed to send online notification'))
                
        except Employee.DoesNotExist:
            raise CommandError(f'Employee with ID {employee_id} not found')
        except Exception as e:
            raise CommandError(f'Error sending online notification: {str(e)}')

    def test_offline_notification(self, employee_id):
        """Test offline notification for an employee"""
        try:
            employee = Employee.objects.get(id=employee_id)
            
            self.stdout.write(self.style.SUCCESS(f'=== Testing Offline Notification ==='))
            self.stdout.write(f"Employee: {employee.full_name} (ID: {employee.id})")
            
            # Show who will receive the notification
            self.show_notification_recipients(employee)
            
            # Send notification
            self.stdout.write(self.style.WARNING('\n=== Sending Notification ==='))
            result = centrifugo_service.notify_user_offline(employee_id)
            
            if result:
                self.stdout.write(self.style.SUCCESS('✅ Offline notification sent successfully!'))
            else:
                self.stdout.write(self.style.ERROR('❌ Failed to send offline notification'))
                
        except Employee.DoesNotExist:
            raise CommandError(f'Employee with ID {employee_id} not found')
        except Exception as e:
            raise CommandError(f'Error sending offline notification: {str(e)}')

    def test_screen_update_notification(self, employee_id, screen_active):
        """Test screen sharing status update notification for an employee"""
        try:
            employee = Employee.objects.get(id=employee_id)

            self.stdout.write(self.style.SUCCESS(f'=== Testing Screen Update Notification ==='))
            self.stdout.write(f"Employee: {employee.full_name} (ID: {employee.id})")
            self.stdout.write(f"Screen Active: {screen_active}")

            # Show who will receive the notification
            self.show_notification_recipients(employee)

            # Send notification
            self.stdout.write(self.style.WARNING('\n=== Sending Screen Update Notification ==='))
            result = centrifugo_service.notify_employee_screen_update(employee_id, screen_active)

            if result:
                self.stdout.write(self.style.SUCCESS('✅ Screen update notification sent successfully!'))
            else:
                self.stdout.write(self.style.ERROR('❌ Failed to send screen update notification'))

        except Employee.DoesNotExist:
            raise CommandError(f'Employee with ID {employee_id} not found')
        except Exception as e:
            raise CommandError(f'Error sending screen update notification: {str(e)}')

    def test_livekit_screen_share_notification(self, employee_id, requester_id, share_type):
        """Test LiveKit screen share notification for an employee"""
        try:
            employee = Employee.objects.get(id=employee_id)
            requester = Employee.objects.get(id=requester_id)

            self.stdout.write(self.style.SUCCESS(f'=== Testing LiveKit Screen Share Notification ==='))
            self.stdout.write(f"Target Employee: {employee.full_name} (ID: {employee.id})")
            self.stdout.write(f"Requester Employee: {requester.full_name} (ID: {requester.id})")
            self.stdout.write(f"Share Type: {share_type}")
            self.stdout.write(f"Channel: teamby:user#{employee.id}")

            # Send notification
            self.stdout.write(self.style.WARNING('\n=== Sending LiveKit Screen Share Notification ==='))
            result = centrifugo_service.notify_livekit_screen_share(employee_id, requester_id, share_type)

            if result:
                self.stdout.write(self.style.SUCCESS('✅ LiveKit screen share notification sent successfully!'))
            else:
                self.stdout.write(self.style.ERROR('❌ Failed to send LiveKit screen share notification'))

        except Employee.DoesNotExist as e:
            raise CommandError(f'Employee not found: {str(e)}')
        except Exception as e:
            raise CommandError(f'Error sending LiveKit screen share notification: {str(e)}')

    def show_notification_recipients(self, employee):
        """Show who will receive notifications for this employee"""
        employees_with_access = employee.get_connected_employees()
        teams = employee.teams.all()
        
        self.stdout.write(f"\nEmployee Teams: {', '.join([team.name for team in teams]) if teams.exists() else 'None'}")
        
        if employees_with_access.exists():
            self.stdout.write(f"\n=== Notification Recipients ({employees_with_access.count()}) ===")
            for emp in employees_with_access:
                channel = f"teamby:user#{emp.id}"
                self.stdout.write(f"  • {emp.full_name} (ID: {emp.id}) → Channel: {channel}")
        else:
            self.stdout.write(self.style.WARNING("\n⚠️  No recipients found! This employee has no team members or permissions."))
            self.stdout.write("Make sure the employee is in a team or has permission relationships.")
