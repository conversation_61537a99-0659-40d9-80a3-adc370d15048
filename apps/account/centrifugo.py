import jwt
import requests
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from typing import Optional, Dict, Any


class CentrifugoService:
    """Service class for Centrifugo integration"""
    
    def __init__(self):
        self.secret = settings.CENTRIFUGO_SECRET
        self.api_url = settings.CENTRIFUGO_API_URL.rstrip('/')  # Remove trailing slash
        self.api_key = settings.CENTRIFUGO_API_KEY
    
    def generate_token(self, user_id: int, expires_in_hours: int = 24, client_id: str = None, connection_ttl: int = None) -> str:
        """Generate JWT token for Centrifugo with unique client identification and connection TTL"""
        import uuid

        # Generate unique client ID if not provided
        if not client_id:
            client_id = str(uuid.uuid4())

        # Default connection TTL to 1 hour (3600 seconds) if not specified
        if connection_ttl is None:
            connection_ttl = 3600  # 1 hour

        payload = {
            'sub': str(user_id),
            'exp': datetime.utcnow() + timedelta(hours=expires_in_hours),
            'iat': datetime.utcnow(),
            'aud': 'teamby',
            'iss': 'teamby-backend',
            'client': client_id,  # Add unique client identifier
            'jti': str(uuid.uuid4()),  # JWT ID for uniqueness
            'info': {
                'connection_ttl': connection_ttl,  # Auto-disconnect after this time
                'client_name': client_id
            }
        }
        return jwt.encode(payload, self.secret, algorithm='HS256')
    
    def publish(self, channel: str, data: Dict[Any, Any]) -> Optional[Dict]:
        """Publish message to Centrifugo channel"""
        try:
            payload = {
                'method': 'publish',
                'params': {
                    'channel': channel,
                    'data': data
                }
            }

            response = requests.post(self.api_url, json=payload, headers={
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key
            })

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('error'):
                        print(f"--centrifugo-api-error-> {result['error']}")
                        return None
                    return result
                except Exception as json_error:
                    print(f"--centrifugo-json-error-> {json_error}")
                    return None
            else:
                print(f"--centrifugo-http-error-> Status {response.status_code}: {response.text}")
                return None

        except Exception as e:
            print(f"Centrifugo publish error: {e}")
            return None
    
    def notify_user_online(self, employee_id: int) -> bool:
        """Notify users who have this employee in their list that employee came online"""
        from apps.employee.models import Employee

        try:
            # Get the employee object
            employee = Employee.objects.get(id=employee_id)

            # Get all employees who have access to this employee
            employees_with_access = employee.get_connected_employees()

            success_count = 0
            total_count = employees_with_access.count()

            payload = {
                'type': 'employee_online',
                'timestamp': int(timezone.now().timestamp() * 1000),
                'employee_id': employee_id,
                'status': 'online'
            }

            # Send notification to each user's personal channel
            for emp in employees_with_access:
                channel = f'teamby:user#{emp.id}'
                result = self.publish(channel, payload)
                if result:
                    success_count += 1
                    print(f'--centrifugo-sent-to-> User {emp.id} ({emp.full_name})')
                else:
                    print(f'--centrifugo-failed-to-> User {emp.id} ({emp.full_name})')

            print(f'--centrifugo-summary-> Sent to {success_count}/{total_count} users')
            return success_count > 0

        except Exception as e:
            print(f'--centrifugo-error-> {str(e)}')
            return False
    
    def notify_user_offline(self, employee_id: int) -> bool:
        """Notify users who have this employee in their list that employee went offline"""
        from apps.employee.models import Employee

        try:
            # Get the employee object
            employee = Employee.objects.get(id=employee_id)

            # Get all employees who have access to this employee
            employees_with_access = employee.get_connected_employees()

            success_count = 0
            total_count = employees_with_access.count()

            payload = {
                'type': 'employee_offline',
                'timestamp': int(timezone.now().timestamp() * 1000),
                'employee_id': employee_id,
                'employee_name': employee.full_name,
                'status': 'offline'
            }

            # Send notification to each user's personal channel
            for emp in employees_with_access:
                channel = f'teamby:user#{emp.id}'
                result = self.publish(channel, payload)
                if result:
                    success_count += 1
                    print(f'--centrifugo-offline-sent-to-> User {emp.id} ({emp.full_name})')
                else:
                    print(f'--centrifugo-offline-failed-to-> User {emp.id} ({emp.full_name})')

            print(f'--centrifugo-offline-summary-> Sent to {success_count}/{total_count} users')
            return success_count > 0

        except Exception as e:
            print(f'--centrifugo-offline-error-> {str(e)}')
            return False

    def notify_employee_screen_update(self, employee_id: int, screen_active: bool) -> bool:
        """Notify users about employee screen sharing status change"""
        from apps.employee.models import Employee

        try:
            # Get the employee object
            employee = Employee.objects.get(id=employee_id)

            # Get all employees who have access to this employee
            employees_with_access = employee.get_connected_employees()

            success_count = 0
            total_count = employees_with_access.count()

            payload = {
                'type': 'employee_update_screen_view',
                'timestamp': int(timezone.now().timestamp() * 1000),
                'employee_id': employee_id,
                'employee_name': employee.full_name,
                'screen_active': screen_active,
                'status': 'screen_sharing_enabled' if screen_active else 'screen_sharing_disabled'
            }

            # Send notification to each user's personal channel
            for emp in employees_with_access:
                channel = f'teamby:user#{emp.id}'
                result = self.publish(channel, payload)
                if result:
                    success_count += 1
                    print(f'--centrifugo-screen-update-sent-to-> User {emp.id} ({emp.full_name})')
                else:
                    print(f'--centrifugo-screen-update-failed-to-> User {emp.id} ({emp.full_name})')

            print(f'--centrifugo-screen-update-summary-> Sent to {success_count}/{total_count} users')
            return success_count > 0

        except Exception as e:
            print(f'--centrifugo-screen-update-error-> {str(e)}')
            return False

    def notify_livekit_screen_share(self, employee_target_id: int, requester_employee_id: int, share_type: str) -> bool:
        """Send LiveKit screen share notification to specific employee"""
        try:
            from apps.employee.models import Employee

            # Get requester employee info
            requester_employee = Employee.objects.get(id=requester_employee_id)

            channel = f'teamby:user#{employee_target_id}'
            payload = {
                'type': 'livekit_share_ascreen_view',
                'timestamp': int(timezone.now().timestamp() * 1000),
                'employee_id': requester_employee_id,
                'employee_name': requester_employee.full_name,
                'share_type': share_type
            }

            result = self.publish(channel, payload)
            if result:
                print(f'--centrifugo-livekit-screen-share-sent-to-> User {employee_target_id} from {requester_employee.full_name}')
                return True
            else:
                print(f'--centrifugo-livekit-screen-share-failed-to-> User {employee_target_id}')
                return False

        except Employee.DoesNotExist:
            print(f'--centrifugo-livekit-screen-share-error-> Requester employee {requester_employee_id} not found')
            return False
        except Exception as e:
            print(f'--centrifugo-livekit-screen-share-error-> {str(e)}')
            return False
    
    def notify_user(self, user_id: int, message_type: str, data: Dict[Any, Any]) -> bool:
        """Send notification to user's personal channel"""
        channel = f'teamby:user#{user_id}'
        payload = {
            'type': message_type,
            'timestamp': int(timezone.now().timestamp() * 1000),
            **data
        }
        
        result = self.publish(channel, payload)
        return result is not None


    
    def notify_system_notification(self, user_id: int, notification_type: str, title: str, message: str, data: Dict = None) -> bool:
        """Send system notification to user"""
        return self.notify_user(user_id, 'notification', {
            'notification_type': notification_type,
            'title': title,
            'message': message,
            'data': data or {}
        })


# Service instance
centrifugo_service = CentrifugoService()
