from django.db import models


class Company(models.Model):
    name = models.CharField(max_length=200, verbose_name="نام شرکت")
    founder_name = models.CharField(max_length=200, verbose_name="نام بنیانگذار")
    website = models.URLField(max_length=300, null=True, blank=True, verbose_name="وب‌سایت شرکت")
    logo = models.ImageField(upload_to='company_logos/', null=True, blank=True, verbose_name="لوگوی شرکت")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاریخ ایجاد")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاریخ به‌روزرسانی")

    class Meta:
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'
        ordering = ['-created_at']

    def __str__(self):
        return self.name


class Position(models.Model):
    name = models.Char<PERSON><PERSON>(max_length=200, verbose_name="نام پوزیشن")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاریخ ایجاد")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاریخ به‌روزرسانی")

    class Meta:
        verbose_name = 'Position'
        verbose_name_plural = 'Positions'
        ordering = ['name']

    def __str__(self):
        return self.name
