from django.contrib import admin
from unfold.admin import ModelAdmin
from .models import Company, Position
from utils.admin import project_admin_site


# @admin.register(Company)
class CompanyAdmin(ModelAdmin):
    list_display = ('name', 'founder_name', 'website', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name', 'founder_name', 'website')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('اطلاعات شرکت', {
            'fields': (
                'name',
                'founder_name',
                'website',
                'logo',
            ),
            'classes': ('grid-col-2',),
            'description': 'اطلاعات اصلی شرکت شامل نام، بنیانگذار و وب‌سایت'
        }),
        ('اطلاعات سیستم', {
            'fields': (
                'created_at',
                'updated_at',
            ),
            'classes': ('grid-col-2',),
            'description': 'اطلاعات سیستمی و تاریخ‌ها'
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('employees')

@admin.register(Position)
class PositionAdmin(ModelAdmin):
    list_display = ('name', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('اطلاعات پوزیشن', {
            'fields': (
                'name',
            ),
            'description': 'اطلاعات پوزیشن شغلی'
        }),
        ('اطلاعات سیستم', {
            'fields': (
                'created_at',
                'updated_at',
            ),
            'classes': ('grid-col-2',),
            'description': 'اطلاعات سیستمی و تاریخ‌ها'
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('employees')


project_admin_site.register(Company, CompanyAdmin)
project_admin_site.register(Position, PositionAdmin)