from django.core.management.base import BaseCommand
from apps.companies.models import Position


class Command(BaseCommand):
    help = 'Create standard software company positions'

    def handle(self, *args, **options):
        # لیست پوزیشن‌های استاندارد در شرکت‌های نرم‌افزاری
        positions = [
            # Development Positions
            'Frontend Developer',
            'Backend Developer',
            'Full Stack Developer',
            'Mobile Developer (iOS)',
            'Mobile Developer (Android)',
            'React Native Developer',
            'Flutter Developer',
            
            # Senior Development Positions
            'Senior Frontend Developer',
            'Senior Backend Developer',
            'Senior Full Stack Developer',
            'Lead Developer',
            'Principal Engineer',
            'Staff Engineer',
            
            # Architecture & Technical Leadership
            'Software Architect',
            'Technical Lead',
            'Engineering Manager',
            'VP of Engineering',
            'CTO',
            
            # DevOps & Infrastructure
            'DevOps Engineer',
            'Site Reliability Engineer (SRE)',
            'Cloud Engineer',
            'Infrastructure Engineer',
            'Platform Engineer',
            
            # Quality Assurance
            'QA Engineer',
            'Test Automation Engineer',
            'QA Lead',
            'QA Manager',
            
            # Data & Analytics
            'Data Scientist',
            'Data Engineer',
            'Data Analyst',
            'Machine Learning Engineer',
            'AI Engineer',
            
            # Design
            'UI Designer',
            'UX Designer',
            'UI/UX Designer',
            'Product Designer',
            'Graphic Designer',
            
            # Product Management
            'Product Manager',
            'Senior Product Manager',
            'Product Owner',
            'Technical Product Manager',
            
            # Project Management
            'Project Manager',
            'Scrum Master',
            'Agile Coach',
            'Program Manager',
            
            # Security
            'Security Engineer',
            'Cybersecurity Specialist',
            'Information Security Analyst',
            
            # Database
            'Database Administrator',
            'Database Developer',
            
            # Business & Operations
            'Business Analyst',
            'Systems Analyst',
            'Technical Writer',
            'Documentation Specialist',
            
            # Support & Maintenance
            'Technical Support Engineer',
            'Customer Success Engineer',
            'Support Manager',
            
            # Sales & Marketing
            'Sales Engineer',
            'Technical Sales Representative',
            'Marketing Manager',
            'Digital Marketing Specialist',
            
            # Management
            'Team Lead',
            'Department Manager',
            'Director of Engineering',
            'CEO',
            'COO',
            'CFO',
            
            # Internship & Entry Level
            'Software Engineering Intern',
            'Junior Developer',
            'Graduate Developer',
            'Trainee Developer',
        ]

        created_count = 0
        existing_count = 0

        for position_name in positions:
            position, created = Position.objects.get_or_create(name=position_name)
            if created:
                created_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Created position: {position_name}')
                )
            else:
                existing_count += 1
                self.stdout.write(
                    self.style.WARNING(f'- Position already exists: {position_name}')
                )

        self.stdout.write(
            self.style.SUCCESS(
                f'\n📊 Summary:'
                f'\n   • Created: {created_count} positions'
                f'\n   • Already existed: {existing_count} positions'
                f'\n   • Total positions: {Position.objects.count()}'
            )
        )
