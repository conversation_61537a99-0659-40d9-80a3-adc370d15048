import os
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.companies.models import Company
from apps.employee.models import Employee


class Command(BaseCommand):
    help = 'Setup NewHorizon company and assign all employees to it'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test',
            action='store_true',
            help='Run in test mode (show what would be done without making changes)',
        )

    def handle(self, *args, **options):
        is_test_mode = options['test']
        
        # بررسی اینکه فقط کاربر اصلی بتواند اجرا کند (غیر از حالت تست)
        if not is_test_mode:
            current_user = os.getenv('USER', os.getenv('USERNAME', ''))
            if current_user != 'alireza':
                self.stdout.write(
                    self.style.ERROR(
                        '❌ This command can only be executed by the main user (alireza).\n'
                        '   Use --test flag to run in test mode.'
                    )
                )
                return

        # اطلاعات شرکت NewHorizon
        company_data = {
            'name': 'NewHorizon',
            'founder_name': '<PERSON><PERSON><PERSON>',
            'website': 'https://newhorizonco.uk'
        }

        if is_test_mode:
            self.stdout.write(
                self.style.WARNING('🧪 TEST MODE - No changes will be made\n')
            )

        # ایجاد یا دریافت شرکت NewHorizon
        if is_test_mode:
            try:
                company = Company.objects.get(name=company_data['name'])
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Company "{company.name}" already exists')
                )
            except Company.DoesNotExist:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Would create company: {company_data["name"]}')
                )
                company = None
        else:
            company, created = Company.objects.get_or_create(
                name=company_data['name'],
                defaults={
                    'founder_name': company_data['founder_name'],
                    'website': company_data['website']
                }
            )
            
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Created company: {company.name}')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Company "{company.name}" already exists')
                )

        # دریافت تمام کارمندان
        employees = Employee.objects.all()
        total_employees = employees.count()

        if total_employees == 0:
            self.stdout.write(
                self.style.WARNING('⚠️  No employees found in the system')
            )
            return

        # تخصیص کارمندان به شرکت
        if is_test_mode:
            employees_without_company = employees.filter(company__isnull=True).count()
            employees_with_different_company = employees.exclude(company=company).count() if company else total_employees
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'✓ Would assign {total_employees} employees to NewHorizon company:\n'
                    f'   • Employees without company: {employees_without_company}\n'
                    f'   • Employees with different company: {employees_with_different_company}'
                )
            )
        else:
            updated_count = 0
            for employee in employees:
                if employee.company != company:
                    employee.company = company
                    employee.save()
                    updated_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(f'✓ Assigned {employee.full_name} to {company.name}')
                    )

            self.stdout.write(
                self.style.SUCCESS(
                    f'\n📊 Summary:'
                    f'\n   • Total employees: {total_employees}'
                    f'\n   • Updated employees: {updated_count}'
                    f'\n   • Company: {company.name}'
                    f'\n   • Founder: {company.founder_name}'
                    f'\n   • Website: {company.website}'
                )
            )
