# Generated by Django 5.2.1 on 2025-07-10 03:18

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200, verbose_name='نام شرکت')),
                ('founder_name', models.CharField(max_length=200, verbose_name='نام بنیانگذار')),
                ('website', models.URLField(blank=True, max_length=300, null=True, verbose_name='وب\u200cسایت شرکت')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/', verbose_name='لوگوی شرکت')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاریخ ایجاد')),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True, verbose_name='تاریخ به\u200cروزرسانی')),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
                'ordering': ['-created_at'],
            },
        ),
    ]
