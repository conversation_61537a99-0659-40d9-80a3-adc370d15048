from django.db import models
class Projects(models.Model):

    name = models.CharField(max_length=200)
    icon = models.ImageField(upload_to='project-icons/', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True, blank=True)
    color_code = models.CharField("Token", max_length=60,default="#232d83",blank=True)

    class Meta:
        verbose_name = 'Project'
        verbose_name_plural = 'Projects'

    def __str__(self):
        return self.name

