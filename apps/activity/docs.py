from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status


# Employee Stats API Documentation
employee_stats_swagger = swagger_auto_schema(
    operation_description="Get employee statistics including monthly work hours, income, and commitment deficits",
    operation_summary="Get Employee Statistics",
    tags=['Activity API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Statistics retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'title': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Statistic title',
                            example='کارکرد ماه'
                        ),
                        'icon': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='FontAwesome icon class',
                            example='fa-clock'
                        ),
                        'value': openapi.Schema(
                            type=openapi.TYPE_NUMBER,
                            description='Statistic value',
                            example=120.5
                        ),
                    }
                )
            ),
            examples={
                'application/json': [
                    {'title': 'کارکرد ماه', 'icon': 'fa-clock', 'value': 120.5},
                    {'title': 'درآمد ماه (میلیون)', 'icon': 'fa-dollar-sign', 'value': 2.5},
                    {'title': 'کمبود ساعت تعهدی امروز', 'icon': 'fa-frown-open', 'value': 2.0}
                ]
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Employee not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Not found.'
                    )
                }
            )
        ),
    }
)


# Time Entry List API Documentation
time_entry_list_swagger = swagger_auto_schema(
    operation_description="Get paginated list of completed time entries for the authenticated employee",
    operation_summary="Get Time Entry List",
    tags=['Activity API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        ),
        openapi.Parameter(
            'page',
            openapi.IN_QUERY,
            description="Page number for pagination",
            type=openapi.TYPE_INTEGER,
            required=False,
            example=1
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Time entries retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                        'edit_status': openapi.Schema(type=openapi.TYPE_STRING, example='APPROVED'),
                        'start_time': openapi.Schema(type=openapi.TYPE_STRING, example='2024-01-15 09:00:00'),
                        'end_time': openapi.Schema(type=openapi.TYPE_STRING, example='2024-01-15 17:00:00'),
                        'project': openapi.Schema(type=openapi.TYPE_STRING, example='Mobile App'),
                        'price': openapi.Schema(type=openapi.TYPE_NUMBER, example=50000),
                        'type': openapi.Schema(type=openapi.TYPE_STRING, example='development'),
                        'reports_audios': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                        'report_text': openapi.Schema(type=openapi.TYPE_STRING, example='Worked on frontend development'),
                        'jira_task_id': openapi.Schema(type=openapi.TYPE_STRING, example='USER-123'),
                        'jira_task_name': openapi.Schema(type=openapi.TYPE_STRING, example='Login Feature'),
                        'duration': openapi.Schema(type=openapi.TYPE_STRING, example='8:0:0'),
                    }
                )
            )
        ),
    }
)


# Time Start Create API Documentation
time_start_create_swagger = swagger_auto_schema(
    operation_description="Start a new time tracking entry for the authenticated employee",
    operation_summary="Start Time Tracking",
    tags=['Activity API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        )
    ],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'project': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Project ID (optional)',
                example=1
            ),
            'type': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Activity type (optional)',
                example='development'
            ),
            'report_text': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Initial activity description (optional)',
                example='Starting work on user authentication module'
            ),
            'project_goal': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Project goal ID (optional)',
                example=1
            ),
            'target_goal': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Target goal ID (optional)',
                example=1
            ),
            'system_local_time': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATETIME,
                description='System local time (optional)',
                example='2024-01-15T09:00:00'
            ),
            'platform': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Platform (optional)',
                example='web'
            ),
            'device_name': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Device name (optional)',
                example='MacBook Pro'
            ),
            'device_os': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Device operating system (optional)',
                example='macOS 14.0'
            ),
            'version': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Activity version (optional)',
                example=2
            ),
            'is_activity_submitted': openapi.Schema(
                type=openapi.TYPE_BOOLEAN,
                description='Is activity submitted (optional)',
                example=True
            ),
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Time tracking started successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'start_time': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                    'employee': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'project': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'type': openapi.Schema(type=openapi.TYPE_STRING, example='development'),
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Bad request - validation errors or active time entry exists",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'error': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='You already have an active time entry'
                    )
                }
            )
        ),
    }
)


# Time End API Documentation
time_end_swagger = swagger_auto_schema(
    operation_description="End the current time tracking entry with report validation",
    operation_summary="End Time Tracking",
    tags=['Activity API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        )
    ],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Time entry ID (optional, uses latest if not provided)',
                example=1
            ),
            'report_text': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Work report description',
                example='Completed user authentication module with login and registration features'
            ),
            'edit_post': openapi.Schema(
                type=openapi.TYPE_BOOLEAN,
                description='Whether this is an edit post',
                example=True
            ),
            'imready_error_logs_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Number of error logs',
                example=0
            ),
            'jira_task': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Jira task in format "task_name***task_id"',
                example='USER-123 Login Feature***USER-123'
            ),
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Time tracking ended successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'start_time': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                    'end_time': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                    'report_text': openapi.Schema(type=openapi.TYPE_STRING),
                    'report_score': openapi.Schema(type=openapi.TYPE_INTEGER, example=85),
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Bad request - validation errors or report quality issues",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'error': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Report quality is insufficient. Please provide more details.'
                    )
                }
            )
        ),
    }
)


# Time Update API Documentation
time_update_swagger = swagger_auto_schema(
    operation_description="Update time tracking entry with detailed report and validation",
    operation_summary="Update Time Tracking",
    tags=['Activity API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        )
    ],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Time entry ID (optional, uses latest if not provided)',
                example=1
            ),
            'activity_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Alternative field name for time entry ID',
                example=1
            ),
            'report_text': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Detailed work report',
                example='Implemented user authentication with JWT tokens, created login/register forms, added password validation'
            ),
            'edit_post': openapi.Schema(
                type=openapi.TYPE_BOOLEAN,
                description='Whether this is an edit post',
                example=True
            ),
            'imready_error_logs_count': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Number of error logs (affects extra time calculation)',
                example=0
            ),
            'jira_task': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Jira task in format "task_name***task_id"',
                example='USER-123 Login Feature***USER-123'
            ),
            'notes': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Alternative field name for report_text',
                example='Worked on user authentication module'
            ),
            'project_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Alternative field name for project',
                example=5
            ),
            'task_id': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Alternative field name for target_goal',
                example=41
            ),
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Time entry updated successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'report_text': openapi.Schema(type=openapi.TYPE_STRING),
                    'report_score': openapi.Schema(type=openapi.TYPE_INTEGER, example=90),
                    'date': openapi.Schema(type=openapi.TYPE_STRING, example='1403/09/23'),
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Bad request - validation errors",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'error': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Report validation failed'
                    )
                }
            )
        ),
    }
)


# Update Last Ready API Documentation
update_last_ready_swagger = swagger_auto_schema(
    operation_description="Update the last ready timestamp for an active time entry",
    operation_summary="Update Last Ready Time",
    tags=['Activity API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        ),
        openapi.Parameter(
            'id',
            openapi.IN_PATH,
            description="Time entry ID",
            type=openapi.TYPE_INTEGER,
            required=True,
            example=1
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Last ready time updated successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Activity updated successfully.'),
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Activity already ended or other error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='error'),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Activity ended.'),
                }
            )
        ),
    }
)


# Edit Time Entry API Documentation
edit_time_entry_swagger = swagger_auto_schema(
    operation_description="Edit a time entry with proposed start/end times and validation",
    operation_summary="Edit Time Entry",
    tags=['Activity API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        ),
        openapi.Parameter(
            'time_entry_id',
            openapi.IN_PATH,
            description="Time entry ID to edit",
            type=openapi.TYPE_INTEGER,
            required=True,
            example=1
        )
    ],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['proposed_start_time', 'proposed_end_time'],
        properties={
            'proposed_start_time': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATETIME,
                description='Proposed start time in ISO format',
                example='2024-01-15T09:00:00Z'
            ),
            'proposed_end_time': openapi.Schema(
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATETIME,
                description='Proposed end time in ISO format',
                example='2024-01-15T17:00:00Z'
            ),
            'edit_reason': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Reason for editing the time entry',
                example='Forgot to log out yesterday'
            ),
            'report_text': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Updated report text',
                example='Updated work description'
            ),
            'project': openapi.Schema(
                type=openapi.TYPE_INTEGER,
                description='Project ID',
                example=1
            ),
            'type': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Activity type',
                example='development'
            ),
            'jira_task_id': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Jira task ID',
                example='USER-123'
            ),
            'jira_task_name': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Jira task name',
                example='Login Feature'
            ),
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Time entry updated successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Time entry updated successfully.'),
                }
            )
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Bad request - validation errors",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='error'),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Proposed start time or end time not provided.'),
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Time entry not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='error'),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Time entry not found.'),
                }
            )
        ),
    }
)


# Time Entry Detail API Documentation
time_entry_detail_swagger = swagger_auto_schema(
    operation_description="Get detailed information about a specific time entry",
    operation_summary="Get Time Entry Detail",
    tags=['Activity API'],
    manual_parameters=[
        openapi.Parameter(
            'X-Api-Key',
            openapi.IN_HEADER,
            description="Employee authentication token",
            type=openapi.TYPE_STRING,
            required=True,
            example="abc123def456ghi789"
        ),
        openapi.Parameter(
            'time_entry_id',
            openapi.IN_PATH,
            description="Time entry ID",
            type=openapi.TYPE_INTEGER,
            required=True,
            example=1
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Time entry details retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                    'data': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                            'employee': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                            'start_time': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                            'end_time': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                            'report_text': openapi.Schema(type=openapi.TYPE_STRING, example='Completed user authentication module'),
                            'project': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                            'project_goal': openapi.Schema(type=openapi.TYPE_INTEGER, example=1, nullable=True),
                            'target_goal': openapi.Schema(type=openapi.TYPE_INTEGER, example=1, nullable=True),
                            'type': openapi.Schema(type=openapi.TYPE_STRING, example='development'),
                            'edit_status': openapi.Schema(type=openapi.TYPE_STRING, example='APPROVED'),
                            'report_score': openapi.Schema(type=openapi.TYPE_INTEGER, example=85, nullable=True),
                            'report_improvement_suggestion': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                            'jira_task_id': openapi.Schema(type=openapi.TYPE_STRING, example='USER-123', nullable=True),
                            'jira_task_name': openapi.Schema(type=openapi.TYPE_STRING, example='Login Feature', nullable=True),
                            'price': openapi.Schema(type=openapi.TYPE_NUMBER, example=50000, nullable=True),
                            'reports_audios': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                            'last_ready': openapi.Schema(type=openapi.TYPE_INTEGER, nullable=True),
                            'proposed_start_time': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, nullable=True),
                            'proposed_end_time': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, nullable=True),
                            'edit_reason': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                            'edit_log': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                            'system_local_time': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, nullable=True),
                            'platform': openapi.Schema(type=openapi.TYPE_STRING, nullable=True, example='web'),
                            'device_name': openapi.Schema(type=openapi.TYPE_STRING, nullable=True, example='MacBook Pro'),
                            'device_os': openapi.Schema(type=openapi.TYPE_STRING, nullable=True, example='macOS 14.0'),
                            'version': openapi.Schema(type=openapi.TYPE_INTEGER, nullable=True, example=2),
                            'is_activity_submitted': openapi.Schema(type=openapi.TYPE_BOOLEAN, nullable=True, example=True),
                        }
                    ),
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Time entry not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'status': openapi.Schema(type=openapi.TYPE_STRING, example='error'),
                    'message': openapi.Schema(type=openapi.TYPE_STRING, example='Time entry not found.'),
                }
            )
        ),
    }
)
