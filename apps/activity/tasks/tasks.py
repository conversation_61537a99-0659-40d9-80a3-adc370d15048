from django.conf import settings
import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application
get_wsgi_application()

import logging
import pytz
import django
import logging
from datetime import datetime
from django.utils import timezone
from datetime import timedelta
from apps.activity.models import TimeEntry
import requests
import jdatetime
import pytz
from django.conf import settings


logger = logging.getLogger(__name__)

def auto_close_inactive_time_entries():
    print("Starting auto_close_inactive_time_entries task")
    
    # محاسبه حداکثر زمان آخرین آماده بودن (۲۰ دقیقه قبل از اکنون)
    now = timezone.now()
    inactivity_threshold = 20  # دقیقه
    
    # محاسبه زمان آستانه فعالیت (۲۰ دقیقه قبل از زمان کنونی)
    max_last_ready_time = now.timestamp() - (inactivity_threshold * 60)
    
    # پیدا کردن تمام فعالیت‌های باز با last_ready قدیمی‌تر از آستانه
    open_entries = TimeEntry.objects.filter(
        end_time__isnull=True,
        last_ready__isnull=False,
        last_ready__lt=max_last_ready_time
    )
    
    count = 0
    for entry in open_entries:
        try:
            # تبدیل last_ready از تایم‌استمپ یونیکس به datetime
            # توجه: last_ready بدون offset منطقه زمانی است
            last_ready_datetime = timezone.datetime.fromtimestamp(entry.last_ready, tz=timezone.utc)
            
            # بروزرسانی زمان پایان با زمان آخرین آماده بودن
            entry.end_time = last_ready_datetime
            
            # ثبت تغییر در لاگ (اگر وجود دارد)
            log_message = f"Automatically closed due to inactivity. Last active: {last_ready_datetime.isoformat()}"
            if entry.edit_log:
                entry.edit_log += f"\n{log_message}"
            else:
                entry.edit_log = log_message
                
            entry.save()
            count += 1
            
            print(f"Auto-closed TimeEntry ID: {entry.id} for employee ID: {entry.employee_id}")
            
        except Exception as e:
            print(f"Error auto-closing TimeEntry ID: {entry.id}: {str(e)}")
    
    print(f"Completed auto_close_inactive_time_entries task. Closed {count} entries.")
    return count 


def send_sms_report_based_on_date():
    """
    تسک ارسال گزارش SMS بر اساس تاریخ و زمان جلالی
    
    این تسک بررسی می‌کند که آیا:
    1. ساعت فعلی بین 3 تا 4 است و امروز گزارش روزانه ارسال نشده (برای گزارش روزانه)
    2. اولین روز هفته (شنبه) است و این هفته گزارش هفتگی ارسال نشده (برای گزارش هفتگی)
    3. اولین روز ماه است و این ماه گزارش ماهانه ارسال نشده (برای گزارش ماهانه)
    
    و در صورت برقراری هر یک از شرایط، API مربوطه را فراخوانی می‌کند.
    """
    # تنظیم منطقه زمانی تهران
    tehran_tz = pytz.timezone('Asia/Tehran')
    now = datetime.now(tehran_tz)
    
    # تبدیل به تاریخ جلالی
    jalali_date = jdatetime.date.fromgregorian(date=now.date())
    
    # مسیر فایل‌های ذخیره آخرین زمان ارسال
    log_dir = "/tmp"
    daily_log_file = f"{log_dir}/timme_daily_sms_sent.log"
    weekly_log_file = f"{log_dir}/timme_weekly_sms_sent.log"
    monthly_log_file = f"{log_dir}/timme_monthly_sms_sent.log"
    
    # بررسی ساعت 3 تا 4 هر روز (برای گزارش روزانه)
    # if 15 <= now.hour < 16:
        # بررسی آیا امروز پیام ارسال شده است
    today_str = now.strftime("%Y-%m-%d")
    already_sent_today = False
    
    try:
        if os.path.exists(daily_log_file):
            with open(daily_log_file, 'r') as f:
                last_sent_date = f.read().strip()
                already_sent_today = (last_sent_date == today_str)
    except Exception as e:
        print(f"خطا در خواندن فایل لاگ روزانه: {str(e)}")
    
    # اگر امروز پیام ارسال نشده، ارسال کن
    # if not already_sent_today:
        # فراخوانی API برای گزارش روزانه
    _call_sms_report_api("Daily_Motivation")
    
    # ذخیره تاریخ ارسال
    try:
        with open(daily_log_file, 'w') as f:
            f.write(today_str)
        print(f"تاریخ ارسال پیام روزانه ذخیره شد: {today_str}")
    except Exception as e:
        print(f"خطا در ذخیره تاریخ ارسال پیام روزانه: {str(e)}")
    
    # بررسی اولین روز هفته - شنبه (برای گزارش هفتگی)
    # در تقویم جلالی، شنبه معادل weekday=0 است
    if jalali_date.weekday() == 0:  # شنبه
        # بررسی آیا این هفته پیام ارسال شده است
        week_str = f"{jalali_date.year}-{jalali_date.month}-W{(jalali_date.day // 7) + 1}"
        already_sent_this_week = False
        
        try:
            if os.path.exists(weekly_log_file):
                with open(weekly_log_file, 'r') as f:
                    last_sent_week = f.read().strip()
                    already_sent_this_week = (last_sent_week == week_str)
        except Exception as e:
            print(f"خطا در خواندن فایل لاگ هفتگی: {str(e)}")
        
        # اگر این هفته پیام ارسال نشده، ارسال کن
        if not already_sent_this_week:
            # فراخوانی API برای گزارش هفتگی
            _call_sms_report_api("weekly_Motivation")
            
            # ذخیره هفته ارسال
            try:
                with open(weekly_log_file, 'w') as f:
                    f.write(week_str)
                print(f"تاریخ ارسال پیام هفتگی ذخیره شد: {week_str}")
            except Exception as e:
                print(f"خطا در ذخیره تاریخ ارسال پیام هفتگی: {str(e)}")
    
    # بررسی اولین روز ماه (برای گزارش ماهانه)
    if jalali_date.day == 1:
        # بررسی آیا این ماه پیام ارسال شده است
        month_str = f"{jalali_date.year}-{jalali_date.month}"
        already_sent_this_month = False
        
        try:
            if os.path.exists(monthly_log_file):
                with open(monthly_log_file, 'r') as f:
                    last_sent_month = f.read().strip()
                    already_sent_this_month = (last_sent_month == month_str)
        except Exception as e:
            print(f"خطا در خواندن فایل لاگ ماهانه: {str(e)}")
        
        # اگر این ماه پیام ارسال نشده، ارسال کن
        if not already_sent_this_month:
            # فراخوانی API برای گزارش ماهانه
            _call_sms_report_api("monthly_Motivation")
            
            # ذخیره ماه ارسال
            try:
                with open(monthly_log_file, 'w') as f:
                    f.write(month_str)
                print(f"تاریخ ارسال پیام ماهانه ذخیره شد: {month_str}")
            except Exception as e:
                print(f"خطا در ذخیره تاریخ ارسال پیام ماهانه: {str(e)}")


def _call_sms_report_api(report_type):
    """
    فراخوانی API ارسال گزارش SMS
    
    Args:
        report_type (str): نوع گزارش (Daily_Motivation, weekly_Motivation, monthly_Motivation)
    """
    # آدرس API
    api_url = f"https://timee.nwhco.ir/api/activity/sendSmsReport"
    
    # پارامترهای درخواست
    params = {
        'token': 'uy4844qwiweqe48dgrt5fdfdfdf5',
        'type': report_type
    }
    
    try:
        # فراخوانی API
        response = requests.get(api_url, params=params)
        
        # بررسی پاسخ
        if response.status_code == 200:
            print(f"گزارش {report_type} با موفقیت ارسال شد: {response.json()}")
        else:
            print(f"خطا در ارسال گزارش {report_type}: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"خطا در فراخوانی API ارسال گزارش {report_type}: {str(e)}")
        
if __name__ == "__main__":
    print("Executed at:", datetime.now())
    send_sms_report_based_on_date()
    # auto_close_inactive_time_entries()
