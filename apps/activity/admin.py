import jdatetime
from datetime import timedelta, datetime
from django.utils.translation import gettext_lazy as _
from django.contrib.admin import SimpleListFilter
from django.contrib import admin
from django.utils.html import format_html
from django.db.models import F, ExpressionWrapper, DurationField, Q as models_Q
from django.utils import timezone

from unfold.admin import ModelAdmin
from unfold.decorators import display
from unfold.contrib.filters.admin import AutocompleteSelectFilter

from apps.activity.models import TimeEntry
from utils.admin import project_admin_site


class ShamsiMonthFilter(SimpleListFilter):
    title = _('ماه شمسی')
    parameter_name = 'shamsi_month'

    def lookups(self, request, model_admin):
        months = []
        for month in range(1, 13):
            months.append((month, jdatetime.date(1400, month, 1).strftime('%B')))
        return months

    def queryset(self, request, queryset):
        if self.value():
            month = int(self.value())
            current_date = jdatetime.date.today()
            year = current_date.year if current_date.month >= month else current_date.year - 1
            start_date = jdatetime.date(year, month, 1).togregorian()
            if month == 12:
                end_date = jdatetime.date(year + 1, 1, 1).togregorian()
            else:
                end_date = jdatetime.date(year, month + 1, 1).togregorian()
            return queryset.filter(start_time__gte=start_date, start_time__lt=end_date)
        return queryset


class ShamsiYearFilter(SimpleListFilter):
    title = _('سال شمسی')
    parameter_name = 'shamsi_year'

    def lookups(self, request, model_admin):
        current_year = jdatetime.date.today().year
        years = []
        for year in range(current_year - 2, current_year + 1):
            years.append((year, str(year)))
        return years

    def queryset(self, request, queryset):
        if self.value():
            year = int(self.value())
            start_date = jdatetime.date(year, 1, 1).togregorian()
            end_date = jdatetime.date(year + 1, 1, 1).togregorian()
            return queryset.filter(start_time__gte=start_date, start_time__lt=end_date)
        return queryset


class ProjectTypeFilter(SimpleListFilter):
    title = _('نوع پروژه')
    parameter_name = 'project_type'

    def lookups(self, request, model_admin):
        return TimeEntry.PROJECT_TYPES

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(type=self.value())
        return queryset


@admin.register(TimeEntry)
class TimeEntryAdmin(ModelAdmin):
    list_display = ('employee_display', 'time_period_display', 'duration_display', 'project_goal_display', 'target_goal_display', 'edit_status')
    search_fields = ('employee__full_name', 'project__name', 'report_text')
    list_filter = [('employee', AutocompleteSelectFilter),  ShamsiMonthFilter, ShamsiYearFilter, ProjectTypeFilter]
    
    autocomplete_fields = ['employee']

    ordering = ['-start_time']
    list_per_page = 20
    list_filter_submit = True 
    
    # def get_urls(self):
    #     from django.urls import path
    #     from .views import ActivityAnalysisView
        
    #     urls = super().get_urls()
    #     custom_urls = [
    #         path(
    #             "analysis/",
    #             self.admin_site.admin_view(ActivityAnalysisView.as_view(model_admin=self)),
    #             name="activity_analysis",
    #         ),
    #     ]
    #     return custom_urls + urls
    # def changelist_view(self, request, extra_context=None):
    #     extra_context = extra_context or {}
    #     if request.GET.get('today') == 'true':
    #         extra_context['title'] = _("Today's Activities")
    #         from django.contrib import messages
    #         today_date = timezone.now().date()
    #         today_jalali = jdatetime.date.fromgregorian(date=today_date)
    #         messages.info(request, _("Showing activities for today ({})").format(today_jalali.strftime('%Y/%m/%d')))
    #     return super().changelist_view(request, extra_context=extra_context)
    # fieldsets = (
    #     (_('Main Information'), {
    #         'fields': ('employee', 'start_time', 'end_time', 'type', 'project')
    #     }),
    #     (_('Jira Information'), {
    #         'fields': ('jira_task_id', 'jira_task_name')
    #     }),
    # )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.annotate(
            duration=ExpressionWrapper(
                F('end_time') - F('start_time'), 
                output_field=DurationField()
            )
        )
        self.request = request  # Store the request for later use
        
        # Filter for today's activities if 'today' parameter is present
        if request.GET.get('today') == 'true':
            today = timezone.now().date()
            today_start = timezone.make_aware(datetime.combine(today, datetime.min.time()))
            today_end = timezone.make_aware(datetime.combine(today, datetime.max.time()))
            
            # Get entries that overlap with today
            qs = qs.filter(
                start_time__lte=today_end,  # Started before end of day
            ).filter(
                # Either ended today or still in progress
                models_Q(end_time__gte=today_start) | models_Q(end_time__isnull=True)
            )
            
        return qs

    @display(description=_("Employee"), ordering="employee__full_name")
    def employee_display(self, obj):
        if obj.employee:
            return format_html(
                '<div class="flex items-center">'
                '<div class="flex-shrink-0 h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 text-sm font-medium mr-2">{}</div>'
                '<div class="text-sm font-medium text-gray-900">{}</div>'
                '</div>',
                obj.employee.full_name[0].upper() if obj.employee.full_name else '?',
                obj.employee.full_name
            )
        return "-"

    @display(description=_("Time Period"), ordering="start_time")
    def time_period_display(self, obj):
        start_jalali = jdatetime.datetime.fromgregorian(datetime=obj.start_time)
        
        if obj.end_time:
            end_jalali = jdatetime.datetime.fromgregorian(datetime=obj.end_time)
            return format_html(
                '<div class="flex flex-col">'
                '<span class="text-sm font-medium text-gray-900">{} to {}</span>'
                '<span class="text-xs text-gray-500">{}</span>'
                '</div>',
                start_jalali.strftime('%H:%M'),
                end_jalali.strftime('%H:%M'),
                start_jalali.strftime('%Y/%m/%d')
            )
        else:
            return format_html(
                '<div class="flex flex-col">'
                '<span class="text-sm font-medium text-gray-900">{} to <span class="text-green-500">In Progress</span></span>'
                '<span class="text-xs text-gray-500">{}</span>'
                '</div>',
                start_jalali.strftime('%H:%M'),
                start_jalali.strftime('%Y/%m/%d')
            )

    @display(description=_("Duration"), ordering="duration")
    def duration_display(self, obj):
        if obj.end_time:
            duration = obj.end_time - obj.start_time
            hours = int(duration.total_seconds() // 3600)
            minutes = int((duration.total_seconds() % 3600) // 60)
            
            # Create time string before using in format_html
            time_str = f"{hours}:{minutes:02d}"
            
            return format_html(
                '<div class="px-2 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800 shadow-sm">'
                '{}'
                '</div>',
                time_str
            )
        elif obj.start_time:
            # In progress
            duration = timezone.now() - obj.start_time
            hours = int(duration.total_seconds() // 3600)
            minutes = int((duration.total_seconds() % 3600) // 60)
            
            # Create time string before using in format_html
            time_str = f"{hours}:{minutes:02d}"
            
            return format_html(
                '<div class="px-2 py-1 inline-flex text-sm leading-5 font-semibold rounded-full shadow-sm">'
                '{} <span class="ml-1 animate-pulse">●</span>'
                '</div>',
                time_str
            )
        return "-"

    @display(description=_("Project"))
    def project_display(self, obj):
        if obj.project:
            return format_html(
                '<div class="px-2 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800 shadow-sm">'
                '{}'
                '</div>',
                obj.project.name
            )
        return "-"

    @display(description=_("Project Goal"))
    def project_goal_display(self, obj):
        if obj.project_goal:
            return format_html(
                '<div class="px-2 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-green-100 text-green-800 shadow-sm">'
                '{}'
                '</div>',
                obj.project_goal.name
            )
        return "-"

    @display(description=_("Target Goal"))
    def target_goal_display(self, obj):
        if obj.target_goal:
            return format_html(
                '<div class="px-2 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 shadow-sm">'
                '{}'
                '</div>',
                obj.target_goal.title
            )
        return "-"

    @display(description=_("Type"))
    def type_display(self, obj):

        return format_html(
            '<div class="px-2 py-1 inline-flex  text-sm leading-5 font-semibold rounded-full  shadow-sm">'
            '{}'
            '</div>',
            obj.type,
        )

    @display(description=_("Platform"))
    def platform_display(self, obj):
        if obj.platform:
            return format_html(
                '<div class="px-2 py-1 inline-flex text-sm leading-5 font-semibold rounded-full bg-purple-100 text-purple-800 shadow-sm">'
                '{}'
                '</div>',
                obj.platform
            )
        return "-"



# Register the model with the project admin site
project_admin_site.register(TimeEntry, TimeEntryAdmin)
