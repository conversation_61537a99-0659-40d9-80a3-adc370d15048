from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from django.utils import timezone

from apps.activity.models import TimeEntry


@receiver(pre_save, sender=TimeEntry)
def update_times_on_status_change(sender, instance, **kwargs):
    # ابتدا بررسی می‌کنیم که آیا وضعیت تغییر کرده است یا خیر
    if instance.pk:
        # فچ کردن مقدار قبلی مدل
        previous_instance = TimeEntry.objects.get(pk=instance.pk)

        # بررسی اینکه آیا وضعیت از پندینگ به اپروود تغییر کرده است
        if (previous_instance.edit_status == 'PENDING' or previous_instance.edit_status == 'REJECTED') and instance.edit_status == 'APPROVED':
            # جایگزینی تایم‌های پیشنهادی به جای تایم‌های اصلی
            if instance.proposed_start_time and instance.proposed_end_time:
                instance.start_time = instance.proposed_start_time
                instance.end_time = instance.proposed_end_time

                # افزودن لاگ به زبان فارسی
                now = timezone.now()
                instance.edit_log += f"\n{now}: زمان‌ها با موفقیت به‌روزرسانی شد. زمان شروع: {instance.start_time}, زمان پایان: {instance.end_time}\n"
            else:
                # اگر تایم‌های پیشنهادی خالی باشد
                instance.edit_log = f"{timezone.now()}: تلاش برای تایید اما زمان‌های پیشنهادی خالی بود.\n"



# @receiver(post_save, sender=TimeEntry)
# def set_end_time_and_report_text(sender, instance, created, **kwargs):
#     if created and instance.employee.id == 31:
#         instance.end_time = timezone.now()
#         instance.report_text = 'test'
#         instance.save()
