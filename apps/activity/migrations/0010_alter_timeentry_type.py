# Generated by Django 5.2.1 on 2025-07-11 23:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('activity', '0009_timeentry_device_name_timeentry_device_os_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='timeentry',
            name='type',
            field=models.CharField(blank=True, choices=[('COMPANY', 'At Company'), ('HOME', 'At Home')], max_length=32, null=True),
        ),
    ]
