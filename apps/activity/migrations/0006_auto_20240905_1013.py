# Generated by Django 3.2.17 on 2024-09-05 10:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('activity', '0005_timeentry_jira_task_name'),
    ]

    operations = [
        migrations.AddField(
            model_name='timeentry',
            name='edit_log',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='edit_reason',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='edit_status',
            field=models.CharField(choices=[('NONE', 'None'), ('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')], default='NONE', max_length=10),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='proposed_end_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='timeentry',
            name='proposed_start_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
