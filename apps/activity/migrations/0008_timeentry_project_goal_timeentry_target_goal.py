# Generated by Django 5.2.1 on 2025-07-11 16:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('activity', '0007_auto_20241019_1020'),
        ('goals', '0004_remove_comment_topic_project_comment_project'),
    ]

    operations = [
        migrations.AddField(
            model_name='timeentry',
            name='project_goal',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='time_entries', to='goals.project', verbose_name='پروژه هدف'),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='target_goal',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='time_entries', to='goals.target', verbose_name='هدف'),
        ),
    ]
