# Generated by Django 5.2.1 on 2025-07-11 23:27

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('activity', '0008_timeentry_project_goal_timeentry_target_goal'),
    ]

    operations = [
        migrations.AddField(
            model_name='timeentry',
            name='device_name',
            field=models.CharField(blank=True, max_length=200, null=True, verbose_name='نام دستگاه'),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='device_os',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='سیستم عامل دستگاه'),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='platform',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='پلتفرم'),
        ),
        migrations.AddField(
            model_name='timeentry',
            name='system_local_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='زمان محلی سیستم'),
        ),
    ]
