# Generated by Django 3.2.17 on 2024-03-07 11:59

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employee', '0004_remove_employee_user'),
        ('activity', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='timeentry',
            name='jira_task_id',
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='timeentry',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='employee.employee'),
        ),
        migrations.AlterField(
            model_name='timeentry',
            name='end_time',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='timeentry',
            name='price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=8, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='timeentry',
            name='project',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=200, null=True),
        ),
    ]
