from django.db import models


class TimeEntry(models.Model):
    PROJECT_TYPES = (
        ('COMPANY', 'At Company'),
        ('HOME', 'At Home'),
    )
    EDIT_STATUS_CHOICES = (
        ('NONE', 'None'),
        ('PENDING', 'Pending Approval'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
    )

    start_time = models.DateTimeField()
    last_ready = models.IntegerField(null=True, blank=True)
    end_time = models.DateTimeField(null=True, blank=True)

    proposed_start_time = models.DateTimeField(null=True, blank=True)
    proposed_end_time = models.DateTimeField(null=True, blank=True)

    edit_status = models.CharField(max_length=10, choices=EDIT_STATUS_CHOICES, default='NONE')
    edit_reason = models.TextField(null=True, blank=True)
    edit_log = models.TextField(null=True, blank=True)

    price = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    employee = models.ForeignKey('employee.Employee', on_delete=models.CASCADE)
    project = models.ForeignKey('projects.Projects', on_delete=models.CASCADE, null=True, blank=True)
    type = models.CharField(max_length=32, choices=PROJECT_TYPES, null=True, blank=True)
    reports_audios = models.FileField(upload_to='audio_reports/', null=True, blank=True)
    report_text = models.TextField(null=True, blank=True)
    report_score = models.IntegerField(null=True, blank=True)
    report_improvement_suggestion = models.CharField(max_length=350,null=True,blank=True)

    jira_task_id = models.CharField(max_length=200,null=True,blank=True)
    jira_task_name = models.CharField(max_length=200,null=True,blank=True)

    # Goals relationships
    project_goal = models.ForeignKey('goals.Project', on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name='time_entries', verbose_name='پروژه هدف')
    target_goal = models.ForeignKey('goals.Target', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='time_entries', verbose_name='هدف')

    # Device and system information
    system_local_time = models.DateTimeField(null=True, blank=True, verbose_name='زمان محلی سیستم')
    platform = models.CharField(max_length=100, null=True, blank=True, verbose_name='پلتفرم')
    device_name = models.CharField(max_length=200, null=True, blank=True, verbose_name='نام دستگاه')
    device_os = models.CharField(max_length=100, null=True, blank=True, verbose_name='سیستم عامل دستگاه')

    # Version and submission status
    version = models.IntegerField(null=True, blank=True, verbose_name='ورژن')
    is_activity_submitted = models.BooleanField(default=False, verbose_name='آیا اکتیویتی سابمیت شده')


