from django.urls import path
from apps.activity.views import TimeEntryListView, TimeStartCreateView, TimeUpdateView, TimeEndView, UpdateLastReadyView, EmployeeStatsAPIView, SendSmsReportView, EditTimeEntryView, TimeEntryDetailView


urlpatterns = [
    path('activity/list/', TimeEntryListView.as_view()),
    path('activity/stats/', EmployeeStatsAPIView.as_view(), name='stats'),
    path('activity/start/', TimeStartCreateView.as_view()),
    path('activity/end/', TimeEndView.as_view()),
    path('activity/update/', TimeUpdateView.as_view()),
    path('activity/<int:id>/imready/', UpdateLastReadyView.as_view(), name='update_activity'),
    path('activity/sendSmsReport', SendSmsReportView.as_view(), name='update_activity'),
    path('activity/edit-time/<int:time_entry_id>/', EditTimeEntryView.as_view(), name='edit_time_entry'),
    path('activity/<int:time_entry_id>/', TimeEntryDetailView.as_view(), name='time_entry_detail'),
]
