from datetime import time,datetime

from rest_framework import serializers
from .models import TimeEntry
from ..projects.models import Projects


class TimeEntrySerializer(serializers.ModelSerializer):
    def save(self, **kwargs):
        return super().save(**kwargs)

    class Meta:
        model = TimeEntry
        fields = '__all__'
        extra_kwargs = {
            'start_time': {'required': False},
        }
class CustomTimeEntrySerializer(serializers.ModelSerializer):

    duration = serializers.SerializerMethodField()
    start_time = serializers.SerializerMethodField()
    end_time = serializers.SerializerMethodField()
    project = serializers.SerializerMethodField()


    class Meta:
        model = TimeEntry
        fields = ['id', 'edit_status', 'start_time','end_time','project','price','type','reports_audios','report_text','start_time','jira_task_id','jira_task_name' ,'duration']

    def get_start_time(self, obj):
        if obj.start_time:
            return obj.start_time.strftime("%Y-%m-%d %H:%M:%S")
        else:
            return None

    def get_end_time(self, obj):
        if obj.end_time:
            return obj.end_time.strftime("%Y-%m-%d %H:%M:%S")
        else:
            return None

    def get_duration(self, obj):

        if obj.end_time is not None and obj.start_time is not None:
            # محاسبه مدت زمان بین start_time و end_time و تبدیل به رشته
            duration = obj.end_time - obj.start_time
            hours, remainder = divmod(duration.seconds, 3600)
            minutes, seconds = divmod(remainder, 60)

            return str(f"{hours}:{minutes}:{seconds}")
        else:
            return None
    def get_project(self, obj):
        if obj.project is not None and obj.project is not None:
            project = Projects.objects.filter(id=obj.project.id).first()
            # محاسبه مدت زمان بین start_time و end_time و تبدیل به رشته
            data = {
                'name':project.name,
                'color':project.color_code,
                'icon':project.icon.url,
            }

            return data
        else:
            return None
