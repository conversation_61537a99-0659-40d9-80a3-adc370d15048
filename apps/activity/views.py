import json

import anthropic
import requests
from django.views.decorators.csrf import csrf_exempt
from rest_framework import views, status
from rest_framework.generics import CreateAPIView, ListAPIView
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.http import JsonResponse
from django.views import View
from django.conf import settings
from django.db.models import Q
from datetime import datetime, time, timedelta
import jdatetime
import pytz
from apps.employee.models import Employee, EmployeeTelegramInfo
from apps.permissions import HasTokenPermission
from apps.account.centrifugo import centrifugo_service
from .serializers import TimeEntrySerializer, CustomTimeEntrySerializer
from .models import TimeEntry
from utils.send_sms import send_sms
from utils.send_tg import send_tgbot
from django.utils.timezone import make_aware
import jdatetime

from ..projects.models import Projects
from .docs import (
    employee_stats_swagger,
    time_entry_list_swagger,
    time_start_create_swagger,
    time_end_swagger,
    time_update_swagger,
    update_last_ready_swagger,
    edit_time_entry_swagger,
    time_entry_detail_swagger
)


class CustomPaginationClass(PageNumberPagination):
    page_size = 5




class SendSmsReportView(View):
    @staticmethod
    def get_days_in_month_jalali():
        today_miladi = timezone.now().date()
        today_jalali = jdatetime.date.fromgregorian(date=today_miladi)

        if not 1 <= today_jalali.month <= 12:
            return ""
        if 1 <= today_jalali.month <= 6:
            return 31
        elif 7 <= today_jalali.month <= 11:
            return 30
        else:
            date = jdatetime.date(today_jalali.year, today_jalali.month, 1)
            return 30 if date.isleap() else 29

    def calculate_workdays(self):
        today_miladi = timezone.now().date()
        today_jalali = jdatetime.date.fromgregorian(date=today_miladi)
        total_days = self.get_days_in_month_jalali()

        workdays = 0
        holidays = [holiday.date() for holiday in settings.HOLIDAYS]

        for day in range(1, total_days + 1):
            current_date = jdatetime.date(today_jalali.year, today_jalali.month, day).togregorian()
            if current_date.weekday() != 4 and current_date not in holidays:  # 4 is Friday
                workdays += 1

        return workdays

    def calculate_workdays_until_today(self):
        today_miladi = timezone.now().date()
        today_jalali = jdatetime.date.fromgregorian(date=today_miladi)
        current_day = today_jalali.day

        workdays = 0
        holidays = [holiday.date() for holiday in settings.HOLIDAYS]

        for day in range(1, current_day + 1):
            current_date = jdatetime.date(today_jalali.year, today_jalali.month, day).togregorian()
            if current_date.weekday() != 4 and current_date not in holidays:  # 4 is Friday
                workdays += 1

        return workdays

    def get(self, request):
        today = jdatetime.date.today()
        type = request.GET.get('type')
        now = timezone.now()
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        today_end = today_start + timedelta(days=1) - timedelta(microseconds=1)

        start_of_jalali_month = timezone.make_aware(datetime.combine(
            jdatetime.date(today.year, today.month, 1).togregorian(),
            time.min
        ))

        end_of_jalali_month = timezone.make_aware(datetime.combine(
            jdatetime.date(today.year, today.month, self.get_days_in_month_jalali()).togregorian(),
            time.max
        )) - timedelta(seconds=1)

        if request.GET.get('token') != "uy4844qwiweqe48dgrt5fdfdfdf5":
            return JsonResponse({'status': 'token is mistake.'})
        if (today in settings.HOLIDAYS or today.weekday() == 6) and (
                type == "Daily_Motivation" or type == "Nightly_Deficiency_Report"):
            return JsonResponse({'status': 'today is a holiday, no SMS sent'})

        if type in ["Daily_Motivation", "Nightly_Deficiency_Report"]:
            msg = None
            tg_msg = None
            employees = Employee.objects.filter(
                (Q(commitment_time_per_month__isnull=False) | Q(commitment_time_per_day__isnull=False)) & Q(
                    daily_tracking=True)
            )
            for employee in employees:
                total_time_today = timedelta()
                time_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__gte=today_start,
                    end_time__lte=today_end
                )

                for entry in time_entries:
                    total_time_today += entry.end_time - entry.start_time

                last_ready_time_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__gte=today_start,
                    end_time__isnull=True
                )
                for entry in last_ready_time_entries:
                    total_time_today += timezone.now() - entry.start_time

                time_shortfall = timedelta(0)
                if employee.commitment_time_per_day is not None:
                    # commitment_time_per_day_timedelta = timedelta(hours=employee.commitment_time_per_day)
                    commitment_time_per_day_timedelta = timedelta(hours=float(employee.commitment_time_per_day))

                    time_shortfall = commitment_time_per_day_timedelta - total_time_today
                elif employee.commitment_time_per_month is not None:
                    commitment_time_per_month_timedelta = timedelta(
                        hours=float(employee.commitment_time_per_month) / self.calculate_workdays())
                        # hours=employee.commitment_time_per_month / self.calculate_workdays())
                    time_shortfall = commitment_time_per_month_timedelta - total_time_today

                if time_shortfall > timedelta(0):
                    total_minutes = time_shortfall.total_seconds() // 60
                    hours = total_minutes // 60
                    minutes = total_minutes % 60

                    if type == "Daily_Motivation":
                        msg = f"""یادت نره که {hours:.0f} ساعت و {minutes:.0f} دقیقه از ساعت تعهدی امروزت باقی مونده! 
امیدوارم تا آخر شب جبرانش کنی!"""

                        tg_msg=f"""
                        🔴
                        هشدار روزانه
                        {employee.full_name}
                        {hours:.0f} ساعت و {minutes:.0f} دقیقه
                        کمتر از ساغت تعهدی زمان گذاشته است.
                        """

                    elif type == "Nightly_Deficiency_Report":
                        msg = f"""متاسفانه امروز {hours:.0f} ساعت و {minutes:.0f} دقیقه کمتر از ساعت تعهدی ات وقت گذاشتی."""

                        tg_msg = f"""
                        🔴
                        هشدار شبانه
                        {employee.full_name}
                        {hours:.0f} ساعت و {minutes:.0f} دقیقه
                        کمتر از ساغت تعهدی زمان گذاشته است.
                        """
                        send_tgbot(tg_msg)
                else:
                    total_minutes = total_time_today.total_seconds() // 60
                    hours = total_minutes // 60
                    minutes = total_minutes % 60
                    if type == "Daily_Motivation":
                        tg_msg = f"""
                        🟢
                        اعلان روزانه
                        {employee.full_name}
                        به ساعت تعهدی روزانه وفادار بوده است
                        میزان ساعت امروز:
                        {hours:.0f} ساعت و {minutes:.0f} دقیقه
                        """
                        send_tgbot(tg_msg)

                    elif type == "Nightly_Deficiency_Report":
                        tg_msg = f"""
                        🟢
                        اعلان شبانه
                        {employee.full_name}
                        به ساعت تعهدی روزانه وفادار بوده است
                        میزان ساعت امروز:
                        {hours:.0f} ساعت و {minutes:.0f} دقیقه
                        """
                        send_tgbot(tg_msg)
                if msg is not None:
                    sms = send_sms(employee.phone_number, msg)
                    tg_msg += f"""
                    وضغیت ارسال پیامک:
                    {sms['status']}
                    """
                if tg_msg is not None:
                    send_tgbot(tg_msg)
                
            return JsonResponse({'status': 'messages sent.'})

        if type in ["monthly_Motivation", "monthly_Deficiency_Report"]:
            msg = None
            tg_msg = None
            current_date = jdatetime.date.today()

            if current_date.day != 23 and current_date != jdatetime.date(current_date.year, current_date.month,
                                                                         current_date.daysinmonth):
                return JsonResponse({'status': 'today not 23 or end of month'})

            employees = Employee.objects.filter(
                (Q(commitment_time_per_month__isnull=False) | Q(commitment_time_per_day__isnull=False)) & Q(
                    monthly_tracking=True)
            )
            for employee in employees:
                total_time_month = timedelta()
                time_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__gte=start_of_jalali_month,
                    end_time__lte=end_of_jalali_month
                )

                for entry in time_entries:
                    total_time_month += entry.end_time - entry.start_time

                last_ready_time_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__gte=start_of_jalali_month,
                    end_time__isnull=True
                )
                for entry in last_ready_time_entries:
                    total_time_month += timezone.now() - entry.start_time

                time_shortfall = timedelta(0)
                commitment_time_per_month_timedelta = None
                if employee.commitment_time_per_day is not None:
                    commitment_time_per_month_timedelta = timedelta(
                        hours=float(employee.commitment_time_per_day) * self.calculate_workdays_until_today())
                    time_shortfall = commitment_time_per_month_timedelta - total_time_month
                elif employee.commitment_time_per_month is not None:
                    commitment_time_per_month_timedelta = timedelta(
                        hours=(float(employee.commitment_time_per_month) / self.calculate_workdays()) * self.calculate_workdays_until_today())
                    time_shortfall = commitment_time_per_month_timedelta - total_time_month

                if time_shortfall > timedelta(0):
                    if type == "monthly_Motivation":
                        msg = f"""شما تا اینجای ماه {time_shortfall.total_seconds() // 3600:.0f} ساعت کسری ساعت تعهدی دارید.
فقط یک هفته تا پایان ماه کاری باقی مونده، حواست باشه که ساعات تعهدی ماهانه ات را تکمیل کنی!"""

                        tg_msg = f"""
                        🔴🔴🔴
                        هشدار نیمه ماه
                        {employee.full_name}
                        {time_shortfall.total_seconds() // 3600:.0f}
                        کمتر از ساغت تعهدی ماهانه زمان گذاشته است.
                        ساعت تعهدی ماهانه:
                        {commitment_time_per_month_timedelta}
                        """

                    elif type == "monthly_Deficiency_Report":
                        msg = f"""متاسفانه شما این ماه {time_shortfall.total_seconds() // 3600:.0f} ساعت کسری داشتی.
لطفاً در این رابطه با مسئول منابع انسانی شرکت گفتگو کن."""

                        tg_msg = f"""
                        🔴🔴🔴
                        هشدار پایان ماه
                        {employee.full_name}
                        {time_shortfall.total_seconds() // 3600:.0f}
                        کمتر از ساغت تعهدی ماهانه زمان گذاشته است.
                        ساحت تعهدی ماهانه:
                        {commitment_time_per_month_timedelta}
                        """


                else:
                    if type == "monthly_Motivation":
                        msg = "تبریک میگم شما تا اینجای ماه هیچ کسری ساعت تعهدی ای نداشتی."

                        tg_msg = f"""
                        🟢🟢🟢
                        اعلان نیمه ماه
                        {employee.full_name}
                        به میزان ساعت تعهدی وفادار بوده است.
                        مقدار ساعت ماه:
                        {total_time_month.total_seconds() // 3600:.0f}
                        """


                    elif type == "monthly_Deficiency_Report":
                        msg = "تبریک میگم شما این ماه هیچ کسری ساعت تعهدی ای نداشتی."

                        tg_msg = f"""
                        🟢🟢🟢
                        اعلان پایان ماه
                        {employee.full_name}
                        به میزان ساعت تعهدی وفادار بوده است.
                        مقدار ساعت ماه:
                        {total_time_month.total_seconds() // 3600:.0f}
                        """

                if msg is not None:
                    sms = send_sms(employee.phone_number, msg)
                    tg_msg += f"""
                    وضغیت ارسال پیامک:
                    {sms['status']}
                    """
                if tg_msg is not None:
                    send_tgbot(tg_msg)
            return JsonResponse({'status': 'messages sent.'})

        if type in ["weekly_Motivation", "weekly_Deficiency_Report"]:
            msg = None
            tg_msg = None
            employees = Employee.objects.filter(
                (Q(commitment_time_per_month__isnull=False) | Q(commitment_time_per_day__isnull=False)) &
                Q(weekly_tracking=True)
            )
            for employee in employees:
                total_time_month = timedelta()
                time_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__gte=start_of_jalali_month,
                    end_time__lte=end_of_jalali_month
                )

                for entry in time_entries:
                    total_time_month += entry.end_time - entry.start_time

                last_ready_time_entries = TimeEntry.objects.filter(
                    employee=employee,
                    start_time__gte=start_of_jalali_month,
                    end_time__isnull=True
                )
                for entry in last_ready_time_entries:
                    total_time_month += timezone.now() - entry.start_time

                time_shortfall = timedelta(0)
                commitment_time_per_month_timedelta = None
                if employee.commitment_time_per_day is not None:
                    commitment_time_per_month_timedelta = timedelta(
                        hours=float(employee.commitment_time_per_day) * self.calculate_workdays_until_today())
                    time_shortfall = commitment_time_per_month_timedelta - total_time_month
                elif employee.commitment_time_per_month is not None:
                    commitment_time_per_month_timedelta = timedelta(
                        hours=(float(employee.commitment_time_per_month) / self.calculate_workdays()) * self.calculate_workdays_until_today())
                    time_shortfall = commitment_time_per_month_timedelta - total_time_month

                if time_shortfall > timedelta(0):
                    if type == "weekly_Motivation":
                        msg = f"""تا اینحا، شما، {time_shortfall.total_seconds() // 3600:.0f} ساعت برای این ماه کسری دارید.
لطفا برای جبرانش تا آخر هفته برنامه ریزی کن."""

                        tg_msg=f"""
                        🔴🔴
                        هشدار میان هفتگی
                        {employee.full_name}
                        {time_shortfall.total_seconds() // 3600:.0f}
                        کمتر از ساغت تعهدی ماه زمان گذاشته است.
                        ساحت تعهدی ماهانه:
                        {commitment_time_per_month_timedelta}
                        """


                    elif type == "weekly_Deficiency_Report":
                        msg = f"""متاسفانه شما این هفته {time_shortfall.total_seconds() // 3600:.0f} ساعت کمتر از تعهد زمانی خودتون فعالیت داشتین."""

                        tg_msg=f"""
                        🔴🔴
                        هشدار پایان هفته
                        {employee.full_name}
                        {time_shortfall.total_seconds() // 3600:.0f}
                        کمتر از ساغت تعهدی ماه زمان گذاشته است.
                        ساحت تعهدی ماهانه:
                        {commitment_time_per_month_timedelta}
                        """

                    if msg is not None:
                        sms = send_sms(employee.phone_number, msg)
                        tg_msg += f"""
                        وضغیت ارسال پیامک:
                        {sms['status']}
                        """
                    if tg_msg is not None:
                        send_tgbot(tg_msg)

            return JsonResponse({'status': 'messages sent.'})

        return JsonResponse({'status': 'Specify the type.'})

# def sendSmsReportView():





def get_current_jalali_month_start():
    today = jdatetime.date.fromgregorian(date=timezone.now().date())
    return jdatetime.date(today.year, today.month, 1).togregorian()


def get_today_hours(employee):
    today_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0)
    today_end = today_start + timedelta(days=1)
    trackings_today = TimeEntry.objects.filter(start_time__gte=today_start, end_time__lt=today_end, employee=employee)

    employee_hours = {}
    for tracking in trackings_today:
        if tracking.end_time:
            duration_hours = (tracking.end_time - tracking.start_time).total_seconds() / 3600
            if tracking.employee_id in employee_hours:
                employee_hours[tracking.employee_id] += duration_hours
            else:
                employee_hours[tracking.employee_id] = duration_hours

    return employee_hours


def get_stats(employee):
    start_of_month = get_current_jalali_month_start()
    trackings = TimeEntry.objects.filter(employee=employee, start_time__gte=start_of_month)
    print(f'-0--> P{trackings}')
    total_duration = sum(
        (tracking.end_time - tracking.start_time).total_seconds() / 3600 for tracking in trackings if tracking.end_time)
    print(f'-1-->{total_duration}')
    total_income = sum(tracking.price for tracking in trackings if tracking.price)
    total_duration = round(total_duration, 1)
    if total_income / 1000000 < 1:
        total_income = round(total_income / 1000000, 3)
    else:
        total_income = round(total_income / 1000000, 2)
    print(f'-2-->{total_duration}')
    stats = [
        {'title': 'Hours This Month', 'slug': 'hours_this_month', 'icon': 'fa-clock', 'value': total_duration},
        {'title': 'Income This Month', 'slug': 'income_this_month', 'icon': 'fa-dollar-sign', 'value': total_income}
    ]

    today_hours = get_today_hours(employee).get(employee.id, 0)
    commitment_deficit = None

    if employee.commitment_time_per_day:
        commitment_deficit = round(float(employee.commitment_time_per_day) - today_hours, 1)
        stats.append({'title': 'Missing Hours Today', 'slug': 'missing_hours_today', 'icon': 'fa-frown-open', 'value': commitment_deficit})
        stats.append({'title': 'Your Daily Commitment', 'slug': 'your_daily_commitment', 'icon': 'fa-calendar-alt', 'value': employee.commitment_time_per_day})
    elif employee.commitment_time_per_month:
        commitment_deficit = round(float(employee.commitment_time_per_month) - total_duration, 1)
        commitment_day_deficit = round((float(employee.commitment_time_per_month) / 26) - today_hours, 1)

        stats.append({'title': 'Missing Hours Today', 'slug': 'missing_hours_today', 'icon': 'fa-frown-open', 'value': commitment_day_deficit})
        stats.append({'title': 'کمبود ساعت تعهدی ماهانه', 'slug': 'missing_hours_monthly', 'icon': 'fa-frown-open', 'value': commitment_deficit})
        stats.append({'title': 'Your Daily Commitment', 'slug': 'your_daily_commitment', 'icon': 'fa-calendar-alt', 'value': employee.commitment_time_per_month})

    return stats


class EmployeeStatsAPIView(APIView):
    @employee_stats_swagger
    def get(self, request):
        # request_token = request.headers.get('X-Api-Key')
        # employee = get_object_or_404(Employee, token=request_token)
        employee = Employee.objects.get(id=31)
        stats = get_stats(employee)
        print(f'--EmployeeStatsAPIView-> {stats}')
        return Response(stats)


class TimeEntryListView(ListAPIView):
    serializer_class = CustomTimeEntrySerializer
    pagination_class = CustomPaginationClass
    permission_classes = [HasTokenPermission]

    def get_queryset(self):
        request_token = self.request.headers.get('X-Api-Key')
        employee = Employee.objects.get(token=request_token)
        # Filter entries where end_time is not null
        return TimeEntry.objects.filter(employee=employee, end_time__isnull=False).order_by('-id')

    @time_entry_list_swagger
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        page = self.paginate_queryset(queryset)
        serializer = self.get_serializer(page, many=True)
        return Response(serializer.data)

class TimeStartCreateView(CreateAPIView):
    permission_classes = [HasTokenPermission]

    @time_start_create_swagger
    def post(self, request, *args, **kwargs):
        print(f'--time_start--> {request.data}')
        data = request.data.copy()
        data['start_time'] = timezone.now()

        request_token = request.headers.get('X-Api-Key')
        employee = Employee.objects.filter(token=request_token).first()
        data['employee'] = employee.id

        serializer = TimeEntrySerializer(data=data)
        activity = TimeEntry.objects.filter(employee=employee, end_time=None, version=request.headers.get('version', None)).exists()
        print(('activity ' + str(activity)))

        if serializer.is_valid() and not activity:
            serializer.save()

            # Send Centrifugo notification that employee came online
            try:
                centrifugo_service.notify_user_online(employee.id)
                print(f'--centrifugo-notification-sent-> Employee {employee.full_name} came online')
            except Exception as e:
                print(f'--centrifugo-notification-error-> {str(e)}')

            webhook_url = 'https://api.telegram.org/bot6696790003:AAGECPM0R5BnHfbCZRUsvzF9LKdeoI-7N_g/sendMessage'
            d = {
                'chat_id': '-1001978915256',
                'text': f'''🟢{employee.full_name}''',
            }
            # requests.post(webhook_url, data=d)
            print(f'--time_start-res-> {serializer.data}')
            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)



def validate_report_text(report_text, employee):
    # اگر employee دارای report_validation_rules باشد، از آن استفاده کن
    if employee.report_validation_rules and employee.report_validation_rules.strip():
        custom_prompt = employee.report_validation_rules.strip()
        print(f"Using custom validation rules for employee {employee.id}: {custom_prompt}")
    else:
        # در غیر این صورت از پرامپت پیش‌فرض استفاده کن
        custom_prompt = """Evaluate the employee's work hour report and assign a score out of 100.

Scoring Criteria:

Higher scores (70-100) will be given to reports that are detailed and provide clear descriptions of both the quantity and quality of the work done.

If the report lacks details, assign the minimum acceptable score (20).

Lower scores (0-15) will be given to vague or general reports, such as:
'Bug fixes' without specifying what the bug was or if it was fully resolved.
'Image creation' without indicating how many images were created or which project/story they belong to.
'Project implementation' without stating how much of the project has been completed or how much remains.
'Meeting with ... ' without describing the meeting topic and summarizing the outcomes.
'Working on UI for Project ...' without specifying which pages, how many pages, or what remains to be done.

Time spent on tasks does not need to be included in the report for this evaluation.

Action Based on Score:

Score < 10: Return False and briefly explain (in 15 words or less) why the report is inadequate.
Score 15-70: Return True with a suggestion (in 15 words or less) for improving the report's clarity or detail.

Output Format (in JSON):
{
  "score": 0,
  "status": "True/False",
  "improvement_suggestion": "string"
}
Only return the final JSON without any additional explanation or text, and always return "improvement_suggestion" in Persian."""

    # برای تست، فعلاً نتیجه ثابت برگردان
    return {
        "score": 80,
        "status": "True",
        "improvement_suggestion": f""
    }

    client = anthropic.Anthropic(
        api_key="************************************************************************************************************",
    )
    safe_report_text = report_text.replace("{", "{{").replace("}", "}}")
    print(safe_report_text)

    prompt = f"""Evaluate the employee's work hour report and assign a score out of 100.

Scoring Criteria:

Higher scores (70-100) will be given to reports that are detailed and provide clear descriptions of both the quantity and quality of the work done.

If the report lacks details, assign the minimum acceptable score (20).

Lower scores (0-15) will be given to vague or general reports, such as:
'Bug fixes' without specifying what the bug was or if it was fully resolved.
'Image creation' without indicating how many images were created or which project/story they belong to.
'Project implementation' without stating how much of the project has been completed or how much remains.
'Meeting with ... ' without describing the meeting topic and summarizing the outcomes.
'Working on UI for Project ...' without specifying which pages, how many pages, or what remains to be done.

Time spent on tasks does not need to be included in the report for this evaluation.

Action Based on Score:

Score < 10: Return False and briefly explain (in 15 words or less) why the report is inadequate.
Score 15-70: Return True with a suggestion (in 15 words or less) for improving the report’s clarity or detail.

Output Format (in JSON):
{{
  "score": 0,
  "status": "True/False",
  "improvement_suggestion": "string"
}}
Only return the final JSON without any additional explanation or text, and always return "improvement_suggestion" in Persian.

Report: {safe_report_text}"""



    print(prompt)

    try:
        # ارسال درخواست به Claude
        response = client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=1024,
            messages=[
                {"role": "user", "content": prompt}
            ]
        )

        # پاسخ Claude به صورت JSON
        answer = response.content[0].text

        # پاسخ JSON را به دیکشنری پایتون تبدیل می‌کنیم
        result = json.loads(answer)

        # بررسی اعتبارسنجی و return نهایی
        return {
            "score": result.get("score", 0),
            "status": result.get("status", "False"),
            "improvement_suggestion": result.get("improvement_suggestion", "")
        }

    except Exception as e:
        return {
            "score": 0,
            "status": "False",
            "improvement_suggestion": f"Error occurred: {str(e)}"
        }


class TimeEndView(views.APIView):
    permission_classes = [HasTokenPermission]

    @time_end_swagger
    def put(self, request):
        print(f'--time_end--> {request.data}')
        request_token = request.headers.get('X-Api-Key')
        employee = Employee.objects.filter(token=request_token).first()

        # Check for version header
        version_header = request.headers.get('version')

        # Handle different field names based on version header
        if version_header:
            # Use activity_id instead of id if version header exists
            activity_id = request.data.get('activity_id') or request.data.get('id')
            if activity_id:
                instance = TimeEntry.objects.filter(employee=employee, id=activity_id).first()
            else:
                instance = TimeEntry.objects.filter(employee=employee).order_by('-id').first()

            # Handle field mappings for version header
            if 'notes' in request.data:
                request.data['report_text'] = request.data['notes']

            if 'project_id' in request.data:
                request.data['project_goal'] = request.data['project_id']

            if 'task_id' in request.data:
                request.data['target_goal'] = request.data['task_id']

            # Set version field
            request.data['version'] = version_header

            # Handle is_activity_submitted logic
            notes_value = request.data.get('notes', '')
            project_id_value = request.data.get('project_id')

            if not notes_value and not project_id_value:
                request.data['is_activity_submitted'] = False
            else:
                request.data['is_activity_submitted'] = True
        else:
            # Original logic for non-version requests
            if request.data.get('id'):
                instance = TimeEntry.objects.filter(employee=employee, id=request.data.get('id')).first()
            else:
                instance = TimeEntry.objects.filter(employee=employee).order_by('-id').first()

        request.data['employee'] = employee.id
        print(request.data)
        imready_error_logs_count = int(request.data.get('imready_error_logs_count', 0))
        extra_time = imready_error_logs_count * 10
        print(request.data.get('edit_post'))

        if 'jira_task' in request.data:
            jira_task = request.data['jira_task']
            if jira_task:
                jira_task_parts = jira_task.split("***")
                if len(jira_task_parts) > 1:
                    request.data['jira_task_name'] = jira_task_parts[0]
                    request.data['jira_task_id'] = jira_task_parts[1]


        # Check if we should set end_time: either edit_post is True OR version header is present
        should_set_end_time = (request.data.get('edit_post') == True) or (version_header is not None)

        if instance.last_ready is not None and should_set_end_time:            
            report_text = request.data.get('report_text')
            if report_text:
                # اعتبارسنجی گزارش از طریق Claude
                validation_result = validate_report_text(report_text)

                # بررسی نتیجه اعتبارسنجی


                if validation_result['status'] == 'False':
                    return Response({'error': validation_result['improvement_suggestion']}, status=400)

                if validation_result['status'] == 'True':
                    request.data['report_score'] = validation_result['score']
                    request.data['report_improvement_suggestion'] = validation_result['improvement_suggestion']


            # اگر end_time قبلاً ثبت شده بود، آن را از request.data حذف کنید
            if instance.end_time is not None:
                request.data.pop('end_time', None)
            else:
                timestamp = instance.last_ready + (3.5 * 60 * 60) + 20 + extra_time
                end_time_aware = datetime.utcfromtimestamp(timestamp)
                request.data['end_time'] = end_time_aware


            webhook_url = 'https://api.telegram.org/bot6696790003:AAGECPM0R5BnHfbCZRUsvzF9LKdeoI-7N_g/sendMessage'
            d = {
                'chat_id': '-1001978915256',
                'text': f'''🔴{employee.full_name}''',
            }
            # requests.post(webhook_url, data=d)

        else:
            webhook_url = 'https://api.telegram.org/bot6696790003:AAGECPM0R5BnHfbCZRUsvzF9LKdeoI-7N_g/sendMessage'
            d = {
                    'chat_id': '-1001978915256',
                    'text': f'''🔴{employee.full_name}''',
                }
            # requests.post(webhook_url, data=d)

        serializer = TimeEntrySerializer(instance, data=request.data)
        if serializer.is_valid():
            serializer.save()

            # Send Centrifugo notification that employee went offline (if end_time was set)
            if 'end_time' in request.data and request.data['end_time']:
                try:
                    centrifugo_service.notify_user_offline(employee.id)
                    print(f'--centrifugo-offline-notification-sent-> Employee {employee.full_name} went offline')
                except Exception as e:
                    print(f'--centrifugo-offline-notification-error-> {str(e)}')

            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)

import re
def escape_markdown_v2(text):
    # Handle None or empty text
    if text is None:
        return ''
    # List of characters that need to be escaped in MarkdownV2
    escape_chars = r'\_*[]()~`>#+-=|{}.!'
    # Escape each character in the text
    return re.sub(f'([{re.escape(escape_chars)}])', r'\\\1', str(text))

class TimeUpdateView(views.APIView):
    permission_classes = [HasTokenPermission]

    @time_update_swagger
    def put(self, request):
        print(f'---time_update--> {request.data}')
        request_token = request.headers.get('X-Api-Key')
        employee = Employee.objects.filter(token=request_token).first()

        # Check for version header
        version_header = request.headers.get('version')

        # Handle different field names based on version header
        if version_header:
            # Use activity_id instead of id if version header exists
            activity_id = request.data.get('activity_id') or request.data.get('id')
            if activity_id:
                instance = TimeEntry.objects.filter(employee=employee, id=activity_id).first()
            else:
                instance = TimeEntry.objects.filter(employee=employee).order_by('-id').first()

            # Handle field mappings for version header
            if 'notes' in request.data:
                request.data['report_text'] = request.data['notes']

            if 'project_id' in request.data:
                request.data['project_goal'] = request.data['project_id']

            if 'task_id' in request.data:
                request.data['target_goal'] = request.data['task_id']

            # Set version field
            request.data['version'] = version_header

            # For TimeUpdateView, always set is_activity_submitted to True when version header exists
            request.data['is_activity_submitted'] = True
        else:
            # Original logic for non-version requests
            if request.data.get('id'):
                instance = TimeEntry.objects.filter(employee=employee, id=request.data.get('id')).first()
            else:
                instance = TimeEntry.objects.filter(employee=employee).order_by('-id').first()

        request.data['employee'] = employee.id
        print(request.data)
        imready_error_logs_count = int(request.data.get('imready_error_logs_count', 0))
        extra_time = imready_error_logs_count * 10
        print(request.data.get('edit_post'))

        if 'jira_task' in request.data:
            jira_task = request.data['jira_task']
            if jira_task:
                jira_task_parts = jira_task.split("***")
                if len(jira_task_parts) > 1:
                    request.data['jira_task_name'] = jira_task_parts[0]
                    request.data['jira_task_id'] = jira_task_parts[1]

        full_name_hashtag = employee.full_name.replace(" ", "_").replace(".", "_")
        full_name_hashtag = f"#{full_name_hashtag}"  # اضافه کردن # به اسلاگ و escape کردن آن

        # Check if we should set end_time: either edit_post is True OR version header is present
        should_set_end_time = (request.data.get('edit_post') == True) or (version_header is not None)

        if instance.last_ready is not None and should_set_end_time:
            report_text = request.data.get('report_text')
            start_time = instance.start_time.astimezone().strftime('%H:%M')  # تبدیل به زمان محلی و فرمت‌بندی
            # end_time_aware = datetime.utcfromtimestamp(timestamp).astimezone()  # تبدیل به زمان محلی
            
            if report_text:
                # اعتبارسنجی گزارش از طریق Claude
                validation_result = validate_report_text(report_text,employee)

                # بررسی نتیجه اعتبارسنجی
                if validation_result['status'] == 'False':
                    return Response({'error': validation_result['improvement_suggestion']}, status=400)

                if validation_result['status'] == 'True':
                    request.data['report_score'] = validation_result['score']
                    request.data['report_improvement_suggestion'] = validation_result['improvement_suggestion']

            timestamp = instance.last_ready + (3.5 * 60 * 60) + 20 + extra_time

            # تبدیل end_time_aware به تاریخ شمسی به صورت عددی
            end_time_aware = datetime.utcfromtimestamp(timestamp)

            request.data['end_time'] = end_time_aware

            end_time_shamsi = jdatetime.datetime.fromgregorian(datetime=end_time_aware)

            # فرمت عددی تاریخ شمسی به صورت 1403/09/23
            formatted_date = end_time_shamsi.strftime('%Y/%m/%d')

            # اضافه کردن تاریخ به request.data
            request.data['date'] = formatted_date

            duration = (end_time_aware - instance.start_time).total_seconds() / 3600
            hours = int(duration)  # تعداد ساعت ها
            minutes = int((duration * 60) % 60)  # تبدیل زمان به دقیقه و گرفتن باقیمانده
            seconds = int((duration * 3600) % 60)  # تبدیل زمان به ثانیه و گرفتن باقیمانده

            # چاپ فرمت شده
            formatted_duration = f"{hours:02}:{minutes:02}:{seconds:02}"
            duration = formatted_duration

            # زمان شروع و پایان را به فرمت مورد نظر تبدیل کنید
            start_time_formatted = instance.start_time.astimezone().strftime('%H:%M')
            end_time_formatted = end_time_aware.strftime('%H:%M')

            # اضافه کردن زمان شروع و پایان به پیام
            message = f'''{escape_markdown_v2(full_name_hashtag)}\n 👤 {escape_markdown_v2(employee.full_name)}\n📅 Date: {escape_markdown_v2(formatted_date)}\n🕔 Duration: {escape_markdown_v2(duration)}\n⏰ Start Time: {escape_markdown_v2(start_time_formatted)}\n⏱️ End Time: {escape_markdown_v2(end_time_formatted)}\n📝 Report:\n{escape_markdown_v2(request.data.get('report_text'))}\n🔗 [Click here to view detailed activity report 📊](https://timee\\.nwhco\\.ir/admin/employee/employee/export/{employee.id}/)'''

            chat_id = '-1002420766681'

        else:
            duration = None
            message = f'''{escape_markdown_v2(full_name_hashtag)}\n 👤 {escape_markdown_v2(employee.full_name)}\n\n📝 Planned tasks:\n{escape_markdown_v2(request.data.get('report_text'))}'''
            request.data['end_time'] = None
            chat_id = '-1001978915256'
        serializer = TimeEntrySerializer(instance, data=request.data)
        # message = "slam"
        if serializer.is_valid():
            serializer.save()

            webhook_url = 'https://api.telegram.org/bot6696790003:AAGECPM0R5BnHfbCZRUsvzF9LKdeoI-7N_g/sendMessage'
            d = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'MarkdownV2', 
            }
            print(f'---> {d}')
            try:
                response = requests.post(webhook_url, data=d)
                if response.status_code != 200:
                    print(f"Telegram API Error: {response.status_code} - {response.text}")
            
            except Exception as e:
                print(f"Error sending message to Telegram: {e}")
            if instance.last_ready is not None and request.data.get('edit_post') == True:
                try:
                    for telegram_info in EmployeeTelegramInfo.objects.filter(employees=employee, is_active=True):
                        webhook_url = 'https://api.telegram.org/bot6696790003:AAGECPM0R5BnHfbCZRUsvzF9LKdeoI-7N_g/sendMessage'
                        d = {
                            'chat_id': telegram_info.chat_id,
                            'text': message,
                            'parse_mode': 'MarkdownV2',
                        }

                        # requests.post(webhook_url, data=d)
                except Exception as e:
                    print(f"Error sending message to Telegram: {e}")

            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)






class UpdateLastReadyView(View):
    serializer_class = CustomTimeEntrySerializer
    permission_classes = [HasTokenPermission]

    @update_last_ready_swagger
    def get(self, request, id):
        request_token = request.headers.get('X-Api-Key')
        employee = Employee.objects.filter(token=request_token).first()
        activity = TimeEntry.objects.filter(employee=employee, id=id).first()
        if activity:
            try:
                if activity.end_time is None:
                    tehran_timezone = pytz.FixedOffset(3.5 * 60)
                    now_utc = timezone.now()
                    now_tehran = now_utc.astimezone(tehran_timezone)
                    activity.last_ready = int(now_tehran.timestamp())
                    activity.save()
                    return JsonResponse({'status': 'success', 'message': 'Activity updated successfully.'})
                else:
                    return JsonResponse({'status': 'error', 'message': 'Activity ended.'})
            except TimeEntry.DoesNotExist:
                return JsonResponse({'status': 'error', 'message': 'Activity not found.'})
        else:
            return JsonResponse({'status': 'error', 'message': 'you have not permission.'})


class EditTimeEntryView(APIView):
    @edit_time_entry_swagger
    def post(self, request, time_entry_id):
        try: 
            # بررسی وجود و اعتبار توکن
            request_token = request.headers.get('X-Api-Key')
            if not request_token:
                return Response({'status': 'error', 'message': 'Token not provided.'}, status=status.HTTP_400_BAD_REQUEST)

            # دریافت امپلویی بر اساس توکن
            employee = Employee.objects.filter(token=request_token).first()
            if not employee:
                return Response({'status': 'error', 'message': 'Invalid token or employee not found.'}, status=status.HTTP_403_FORBIDDEN)

            # دریافت اکتیویتی مربوط به امپلویی
            time_entry = get_object_or_404(TimeEntry, id=time_entry_id, employee=employee)

            # دریافت تایم‌های پیشنهادی و دلیل ویرایش از درخواست
            proposed_start_time = request.data.get('proposed_start_time')
            proposed_end_time = request.data.get('proposed_end_time')
            edit_reason = request.data.get('edit_reason', '')

            # دریافت سایر فیلدهای اکتیویتی از درخواست
            report_text = request.data.get('report_text')
            project_id = request.data.get('project')
            type_value = request.data.get('type')
            jira_task_id = request.data.get('jira_task_id')
            jira_task_name = request.data.get('jira_task_name')

            if not proposed_start_time or not proposed_end_time:
                return Response({'status': 'error', 'message': 'Proposed start time or end time not provided.'}, status=status.HTTP_400_BAD_REQUEST)

            try:
                # تبدیل تایم‌ها به فرمت datetime
                proposed_start_time = timezone.datetime.fromisoformat(proposed_start_time)
                proposed_end_time = timezone.datetime.fromisoformat(proposed_end_time)
            except ValueError:
                return Response({'status': 'error', 'message': 'Invalid date format.'}, status=status.HTTP_400_BAD_REQUEST)

            # بررسی وجود تایم‌های فعلی
            if not time_entry.start_time or not time_entry.end_time:
                return Response({'status': 'error', 'message': 'Current start or end time not available.'}, status=status.HTTP_400_BAD_REQUEST)

            # محاسبه مدت زمان فعلی و پیشنهادی
            current_duration = (time_entry.end_time - time_entry.start_time).total_seconds()
            proposed_duration = (proposed_end_time - proposed_start_time).total_seconds()

            # بررسی اختلاف بیشتر از 1 دقیقه
            duration_difference = abs(proposed_duration - current_duration)
            one_minute_seconds = 120

            if duration_difference > one_minute_seconds:
                # بروزرسانی تایم‌های پیشنهادی و دلیل ویرایش در اکتیویتی
                time_entry.proposed_start_time = proposed_start_time
                time_entry.proposed_end_time = proposed_end_time
                time_entry.edit_reason = edit_reason

                # ثبت تغییرات در لاگ
                log_message = f"Edited by {employee.full_name} on {timezone.now().isoformat()}: "
                log_message += f"Proposed Start Time: {proposed_start_time}, Proposed End Time: {proposed_end_time}, Reason: {edit_reason}. "

                # بررسی کاهش یا افزایش مدت زمان
                if proposed_duration < current_duration:
                    # در صورت کاهش مدت زمان، وضعیت به طور خودکار تایید می‌شود
                    time_entry.edit_status = 'APPROVED'
                    # بروزرسانی start_time و end_time با تایم‌های پیشنهادی
                    time_entry.start_time = proposed_start_time
                    time_entry.end_time = proposed_end_time
                    log_message += "Status: APPROVED."
                else:
                    # در صورت افزایش مدت زمان، وضعیت به حالت در انتظار تایید می‌رود
                    time_entry.edit_status = 'PENDING'
                    log_message += "Status: PENDING."

                # افزودن لاگ جدید به فیلد edit_log
                if time_entry.edit_log:
                    time_entry.edit_log += "\n" + log_message
                else:
                    time_entry.edit_log = log_message

            # بروزرسانی سایر فیلدها در صورت موجود بودن
            if report_text is not None:
                time_entry.report_text = report_text
            if project_id is not None:
                project = get_object_or_404(Projects, id=project_id)
                time_entry.project = project
            if type_value in dict(TimeEntry.PROJECT_TYPES).keys():
                time_entry.type = type_value
            if jira_task_id is not None:
                time_entry.jira_task_id = jira_task_id
            if jira_task_name is not None:
                time_entry.jira_task_name = jira_task_name

            # ذخیره تغییرات
            time_entry.save()
            return Response({'status': 'success', 'message': 'Time entry updated successfully.'})

        except TimeEntry.DoesNotExist:
            return Response({'status': 'error', 'message': 'Time entry not found.'}, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            # مدیریت سایر خطاهای پیش‌بینی نشده
            return Response({'status': 'error', 'message': f'An unexpected error occurred: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TimeEntryDetailView(APIView):
    @time_entry_detail_swagger
    def get(self, request, time_entry_id):
        # بررسی وجود و اعتبار توکن
        request_token = request.headers.get('X-Api-Key')
        if not request_token:
            return Response({'status': 'error', 'message': 'Token not provided.'}, status=status.HTTP_400_BAD_REQUEST)

        # دریافت امپلویی بر اساس توکن
        employee = Employee.objects.filter(token=request_token).first()
        if not employee:
            return Response({'status': 'error', 'message': 'Invalid token or employee not found.'}, status=status.HTTP_403_FORBIDDEN)

        # دریافت اکتیویتی مربوط به امپلویی
        time_entry = get_object_or_404(TimeEntry, id=time_entry_id, employee=employee)

        # استفاده از سریالایزر برای بازگرداندن داده‌ها
        serializer = TimeEntrySerializer(time_entry)
        return Response({'status': 'success', 'data': serializer.data}, status=status.HTTP_200_OK)