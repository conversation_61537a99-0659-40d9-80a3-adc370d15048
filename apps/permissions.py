from lxml.html.diff import token
from rest_framework.permissions import BasePermission
from apps.employee.models import Employee
from rest_framework.response import Response
from rest_framework import status

class HasTokenPermission(BasePermission):
    """
    اجازه دسترسی فقط در صورتی که توکن ارسالی مطابق با توکن مورد انتظار باشد.
    """

    def has_permission(self, request, view):
        # توکن انتظاری - این را با توکن واقعی جایگزین کنید
        # توکن ارسالی را از هدرهای درخواست بخوانید
        request_token = request.headers.get('X-Api-Key')
        return Employee.objects.filter(token=request_token).exists()

class EmployeeTokenPermission(BasePermission):
    """
    Permission class that validates employee token from X-Api-Key header
    """
    
    def has_permission(self, request, view):
        # Get token from X-Api-Key header
        token = request.headers.get('X-Api-Key')
        if not token:
            return False
            
        # Check if employee exists with this token
        try:
            employee = Employee.objects.get(token=token)
            # Store employee in request for later use
            request.employee = employee
            return True
        except Employee.DoesNotExist:
            return False
    
    def permission_denied(self, request, message=None, code=None):
        """
        Custom permission denied response
        """
        return Response({
            'success': False,
            'message': 'Permission denied - Invalid or missing token'
        }, status=status.HTTP_403_FORBIDDEN)
