import json

from dj_language.models import Language
from django import forms
from django.db import models


def get_languages():
    try:
        return list(Language.objects.filter(status=True).values_list('code', flat=True))
    except Exception as e:
        print(e)
        return []


schema_translation = {
    'type': "array",
    'format': 'table',
    'title': ' ',
    'items': {
        'type': 'object',
        'title': 'Translation',
        'properties': {
            'text': {'type': 'string', 'format': 'textarea', 'title': "Text"},
            'language_code': {
                'type': "string",
                'enum': get_languages(),
                'default': "en",
                'title': "Language Code"
            }
        }
    }
}


class JsonEditorWidget(forms.Textarea):
    template_name = 'fields/json_editor_field.html'

    def __init__(self, attrs={}, schema: dict = schema_translation):
        if schema:
            attrs.update({'schema': schema})

        super(JsonEditorWidget, self).__init__(attrs=attrs)


class JsonEditorField(models.JSONField):
    schema = {}

    def __init__(self, *args, schema: dict, **kwargs):
        self.schema = schema
        super().__init__(*args, **kwargs)

    def formfield(self, **kwargs):
        kwargs.update({
            'widget': JsonEditorWidget(attrs={'schema': json.dumps(self.schema)}),
        })
        return super(JsonEditorField, self).formfield(**kwargs)

    def deconstruct(self):
        name, path, args, kwargs = super().deconstruct()
        kwargs['schema'] = self.schema

        return name, path, args, kwargs
