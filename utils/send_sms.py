import requests
def send_sms(phone_number, message):
    message +="""
تایمی
    """
    data = {'from': '50004001410202', 'to': phone_number, 'text': message}
    try:
        response = requests.post('https://console.melipayamak.com/api/send/simple/33213d78f1234e99b81f94eefda77e45',
                                json=data)
        if response.status_code != 200 or not response.text.strip():
            return {'status': f'Error: HTTP status {response.status_code}', 'success': False}
        
        try:
            return response.json()
        except requests.exceptions.JSONDecodeError:
            # اگر پاسخ قابل تبدیل به JSON نباشد، متن پاسخ را برگردان
            return {'status': f'Error: Invalid JSON response - {response.text[:100]}', 'success': False}
    except Exception as e:
        # در صورت بروز هر گونه خطای دیگر
        return {'status': f'Error: {str(e)}', 'success': False}
    
    
    
