import requests
def send_tgbot(message):
    try:
        webhook_url = 'https://api.telegram.org/bot6696790003:AAGECPM0R5BnHfbCZRUsvzF9LKdeoI-7N_g/sendMessage'
        d = {
            'chat_id': '-1002212750034',
            'text': message,
        }
        response = requests.post(webhook_url, data=d, timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"Error sending Telegram message: {str(e)}")
        return False