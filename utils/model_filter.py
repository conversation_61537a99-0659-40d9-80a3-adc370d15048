from dj_language.models import Language
from django.contrib import admin


class CategoryLanguageFilter(admin.SimpleListFilter):
    title = 'Language'
    parameter_name = 'language'

    def lookups(self, request, model_admin):
        res = Language.objects.filter(status=True).all()
        return [
            (r.id, r.name) for r in res
        ]

    def queryset(self, request, queryset):
        if language_id := request.GET.get(self.parameter_name):
            return queryset.filter(category__language__id=language_id)
        return queryset.all()

# class CategoryFilter(admin.SimpleListFilter):
#     title = 'Category'
#     parameter_name = 'category'
#
#     def lookups(self, request, model_admin):
#         res = MafatihCategory.objects.filter(
#             is_active=True, content_type=ContentType.objects.get(model=MafatihCategory._meta.model_name)
#         ).all()
#         return [
#             (r.id, r.name) for r in res
#         ]
#
#     def queryset(self, request, queryset):
#         if category_id := request.GET.get(self.parameter_name):
#             return queryset.filter(category_id=category_id)
#         return queryset.all()
