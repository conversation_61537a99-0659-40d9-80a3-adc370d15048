from django.contrib import admin
from django.db import models
from django.utils.translation import gettext_lazy as _


class ModelExtension(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('created at'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('updated at'))
    created_by = models.ForeignKey(
        "account.User", verbose_name=_('created by'), null=True, on_delete=models.SET_NULL, related_name='+',
        editable=False,
    )
    updated_by = models.ForeignKey(
        "account.User", verbose_name=_('updated by'), null=True, on_delete=models.SET_NULL, related_name='+',
        editable=False,
    )

    class Meta:
        abstract = True


class AdminModelExtension(admin.ModelAdmin):
    def save_model(self, request, obj, form, change):
        obj.updated_by = request.user

        if not change:
            obj.created_by = request.user

        return super(AdminModelExtension, self).save_model(request, obj, form, change)
