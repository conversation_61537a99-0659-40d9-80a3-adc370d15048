import requests


def save_thumb(url, save_folder):
    from filer.models.filemodels import File
    import os
    import secrets

    name = secrets.token_urlsafe(8) + '.jpg'

    p = f'/tmp/{save_folder}'
    os.system(f'rm -rf {p} && mkdir {p}')

    with open(f'{p}/{name}.jpg', 'wb') as f:
        resp = requests.get(url)
        if resp and resp.status_code == 200:
            f.write(resp.content)
        else:
            return None

    os.system(f'python manage.py import_files --path={p} --folder=youtube')

    return File.objects.filter(original_filename=f'{name}.jpg', owner=None).first()
