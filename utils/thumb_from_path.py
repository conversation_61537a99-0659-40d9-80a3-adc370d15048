from functools import lru_cache

from easy_thumbnails.files import get_thumbnailer
from filer.models import ThumbnailOption


@lru_cache
def qs_thumbs():
    return ThumbnailOption.objects.all()


def gen_thumbs(file, request):
    try:
        thumbs = qs_thumbs()
        thumbnail_object = {}

        for thumb in thumbs:
            url = get_thumbnailer(file).get_thumbnail(thumb.as_dict).url
            # url = get_thumbnailer(file.path, thumb.as_dict).file.url
            if request:
                url = request.build_absolute_uri(url)
            thumbnail_object[thumb.name] = url

        return thumbnail_object
    except Exception as e:
        print(e)
        return {}
