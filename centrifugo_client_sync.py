#!/usr/bin/env python3
"""
Centrifugo Synchronous Client Script
Connects to Centrifugo WebSocket and listens to user channel
Usage: python centrifugo_client_sync.py --user-id 31
"""

import json
import argparse
import os
import sys
import django
from datetime import datetime
import threading
import time

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.account.centrifugo import centrifugo_service
from apps.employee.models import Employee

try:
    import websocket
except ImportError:
    print("❌ websocket-client package not found. Install it with:")
    print("pip install websocket-client")
    sys.exit(1)


class CentrifugoSyncClient:
    def __init__(self, user_id, ws_url='wss://centrifugo.newhorizonco.uk/connection/websocket'):
        self.user_id = user_id
        self.ws_url = ws_url
        self.token = None
        self.ws = None
        self.channel = f'teamby:user#{user_id}'
        self.message_id = 1
        self.connected = False
        self.subscribed = False
        
    def get_token(self):
        """Get JWT token for authentication"""
        try:
            # Check if user exists
            employee = Employee.objects.get(id=self.user_id)
            print(f"🔑 Generating token for: {employee.full_name} (ID: {employee.id})")
            
            # Generate token
            self.token = centrifugo_service.generate_token(self.user_id)
            print(f"✅ Token generated successfully")
            return True
            
        except Employee.DoesNotExist:
            print(f"❌ Employee with ID {self.user_id} not found")
            return False
        except Exception as e:
            print(f"❌ Error generating token: {str(e)}")
            return False
    
    def on_message(self, ws, message):
        """Handle incoming WebSocket messages"""
        try:
            data = json.loads(message)
            self.handle_message(data)
        except json.JSONDecodeError:
            print(f"⚠️  Invalid JSON received: {message}")
        except Exception as e:
            print(f"⚠️  Error handling message: {str(e)}")
    
    def on_error(self, ws, error):
        """Handle WebSocket errors"""
        print(f"❌ WebSocket error: {error}")
    
    def on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        print("🔌 WebSocket connection closed")
        self.connected = False
        self.subscribed = False
    
    def on_open(self, ws):
        """Handle WebSocket open"""
        print("🔌 WebSocket connection opened")
        self.authenticate()
    
    def authenticate(self):
        """Send authentication message"""
        connect_message = {
            "id": self.message_id,
            "connect": {
                "token": self.token,
                "name": "python-sync-client"
            }
        }
        self.message_id += 1
        
        print("🔑 Authenticating...")
        self.ws.send(json.dumps(connect_message))
    
    def subscribe(self):
        """Subscribe to user channel"""
        subscribe_message = {
            "id": self.message_id,
            "subscribe": {
                "channel": self.channel
            }
        }
        self.message_id += 1
        
        print(f"📡 Subscribing to channel: {self.channel}")
        self.ws.send(json.dumps(subscribe_message))
    
    def handle_message(self, data):
        """Handle incoming messages"""
        current_time = datetime.now().strftime('%H:%M:%S')
        
        # Handle connection response
        if 'connect' in data:
            if data['connect'].get('client'):
                client_id = data['connect']['client']
                print(f"✅ Connected successfully! Client ID: {client_id}")
                self.connected = True
                self.subscribe()
            else:
                print(f"❌ Connection failed: {data}")
            return
        
        # Handle subscription response
        if 'subscribe' in data:
            print(f"✅ Subscribed to channel: {self.channel}")
            print(f"🎧 Listening for employee status updates...")
            print("=" * 60)
            self.subscribed = True
            return
        
        # Handle push messages (actual notifications)
        if 'push' in data:
            push_data = data['push']
            channel = push_data.get('channel', '')
            pub_data = push_data.get('pub', {}).get('data', {})
            
            if channel == self.channel:
                message_type = pub_data.get('type', 'unknown')
                
                if message_type == 'employee_online':
                    employee_id = pub_data.get('employee_id')
                    employee_name = pub_data.get('employee_name')
                    timestamp = pub_data.get('timestamp')
                    
                    print(f"🟢 [{current_time}] EMPLOYEE ONLINE")
                    print(f"   👤 Name: {employee_name}")
                    print(f"   🆔 ID: {employee_id}")
                    print(f"   ⏰ Time: {datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')}")
                    print("-" * 40)
                    
                elif message_type == 'employee_offline':
                    employee_id = pub_data.get('employee_id')
                    employee_name = pub_data.get('employee_name')
                    timestamp = pub_data.get('timestamp')
                    
                    print(f"🔴 [{current_time}] EMPLOYEE OFFLINE")
                    print(f"   👤 Name: {employee_name}")
                    print(f"   🆔 ID: {employee_id}")
                    print(f"   ⏰ Time: {datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')}")
                    print("-" * 40)
                    
                else:
                    print(f"📨 [{current_time}] OTHER MESSAGE")
                    print(f"   Type: {message_type}")
                    print(f"   Data: {json.dumps(pub_data, indent=2)}")
                    print("-" * 40)
            return
        
        # Handle other message types
        if not any(key in data for key in ['connect', 'subscribe', 'unsubscribe']):
            print(f"📋 [{current_time}] RAW MESSAGE: {json.dumps(data, indent=2)}")
    
    def connect(self):
        """Connect to Centrifugo WebSocket"""
        if not self.get_token():
            return False
        
        print(f"🔌 Connecting to: {self.ws_url}")
        print(f"📡 Listening to channel: {self.channel}")
        print("=" * 60)
        
        # Enable WebSocket debug if needed
        # websocket.enableTrace(True)
        
        self.ws = websocket.WebSocketApp(
            self.ws_url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close
        )
        
        # Start the WebSocket connection
        self.ws.run_forever()
        
        return True
    
    def close(self):
        """Close the WebSocket connection"""
        if self.ws:
            self.ws.close()
            print("🔌 Connection closed")


def main():
    parser = argparse.ArgumentParser(description='Centrifugo Sync Client - Listen to employee status updates')
    parser.add_argument('--user-id', type=int, required=True, help='User ID to listen for')
    parser.add_argument('--ws-url', default='wss://centrifugo.newhorizonco.uk/connection/websocket', help='WebSocket URL')
    
    args = parser.parse_args()
    
    print("🚀 Centrifugo Sync Client Starting...")
    print(f"👤 User ID: {args.user_id}")
    print(f"🌐 WebSocket URL: {args.ws_url}")
    print("=" * 60)
    
    client = CentrifugoSyncClient(args.user_id, args.ws_url)
    
    try:
        client.connect()
    except KeyboardInterrupt:
        print("\n⏹️  Interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
    finally:
        client.close()


if __name__ == '__main__':
    main()
