import os

from celery import Celery

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.production')
os.environ.setdefault('CELERY_HOST', 'localhost')
os.environ.setdefault('CELERY_BROKER_URL', f'redis://{os.environ.get("CELERY_HOST")}:6379/0')
os.environ.setdefault('CELERY_RESULT_BACKEND', f'redis://{os.environ.get("CELERY_HOST")}:6379/0')

app = Celery('config')
# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
# - namespace='CELERY' means all celery-related configuration keys
#   should have a `CELERY_` prefix.
app.config_from_object('django.conf:settings', namespace='CELERY')

# بارگذاری تنظیمات زمان‌بندی Celery Beat
from config.celerybeat_schedule import CELERYBEAT_SCHEDULE
app.conf.beat_schedule = CELERYBEAT_SCHEDULE

# تنظیمات اضافی Celery
app.conf.broker_url = os.environ.get('CELERY_BROKER_URL')
app.conf.result_backend = os.environ.get('CELERY_RESULT_BACKEND')
app.conf.timezone = 'Asia/Tehran'
app.conf.enable_utc = False

# Load task modules from all registered Django apps.
app.autodiscover_tasks()
