from django.conf import settings
from django.urls import path, include, re_path
from django.conf.urls.static import static
from django.urls import path, include
from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework import permissions
from utils.admin import project_admin_site, HomeView
from django.conf.urls.i18n import i18n_patterns

from .api import *

# router = routers.DefaultRouter()
# router.register(r'time_entries', TimeEntryViewSet)

schema_view = get_schema_view(
    openapi.Info(
        title="Snippets API",
        default_version='v1',
        description="Test description",
        terms_of_service="https://www.google.com/policies/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="BSD License"),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    # path("admin/", HomeView.as_view(), name="home"),
    # path("admin/", project_admin_site.urls),
    path("admin/", HomeView.as_view(), name="home"),

    path("i18n/", include("django.conf.urls.i18n")),

    # path('', include('limitless_dashboard.urls')),
    path('api/account/', include('apps.account.urls')),
    path('api/', include('apps.activity.urls')),
    path('api/', include('apps.projects.urls')),
    path('api/', include('apps.employee.urls')),
    path('api/', include('apps.meet.urls')),
    path('api/', include('apps.message.urls')),
    path('', include('apps.goals.urls')),


    path('api/activities/', activities),
    path('api/comments/', create_comment, name='api_create_comment'),
    path('', include('apps.payroll.urls')),
]
urlpatterns+= i18n_patterns(
    path("admin/", project_admin_site.urls),
    re_path(r'^swagger(?P<format>\.json|\.yaml)$', schema_view.without_ui(cache_timeout=0), name='schema-json'),
    re_path(r'^swagger/$', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    re_path(r'^redoc/$', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('admin/filer/', include('filer.urls')),
)

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
