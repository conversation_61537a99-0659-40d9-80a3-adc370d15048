from celery.schedules import crontab

# تنظیمات زمان‌بندی Celery Beat
CELERYBEAT_SCHEDULE = {
    # تسک روزانه - هر ساعت در دقیقه 0 اجرا می‌شود
    'check-daily-tracking-reports': {
        'task': 'apps.employee.tasks.send_daily_tracking_reports',
        'schedule': crontab(minute='0'),  # هر ساعت در دقیقه 0 اجرا می‌شود
        'args': (),
    },
    
    # تسک هفتگی - هر ساعت در دقیقه 0 اجرا می‌شود
    'check-weekly-tracking-reports': {
        'task': 'apps.employee.tasks.send_weekly_tracking_reports',
        'schedule': crontab(minute='0'),  # هر ساعت در دقیقه 0 اجرا می‌شود
        'args': (),
    },
    
    # تسک ماهانه - هر ساعت در دقیقه 0 اجرا می‌شود
    'check-monthly-tracking-reports': {
        'task': 'apps.employee.tasks.send_monthly_tracking_reports',
        'schedule': crontab(minute='0'),  # هر ساعت در دقیقه 0 اجرا می‌شود
        'args': (),
    },
}