import os
from datetime import datetime
from pathlib import Path

import environ
from django.templatetags.static import static
from django.urls import reverse_lazy

env = environ.Env(
    # set casting, default value
    # DEBUG=(bool, False)
)
from django.utils.translation import gettext_lazy as _

BASE_DIR = Path(__file__).resolve().parent.parent.parent

environ.Env.read_env(os.path.join(BASE_DIR, '.env'))

ALLOWED_HOSTS = env('DJANGO_ALLOWED_HOSTS').split(',')

SECRET_KEY = 'django-insecure-$77e7e9&4&$kan*jl^5v8htl6b93@afn3%y(8&^)=*xn!u)^2%'

X_FRAME_OPTIONS = 'SAMEORIGIN'
SWAGGER_SETTINGS = {
    'api_key': 'yourKeyGoesHere',
}
INSTALLED_APPS = [
    "unfold",
    "unfold.contrib.filters",
    "unfold.contrib.import_export",
    "unfold.contrib.guardian",
    "unfold.contrib.simple_history",
    "unfold.contrib.forms",
    "unfold.contrib.inlines",
    "whitenoise.runserver_nostatic",
    # 'limitless_dashboard.apps.DashboardConfig',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',  
    'rest_framework',
    'django.contrib.postgres',
    'django_filters',
    'drf_yasg',
    'corsheaders',
    'dj_language',
    'ajaxdatatable',
    "phonenumber_field",
    'easy_thumbnails',
    'apps.account',
    'apps.activity',
    'apps.employee',
    'apps.projects',
    'apps.teams',
    'apps.meet',
    'apps.payroll',
    'apps.message',
    'apps.goals',  # Goals management app
    'apps.companies',  # Companies management app



]

MIDDLEWARE = [
    'django.middleware.gzip.GZipMiddleware',
    'django.middleware.security.SecurityMiddleware',
    "whitenoise.middleware.WhiteNoiseMiddleware",
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'config.language_code_middleware.language_middleware',

]

ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            BASE_DIR / 'templates',
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.i18n',
                "utils.admin.variables",

            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 6,
        }
    },
]

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Tehran'

USE_I18N = True

USE_L10N = True

USE_TZ = False

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

STATICFILES_DIRS = [
    BASE_DIR / "static",
]

STATIC_URL = "/static/"
STATIC_ROOT = BASE_DIR / "staticfiles"

MEDIA_URL = "/media/"
MEDIA_ROOT = BASE_DIR / "mediafiles"

FILER_ADMIN_ICON_SIZES = ('32', '48')

REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.LimitOffsetPagination',
    'PAGE_SIZE': 200,
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
    ]
}

# custom settings
APPS_REORDER = {
    'auth': {
        'icon': 'icon-shield-check',
        'name': 'Authentication'
    },
}

# User Auth
AUTH_USER_MODEL = "account.User"

# django google recaptcha default keys
RECAPTCHA_PUBLIC_KEY = env('captcha_public_key')
RECAPTCHA_PRIVATE_KEY = env('captcha_private_key')

GEOIP_PATH = str(BASE_DIR / 'geoip_db')

# Centrifugo Settings
CENTRIFUGO_SECRET = env('CENTRIFUGO_SECRET', default='lqtujzClu1rbim1fzB994YVi4sjjANPp')
CENTRIFUGO_API_URL = env('CENTRIFUGO_API_URL', default='http://localhost:8000/api')
CENTRIFUGO_API_KEY = env('CENTRIFUGO_API_KEY', default='tJID9nFyaw9OabjzS7O9Z6fn49ev22MA')
CENTRIFUGO_WS_URL = env('CENTRIFUGO_WS_URL', default='wss://centrifugo.newhorizonco.uk/connection/websocket')

ALL_MULTILINGUAL = True
CATEGORY_ENABLE_MULTILINGUAL = True
SLIDER_ENABLE_MULTILINGUAL = True
BLOG_ENABLE_CATEGORY = True

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': env('POSTGRES_DB'),
        'USER': env('POSTGRES_USER'),
        'PASSWORD': env('POSTGRES_PASSWORD'),
        'HOST': env('POSTGRES_HOST'),
        'PORT': env('POSTGRES_PORT'),
        'ATOMIC_REQUESTS': True,
    },
    # 'mongo': {
    #     'ENGINE': 'djongo',
    #     'NAME': env('MONGO_NAME'),
    #     'CLIENT': {
    #         'host': env('MONGO_HOST'),
    #     },
    # }
}

# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.memcached.PyMemcacheCache',
#         # 'LOCATION': '127.0.0.1:11211',
#     }
# }


FILER_ENABLE_LOGGING = True
FILER_DEBUG = True
ADMIN_TITLE = 'Timee App'
ADMIN_INDEX_TITLE = 'Timee Administration'

PAYPAL_TEST = True

LANGUAGES = [
    ('en', _('English')),
    ('fa', _('Persian')),
]

SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')

LANGUAGES_SORT_MAP = {
    'az': ['az', 'tr', 'fa', 'en'],
    'tr': ['re', 'az', 'fa', 'en'],
    'ru': ['ru', 'az', 'tr', 'fa', 'en'],
    'ar': ['ar', 'fa', 'en'],
    'ur': ['ur', 'en', 'fa', 'ar'],
    'en': ['en', 'fa', 'ar'],
}


HOLIDAYS = [
    datetime(2024, 3, 20),  # عید نوروز
    datetime(2024, 3, 21),
    datetime(2024, 3, 22),
    datetime(2024, 3, 23),
    datetime(2024, 3, 30),  # روز جمهوری اسلامی
    datetime(2024, 4, 1),   # روز طبیعت
    datetime(2024, 6, 3),   # رحلت امام خمینی
    datetime(2024, 6, 4),   # قیام ۱۵ خرداد
    datetime(2024, 2, 10),  # پیروزی انقلاب اسلامی
    datetime(2024, 3, 19),  # ملی شدن صنعت نفت
    datetime(2024, 7, 16),  # تاسوعا
    datetime(2024, 7, 17),  # عاشورا
    datetime(2024, 8, 26),  # اربعین
    datetime(2024, 9, 3),   # رحلت پیامبر اکرم (ص) و شهادت امام حسن (ع)
    datetime(2024, 9, 5),   # شهادت امام رضا (ع)
    datetime(2024, 9, 13),  # شهادت امام حسن عسکری (ع)
    datetime(2024, 9, 22),  # میلاد پیامبر (ص)
    datetime(2024, 11, 6),  # شهادت حضرت فاطمه زهرا (س)
    datetime(2024, 1, 12),  # میلاد امام علی (ع)
    datetime(2024, 1, 26),  # مبعث پیامبر (ص)
    datetime(2024, 2, 24),  # میلاد امام زمان (عج)
    datetime(2024, 3, 27),  # شهادت امام علی (ع)
    datetime(2024, 4, 11),  # عید فطر
    datetime(2024, 4, 12),  # دومین روز عید فطر
    datetime(2024, 6, 17),  # عید قربان
    datetime(2024, 6, 25),  # عید غدیر
]
DAYS_OF_WEEK_TRANSLATION = {
    "Saturday": "شنبه",
    "Sunday": "یکشنبه",
    "Monday": "دوشنبه",
    "Tuesday": "سه‌شنبه",
    "Wednesday": "چهارشنبه",
    "Thursday": "پنج‌شنبه",
    "Friday": "جمعه"
}

MONTHS_TRANSLATION = {
    "Farvardin": "فروردین",
    "Ordibehesht": "اردیبهشت",
    "Khordad": "خرداد",
    "Tir": "تیر",
    "Mordad": "مرداد",
    "Shahrivar": "شهریور",
    "Mehr": "مهر",
    "Aban": "آبان",
    "Azar": "آذر",
    "Dey": "دی",
    "Bahman": "بهمن",
    "Esfand": "اسفند"
}
DEBUG = True

SESSION_ENGINE = "django.contrib.sessions.backends.signed_cookies"
LOGIN_URL = "admin:login"
LOGIN_REDIRECT_URL = reverse_lazy("admin:index")

UNFOLD = {
    "SITE_TITLE": _("Timee Admin"),
    "SITE_HEADER": _("Timee "),
    "SITE_SUBHEADER": _("Timee Admin"),
    "SITE_DROPDOWN": [
        # {
        #     "icon": "diamond",
        #     "title": _("Imam Javad Site"),
        #     "link": "https://habibapp.com",
        # },
    ],
    "SITE_SYMBOL": "settings",
    # "SHOW_HISTORY": True,
    "SHOW_LANGUAGES": True,
    "ENVIRONMENT": "utils.environment_callback",
    "DASHBOARD_CALLBACK": "utils.admin.dashboard_callback",
    "SITE_ICON": {
        "light": lambda request: static("images/logo2.svg"),  # light mode
        "dark": lambda request: static("images/logo2.svg"),  # dark mode
    },
    "SITE_SYMBOL": "speed",
    "SHOW_BACK_BUTTON": True, # show/hide "Back" button on changeform in header, default: False
    "THEME": "dark",
    # "LOGIN": {
        # "image": lambda request: static("images/logo2.svg"),
    # },
    "COLORS": {
        "base": {
            "50": "249 250 251",
            "100": "243 244 246",
            "200": "229 231 235",
            "300": "209 213 219",
            "400": "156 163 175",
            "500": "107 114 128",
            "600": "75 85 99",
            "700": "55 65 81",
            "800": "31 41 55",
            "900": "17 24 39",
            "950": "3 7 18",
        },
        "primary": {
            "50": "243 234 253",
            "100": "232 208 251",
            "200": "216 167 247",
            "300": "200 140 235",
            "400": "190 120 230",
            "500": "174 100 216",  # #ae64d8 - رنگ دکمه اصلی (بنفش معدنی)
            "600": "150 80 190",
            "700": "130 70 170",
            "800": "110 60 150",
            "900": "90 50 130",
            "950": "70 40 110",
        },
        "secondary": {
            "50": "240 253 250",
            "100": "204 251 241",
            "200": "153 246 228",
            "300": "94 234 212",
            "400": "45 212 191",
            "500": "1 53 59",  # #01353B - رنگ پس‌زمینه
            "600": "1 43 48",
            "700": "1 36 40",
            "800": "1 30 34",
            "900": "0 26 29",
            "950": "0 13 15",
        },
        "font": {
            "subtle-light": "var(--color-base-500)",
            "subtle-dark": "var(--color-base-400)",
            "default-light": "var(--color-secondary-500)",  # استفاده از رنگ ثانویه برای متن
            "default-dark": "var(--color-base-300)",
            "important-light": "var(--color-base-900)",
            "important-dark": "255 255 255",  # #FFFFFF - برای متن سفید در دکمه‌ها
        },
    },
    "STYLES": [
        lambda request: static("css/styles.css"),
        lambda request: static("css/timeentry_admin.css"),
    ],
    "SCRIPTS": [
        # lambda request: static("js/chart.min.js"),
    ],
    "TABS": [
        {
            "page": "accounts",
            "models": ["account.user"],
            "items": [
                {
                    "title": _("Users"),
                    "icon": "sports_motorsports",
                    "link": reverse_lazy("admin:account_user_changelist"),
                    "active": lambda request: request.path.endswith("/account/user/")
                    # and "email__isnull" not in request.GET,
                },
            ],
        },
        {
            "page": "activities",
            "models": ["activity.timeentry"],
            "items": [
                {
                    "title": _("All Time Entries"),
                    "icon": "timer",
                    "link": reverse_lazy("admin:activity_timeentry_changelist"),
                    "active": lambda request: request.path.endswith("/activity/timeentry/")
                    and not request.GET.get('edit_status'),
                },
                # {
                #     "title": _("Activity Analysis"),
                #     "icon": "analytics",
                #     "link": reverse_lazy("admin:activity_timeentry_analysis"),
                #     "active": lambda request: "/activity/timeentry/analysis/" in request.path,
                # },
            ],
        },
        {
            "page": "goals",
            "models": ["goals.project", "goals.topic", "goals.target", "goals.comment"],
            "items": [
                {
                    "title": _("Projects Overview"),
                    "icon": "dashboard",
                    "link": reverse_lazy("admin:custom_view"),
                    "active": lambda request: request.path.endswith("/custom-url-path"),
                },
                {
                    "title": _("Projects"),
                    "icon": "folder",
                    "link": reverse_lazy("admin:goals_project_changelist"),
                    "active": lambda request: request.path.endswith("/goals/project/"),
                },
                {
                    "title": _("Topics"),
                    "icon": "topic",
                    "link": reverse_lazy("admin:goals_topic_changelist"),
                    "active": lambda request: request.path.endswith("/goals/topic/"),
                },
                {
                    "title": _("Targets"),
                    "icon": "task",
                    "link": reverse_lazy("admin:goals_target_changelist"),
                    "active": lambda request: request.path.endswith("/goals/target/"),
                },
                {
                    "title": _("Comments"),
                    "icon": "comment",
                    "link": reverse_lazy("admin:goals_comment_changelist"),
                    "active": lambda request: request.path.endswith("/goals/comment/"),
                },
            ],
        },
    ],
    "SIDEBAR": {
        # "show_search": True,
        "show_all_applications": True,
        "navigation": [
            # {
            #     "title": _(""),
            #     "separator": True,
            #     "collapsible": True,
            #     "items": [
            #         {
            #             "title": _("Dashboard"),
            #             "icon": "dashboard",
            #             "link": reverse_lazy("admin:index"),
            #         },
            #     ],
            # },
            {
                "title": _("Accounts"),
                "items": [
                    {
                        "title": _("Users"),
                        "icon": "person",
                        "link": reverse_lazy("admin:account_user_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                ],
            },
            {
                "title": _("Employees"),
                "icon": "business",
                "items": [
                    {
                        "title": _("Employees"),
                        "icon": "badge",
                        "link": reverse_lazy("admin:employee_employee_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    {
                        "title": _("Device Tokens"),
                        "icon": "smartphone",
                        "link": reverse_lazy("admin:employee_devicetoken_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    {
                        "title": _("Telegram custom report"),
                        "icon": "badge",
                        "link": reverse_lazy("admin:employee_employeetelegraminfo_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                ],
            },
            {
                "title": _("Activities"),
                "icon": "schedule",
                "items": [
                    {
                        "title": _("Time Entries"),
                        "icon": "timer",
                        "link": reverse_lazy("admin:activity_timeentry_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    # {
                    #     "title": _("Activity Analysis"),
                    #     "icon": "analytics",
                    #     "link": reverse_lazy("admin:activity_timeentry_analysis"),
                    #     "permission": lambda request: request.user.is_staff,
                    # },
                ],
            },
            {
                "title": _("Companies"),
                "icon": "business",
                "items": [
                    {
                        "title": _("Companies"),
                        "icon": "domain",
                        "link": reverse_lazy("admin:companies_company_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    {
                        "title": _("Positions"),
                        "icon": "work",
                        "link": reverse_lazy("admin:companies_position_changelist"),
                        "permission": lambda request: request.user.is_staff,
                    },
                ],
            },
            {
                "title": _("Goals"),
                "icon": "flag",
                "items": [
                    {
                        "title": _("Projects"),
                        "icon": "folder",
                        "link": reverse_lazy("admin:custom_view"),
                        "permission": lambda request: request.user.is_staff,
                    },
                    # {
                    #     "title": _("Topics"),
                    #     "icon": "topic",
                    #     "link": reverse_lazy("admin:goals_topic_changelist"),
                    #     "permission": lambda request: request.user.is_staff,
                    # },
                    # {
                    #     "title": _("Targets"),
                    #     "icon": "task",
                    #     "link": reverse_lazy("admin:goals_target_changelist"),
                    #     "permission": lambda request: request.user.is_staff,
                    # },
                    # {
                    #     "title": _("Comments"),
                    #     "icon": "comment",
                    #     "link": reverse_lazy("admin:goals_comment_changelist"),
                    #     "permission": lambda request: request.user.is_staff,
                    # },
                ],
            },

    # "STYLES": [
    #     lambda request: static("css/styles.css"),
    # ],
    # "SCRIPTS": [
    #     lambda request: static("js/scripts.js"),
    # ],

        ],
    },
}


UNFOLD_STUDIO_DEFAULT_FRAGMENT = "color-schemes"
UNFOLD_STUDIO_PERMISSION = lambda request: request.user.is_authenticated

PLAUSIBLE_DOMAIN = env("PLAUSIBLE_DOMAIN")

# Celery Configuration
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', f'redis://{os.environ.get("CELERY_HOST", "localhost")}:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', f'redis://{os.environ.get("CELERY_HOST", "localhost")}:6379/0')
CELERY_ACCEPT_CONTENT = ['application/json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = 'Asia/Tehran'  # تنظیم منطقه زمانی مناسب برای ایران
CELERY_ENABLE_UTC = False  # غیرفعال کردن UTC برای استفاده از منطقه زمانی محلی