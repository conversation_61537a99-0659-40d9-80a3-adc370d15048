from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
import json
from apps.goals.models import Comment, Project


def activities(request):
    return JsonResponse({
        'ok': 'True',
    })


@require_POST
def create_comment(request):
    try:
        # Check if the request is form data or JSON
        if request.content_type and 'application/json' in request.content_type:
            data = json.loads(request.body)
            project_id = data.get('project')
            text = data.get('text')
        else:
            project_id = request.POST.get('project')
            text = request.POST.get('text')
        
        # Validate required fields
        if not project_id or not text:
            return JsonResponse({
                'success': False,
                'error': 'Project ID and text are required'
            }, status=400)
        
        # Get project
        try:
            project = Project.objects.get(pk=project_id)
        except Project.DoesNotExist:
            return JsonResponse({
                'success': False,
                'error': 'Project not found'
            }, status=404)
        
        # Create comment
        comment = Comment.objects.create(
            project=project,
            text=text
        )
        
        return JsonResponse({
            'success': True,
            'comment': {
                'id': comment.id,
                'text': comment.text,
                'created_at': comment.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)

