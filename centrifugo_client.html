<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centrifugo Client - Employee Status Monitor</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .config-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
        }
        .message.online {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        .message.offline {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .message.info {
            background: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }
        .message.error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .clear-btn {
            background: #6c757d;
            font-size: 12px;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Centrifugo Client</h1>
            <p>Employee Status Monitor</p>
        </div>

        <div class="config-section">
            <h3>Configuration</h3>
            <div class="input-group">
                <label for="userId">User ID:</label>
                <input type="number" id="userId" value="31" placeholder="Enter user ID">
            </div>
            <div class="input-group">
                <label for="token">JWT Token:</label>
                <input type="text" id="token" placeholder="Get token from: python manage.py test_centrifugo token --user-id 31">
            </div>
            <div class="input-group">
                <label for="wsUrl">WebSocket URL:</label>
                <input type="text" id="wsUrl" value="ws://localhost:8000/connection/websocket">
            </div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>

        <div id="status" class="status disconnected">
            🔴 Disconnected
        </div>

        <div>
            <h3>Messages <button class="clear-btn" onclick="clearMessages()">Clear</button></h3>
            <div id="messages" class="messages"></div>
        </div>
    </div>

    <script>
        let ws = null;
        let messageId = 1;
        let userId = null;
        let channel = null;

        function updateStatus(message, type = 'disconnected') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function addMessage(message, type = 'info') {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function connect() {
            const userIdInput = document.getElementById('userId').value;
            const token = document.getElementById('token').value;
            const wsUrl = document.getElementById('wsUrl').value;

            if (!userIdInput || !token || !wsUrl) {
                alert('Please fill in all fields');
                return;
            }

            userId = parseInt(userIdInput);
            channel = `teamby:user#${userId}`;

            updateStatus('🟡 Connecting...', 'connecting');
            addMessage(`Connecting to ${wsUrl}`, 'info');
            addMessage(`User ID: ${userId}`, 'info');
            addMessage(`Channel: ${channel}`, 'info');

            ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                addMessage('WebSocket connection opened', 'info');
                authenticate(token);
            };

            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleMessage(data);
                } catch (error) {
                    addMessage(`Invalid JSON received: ${event.data}`, 'error');
                }
            };

            ws.onclose = function() {
                updateStatus('🔴 Disconnected', 'disconnected');
                addMessage('WebSocket connection closed', 'error');
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
            };

            ws.onerror = function(error) {
                addMessage(`WebSocket error: ${error.message || 'Unknown error'}`, 'error');
            };

            document.getElementById('connectBtn').disabled = true;
            document.getElementById('disconnectBtn').disabled = false;
        }

        function authenticate(token) {
            const connectMessage = {
                id: messageId++,
                connect: {
                    token: token,
                    name: 'browser-client'
                }
            };

            addMessage('Authenticating...', 'info');
            ws.send(JSON.stringify(connectMessage));
        }

        function subscribe() {
            const subscribeMessage = {
                id: messageId++,
                subscribe: {
                    channel: channel
                }
            };

            addMessage(`Subscribing to channel: ${channel}`, 'info');
            ws.send(JSON.stringify(subscribeMessage));
        }

        function handleMessage(data) {
            // Handle connection response
            if (data.connect) {
                if (data.connect.client) {
                    updateStatus('🟢 Connected', 'connected');
                    addMessage(`Connected successfully! Client ID: ${data.connect.client}`, 'info');
                    subscribe();
                } else {
                    updateStatus('🔴 Connection Failed', 'disconnected');
                    addMessage(`Connection failed: ${JSON.stringify(data)}`, 'error');
                }
                return;
            }

            // Handle subscription response
            if (data.subscribe) {
                addMessage(`Subscribed to channel: ${channel}`, 'info');
                addMessage('🎧 Listening for employee status updates...', 'info');
                return;
            }

            // Handle push messages (actual notifications)
            if (data.push) {
                const pushData = data.push;
                const pushChannel = pushData.channel;
                const pubData = pushData.pub?.data || {};

                if (pushChannel === channel) {
                    const messageType = pubData.type || 'unknown';

                    if (messageType === 'employee_online') {
                        const employeeId = pubData.employee_id;
                        const employeeName = pubData.employee_name;
                        const timestamp = pubData.timestamp;
                        const time = new Date(timestamp).toLocaleString();

                        addMessage(`🟢 <strong>EMPLOYEE ONLINE</strong><br>
                                   👤 Name: ${employeeName}<br>
                                   🆔 ID: ${employeeId}<br>
                                   ⏰ Time: ${time}`, 'online');

                    } else if (messageType === 'employee_offline') {
                        const employeeId = pubData.employee_id;
                        const employeeName = pubData.employee_name;
                        const timestamp = pubData.timestamp;
                        const time = new Date(timestamp).toLocaleString();

                        addMessage(`🔴 <strong>EMPLOYEE OFFLINE</strong><br>
                                   👤 Name: ${employeeName}<br>
                                   🆔 ID: ${employeeId}<br>
                                   ⏰ Time: ${time}`, 'offline');

                    } else {
                        addMessage(`📨 <strong>OTHER MESSAGE</strong><br>
                                   Type: ${messageType}<br>
                                   Data: <pre>${JSON.stringify(pubData, null, 2)}</pre>`, 'info');
                    }
                }
                return;
            }

            // Handle other message types
            addMessage(`📋 RAW MESSAGE: <pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        // Auto-focus on token input
        document.getElementById('token').focus();
    </script>
</body>
</html>
