بسم الله الرحمن الرحیم

# <PERSON><PERSON> Application


## Installation

requirements: 
```
python >= 3.8
postgres database
 
google captcha keys in .env config file
```

step 1. clone project from git
```bash
git clone https://git.nwhco.ir/syedazainab/backend.git
```

step 2. create .env config file by copying [.env.example] and fill parameters with ur own configs 

```bash
cp .env.example .env
```


step 3. create and activate virualenv

```bash
python -m venv venv
source venv/bin/activate
```

step 4. install dependencies

```bash
pip install -r requirements.txt
```


step 5. make migrations and migrate to database

```bash
python manage.py makemigrations

python manage.py migrate
```

step 6. create superuser and runserver

```bash
python manage.py createsuperuser
# enter username and password
```
then run the django server by 
```
python manage.py runserver
```

then open http://SERVER-IP:8000

admin panel url
http://SERVER-IP:8000/admin/
