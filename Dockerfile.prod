# pull official base image
FROM python:3.9

# set work directory
WORKDIR /usr/src/app

# set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# create directory for the app user
RUN mkdir -p /home/<USER>

# create the appropriate directories
ENV HOME=/home/<USER>
ENV APP_HOME=/home/<USER>/web
RUN mkdir $APP_HOME
RUN mkdir $APP_HOME/staticfiles
RUN mkdir $APP_HOME/mediafiles
WORKDIR $APP_HOME

# install dependencies

RUN apt-get update
RUN apt-get install -y vim

RUN pip install --upgrade pip


COPY ./requirements_server.txt .
COPY ./.env.prod ./.env
RUN --mount=type=cache,target=/root/.cache  pip install -r requirements_server.txt

# copy entrypoint.prod.sh
COPY ./entrypoint.prod.sh .
RUN sed -i 's/\r$//g'  $APP_HOME/entrypoint.prod.sh
RUN chmod +x  $APP_HOME/entrypoint.prod.sh

# copy project
COPY . $APP_HOME


# run entrypoint.prod.sh
ENTRYPOINT ["/home/<USER>/web/entrypoint.prod.sh"]

