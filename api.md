<div style="text-align: center;">بسم الله الرحمن الرحیم</div>


# syedazainab API

## Actions

> `/api/` for all routes prefix

|Route      | METHOD | Description                                             |
|-----------------------|-----|----------|
|[`/home/<USER>
|[`/ziarat_niabati/`](#ziarat-niabati) | POST  | Home Page |
|[`/books/`](#books)    | GET ||
|[`/books/category_list/`](#books-category-list)    | GET ||
|[`/books/<int:id>/`](#book-single)   | GET ||
|[`/wiki/`](#wiki) | GET ||
|[`/wiki/category_list/`](#wiki-category-list) | GET ||
|[`/wiki/<int:id>/`](#wiki-single) | GET ||
|[`/duas/`](#duas) | GET ||
|[`/duas/<int:id>/`](#dua-single) | GET ||
|[`/hadith/category_list/`](#hadith-category-list) | GET ||
|[`/hadith/list/`](#hadith-list) | GET ||
|[`/donate/list/`](#donate-list) | GET ||
<!-- |[`/zeynabie/`](#donate-list) | GET || -->
<!-- |[`/gallery/`](#donate-list) | GET || -->


<hr>

### Home Page

```yaml
#request api/home/
{
}
  #response
  {
    "hadith": {
        "translate":"",
        "narrator":""
    },
    "zeynabyah" {
        "audios" :{
            "1" : {
            "title":"",
            "owner":"",
            "thumbnail":"",
            "url":""
            },
            ...
        }
        "videos" :{
            "1":{
            "title":"",
            "owner":"",
            "thumbnail":"",
            "url":"",
        }
    },
    "books":{
      [
        {
        "id" : "",
        "title" : "",
        "summary" : "",
        "thumbnail" : "",
        "book_type" : "",
        "language" : "",
        "view_count" : "",
        "authors" : {
         [
           {
            "first_name" : "",
            "last_name" : ""
           },
           ...
         ]
        },
      },
      ...
      ]
    },
    "gallery":{
      "id" : ""
      "title" : "",
      "thumbnail" : "",
    },
    "heartnote":{
      [
        {
       "name" : "",
       "note" : "" ,
       "country" : ""
      },
      ...
      ]
    }

  }
```

<hr>

### ziarat niabati

```yaml
#request /api/ziarat_niabati/
{
  "name": "",
  "country": "see: https://gist.github.com/danrovito/977bcb97c9c2dfd3398a",
  "email": "",
  "note": "",

}

  #response
  {
    "status": 200,
    "message": "(server message)",
  }
```

<hr>

### books

```yaml
#request /api/books/
{
  "limit" : "int Number of results to return per page.(Please enter 12)",
  "offset" : "The initial index from which to return the results.",
  "category_id" : "If was empty, returns all the books",
  "sort_by" : "string newest or popular"
}

  #response
  {
      [
        {
        "id" : "",
        "title" : "",
        "summary" : "",
        "thumbnail" : "",
        "book_type" : "",
        "language" : "",
        "view_count" : "",
        "authors" : {
          [{
            "first_name" : "",
            "last_name" : ""
           },
           ...
          ]
        },
      },
      ...
      ]
  }
```

<hr>

### books category list

```yaml
#request /api/books/category_list
{
}

  #response
  ******
```

<hr>

### book single

```yaml
#request /api/books/<int:id>/
{}

  #response
  {
    "id" : "",
    "title" : "",
    "summary" : "",
    "description" : "",
    "publisher" : "",
    "volume_number" : "",
    "page_count" : "",
    "thumbnail" : "",
    "book_type" : "",
    "language" : "",
    "view_count" : "",
    "ISBN" : "****",
    "seo" : {
      "title" : "",
      "keyword" "",
      "description" : ""
    },
    "files" : {
      [{
        "title" : "",
        "url" : ""
      },
      ...
      ]
    }
    "authors" : {
      [{
        "first_name" : "",
        "last_name" : ""
       },
       ...
      ]
    },
    "tags" : {
      *******
    }
    "related" : {
      [{
        "title" : "",
        "thumbnail" : "",
        "book_type" : "",
        "authors" : {
          [{
            "first_name" : "",
            "last_name" : ""
           },
           ...
          ]
          },   
      },
      ...
      ]
    }
  }

```

<hr>

### wiki

```yaml
#request /api/wiki
{
}

  #response
{
"main_category_list" :[
  //the list of root category of each language (All of those)
  {
    "id" : "",
    "title" : "",
    "subtitle": ""
  },
  ...
],
"suggestions": [
// the list of suggested articles by admins (12 of the last ones)
  {
    "id" : "",
    "thumbnail" : "",
    "title" : "",
    "author" : "",

  },
  ...
],
"newests": [
// the list of newests articles (12 of the last ones)
  {
    "id" : "",
    "thumbnail" : "",
    "title" : "",
    "author" : "",

  },
  ...
],
"populars": [
// the list of popular articles (12 of the last ones)
  {
    "id" : "",
    "thumbnail" : "",
    "title" : "",
    "author" : "",

  },
  ...
]
}
```

<hr>

### wiki category list

```yaml
#request /api/wiki/category_list
{
}

  #response
{
  ******
}
```

<hr>

### wiki single

```yaml
#request /api/wiki/<int:id>/
{
}

  #response
{
 "id" : "",
 "title" : "",
 "author" : "",
 "content" : "html text of article",
 "tags": [*****],
 "related" :[
  // the list of related articles (5 of the last ones)
  {
    "id" : "",
    "thumbnail" : "",
    "title" : "",
    "author" : "",

  },
 ],
 "seo" : {
  "title" : "",
  "keyword" "",
  "description" : ""
},
}
```

<hr>

### duas

```yaml
#request /api/duas/
{
  "dua_type": "dua or ziyarat"
}

  #response
[
  // the list of duas (filtered by dua type)(All of those)
  {
 "id" : "",
 "title" : "",
 "has_audio" : "(boolean) Does it have audio?",
  },
  ...
]

```
<hr>

### dua single

```yaml
#request /api/duas/<int:id>
{
}

  #response
  {
 "id" : "",
 "title" : "",
 "has_audio" : "(boolean) Does it have audio?",
 "audio" : "url of audio file",
 "text" : "html text of dua"
  }
```
<hr>

### hadith category list

```yaml
#request /hadith/list/
{
}

#response

{
*******
}
```
<hr>

### hadith list

```yaml
#request /hadith/list/
{
  "category_id" : ""
  // If category_id is not sent, the default category will be returned
}

#response
[
  {
    "id" : "",
    "narrated_by" : "",
    "text" : "",
    "translation" : "",
    "tags":["",...],
    "source" : ""
  },
  ...
]
```
<hr>

### donate list

```yaml
#request /hadith/list/
{
}

#response
[
  {
    "id" : "",
    "title" : "",
    "summery" : "",
    "description" : "",
    "required_budget" : "",
    "funded_budget": "" ,
    "thumbnail" : "",
    "donor_count" : "",
    "files" : [{"url":"","type":""},...]
  },
  ...
]
```
<hr>