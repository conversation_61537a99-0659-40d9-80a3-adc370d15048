pipeline {
    environment {
        develop_server = '***********'
        production_server = "***********"
        project_name = "backendsyedzeynab"
        version = "master"
		gitBranch = "origin/master"
	    DOCKER_BUILDKIT = "1"
	    COMPOSE_DOCKER_CLI_BUILD = 1
    } 
    agent any
    stages {
        stage('build') {
            steps {
                script{
                    gitBranch=env.GIT_BRANCH

                    if(gitBranch=="origin/develop"){
                        version="develop"
                    }

                    if(gitBranch=="origin/master"){
                        sh "echo pushed to master "
                    }
                }
            }
           	post {
    			failure {
        			script{
            			sh "FAILED"
        			}
    			}
   			}
        }

        stage('test'){
            steps{
                sh "echo Run some unit tests"
            }
        }
        stage('deploy'){
            steps{
                script{

                    if(gitBranch=="origin/develop"){
                        withCredentials([usernamePassword(credentialsId: 'develop_server', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')]) {
                            sh 'sshpass -p $PASSWORD ssh $develop_server "cd /root/projects/$project_name && git pull"'
                            sh 'sshpass -p $PASSWORD ssh $develop_server "cd /root/projects/$project_name && DOCKER_BUILDKIT=1 COMPOSE_DOCKER_CLI_BUILD=1 docker-compose  up -d --build"'
                        }
                    }
                }
            }
        }
    }
}
//TestLine

