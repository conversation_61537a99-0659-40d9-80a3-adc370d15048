import os

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
from django.core.wsgi import get_wsgi_application

application = get_wsgi_application()
from utils.save_url_to_filer import save_thumb
from utils.ytdl import YoutubeDownloader
from dj_language.models import Language


def dl_songs_audios():
    from apps.zeynabie.models import Song, ZeynabieCategory, Genre, ZeynabieOwner
    from utils.ytdl.effoe import Effoe
    import csv

    data = csv.DictReader(open('madahi.csv'))
    for line in data:
        lang = Language.objects.get(code=line['lang'].lower())
        category, _ = ZeynabieCategory.objects.get_or_create(
            name='Maddahi',
            language=lang,
        )
        genre, _ = Genre.objects.get_or_create(
            title='مولودی',
        )
        owner, o_ = ZeynabieOwner.objects.get_or_create(
            name=line['madah'] or 'unknown',
        )
        owner.languages.set([lang])

        yt = YoutubeDownloader(line['link'])
        yt.drivers = [Effoe]
        video_info = yt.get_videos_info()
        video_path = yt.download_video()
        video_file = yt.save_to_filer(video_path)
        if video_info:
            song, _ = Song.objects.update_or_create(
                title=video_info['title'],
                description=video_info['description'],
                owner=owner,
                genre=genre,
                song_type=Song.Type.video,
                file=video_file,
                thumbnail=save_thumb(video_info['thumbnail'], 'songs-thumbnail'),
            )
            song.categories.set([category])

        else:
            print(line['link'], ' has no info')


dl_songs_audios()
