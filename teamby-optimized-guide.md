# راهنمای بهینه TeamBy Centrifugo + Django REST Framework

## 🎯 استراتژی بهینه: فقط Namespace `teamby`



### ✅ راه‌حل بهینه:
```javascript
// کاربر فقط به کانال شخصی خودش گوش می‌دهد
centrifuge.newSubscription('teamby:user#123');

// همه چیز از طریق type تشخیص داده می‌شود - نیازی به کانال اضافی نیست!
// حتی پیام‌های real-time چت هم از همین کانال می‌آید
```

## 📋 ساختار کانال (تک کانال)

### فقط یک کانال برای هر کاربر:
```
teamby:user#123
```

### انواع داده‌های دریافتی با `type`:

```javascript
// نوع 1: پیام جدید در چت
{
  type: 'chat_message',
  chat_id: 456,
  chat_type: 'private', // یا 'group'
  message: {
    id: 789,
    text: 'سلام چطوری؟',
    sender_id: 101,
    sender_name: 'علی احمدی',
    timestamp: 1640995200000
  }
}

// نوع 2: نوتیفیکیشن سیستمی
{
  type: 'notification',
  notification_type: 'friend_request', // یا 'system_update'
  title: 'درخواست دوستی جدید',
  message: 'محمد رضایی درخواست دوستی فرستاده',
  data: { user_id: 102 }
}

// نوع 3: به‌روزرسانی وضعیت چت
{
  type: 'chat_status',
  chat_id: 456,
  status: 'read', // یا 'typing', 'online'
  user_id: 101
}

// نوع 4: به‌روزرسانی لیست چت‌ها
{
  type: 'chat_list_update',
  action: 'update', // یا 'delete', 'add'
  chat: {
    chat_id: 456,
    name: 'علی احمدی',
    last_message: 'سلام',
    unread_count: 2
  }
}
```

## 🔄 جریان کامل (Full Flow) - ترکیب HTTP API و Centrifugo

### مفهوم کلی

در سیستم TeamBy، ترکیب HTTP API (Django REST Framework) و Centrifugo:

- **Django REST API**: دریافت داده‌های اولیه، ارسال پیام‌ها، validation
- **Centrifugo**: real-time notifications و sync تغییرات

### 1. **Django REST Framework Backend**

#### تولید JWT Token برای Centrifugo

```python
# settings.py
CENTRIFUGO_SECRET = 'teamby-hmac-secret-key-256bit-2024'
CENTRIFUGO_API_URL = 'http://localhost:8000/api'
CENTRIFUGO_API_KEY = 'teamby-api-secret-key-2024'

# utils/centrifugo.py
import jwt
import requests
from datetime import datetime, timedelta
from django.conf import settings

class CentrifugoService:
    def __init__(self):
        self.secret = settings.CENTRIFUGO_SECRET
        self.api_url = settings.CENTRIFUGO_API_URL
        self.api_key = settings.CENTRIFUGO_API_KEY
    
    def generate_token(self, user_id):
        """تولید JWT token برای کاربر"""
        payload = {
            'sub': str(user_id),
            'exp': datetime.utcnow() + timedelta(hours=24),
            'iat': datetime.utcnow(),
            'aud': 'teamby',
            'iss': 'teamby-backend'
        }
        return jwt.encode(payload, self.secret, algorithm='HS256')
    
    def publish(self, channel, data):
        """ارسال پیام به کانال Centrifugo"""
        try:
            response = requests.post(self.api_url, json={
                'method': 'publish',
                'params': {
                    'channel': channel,
                    'data': data
                }
            }, headers={
                'Content-Type': 'application/json',
                'X-API-Key': self.api_key
            })
            return response.json()
        except Exception as e:
            print(f"Centrifugo publish error: {e}")
            return None
    
    def notify_new_message(self, message, chat_participants):
        """اعلان پیام جدید به شرکت‌کنندگان چت"""
        for user_id in chat_participants:
            if user_id != message.sender_id:
                # ارسال به کانال شخصی کاربر
                self.publish(f'teamby:user#{user_id}', {
                    'type': 'chat_message',
                    'chat_id': message.chat_id,
                    'chat_type': message.chat_type,
                    'message': {
                        'id': message.id,
                        'text': message.text,
                        'sender_id': message.sender_id,
                        'sender_name': message.sender.get_full_name(),
                        'timestamp': int(message.created_at.timestamp() * 1000)
                    }
                })

    def notify_chat_status(self, chat_id, user_id, status, participants):
        """اعلان تغییر وضعیت چت (تایپ کردن، خوانده شدن و...)"""
        for participant_id in participants:
            if participant_id != user_id:
                self.publish(f'teamby:user#{participant_id}', {
                    'type': 'chat_status',
                    'chat_id': chat_id,
                    'status': status,
                    'user_id': user_id
                })

    def notify_system_notification(self, user_id, notification_type, title, message, data=None):
        """ارسال نوتیفیکیشن سیستمی"""
        self.publish(f'teamby:user#{user_id}', {
            'type': 'notification',
            'notification_type': notification_type,
            'title': title,
            'message': message,
            'data': data or {},
            'timestamp': int(time.time() * 1000)
        })

    def notify_chat_list_update(self, user_id, action, chat_data):
        """به‌روزرسانی لیست چت‌ها"""
        self.publish(f'teamby:user#{user_id}', {
            'type': 'chat_list_update',
            'action': action,  # 'update', 'delete', 'add'
            'chat': chat_data
        })

centrifugo_service = CentrifugoService()
```

#### API Views

```python
# views.py
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from .models import Chat, Message
from .serializers import ChatSerializer, MessageSerializer
from .utils.centrifugo import centrifugo_service

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_centrifugo_token(request):
    """دریافت JWT token برای اتصال به Centrifugo"""
    token = centrifugo_service.generate_token(request.user.id)
    return Response({
        'token': token,
        'user_id': request.user.id,
        'ws_url': 'ws://localhost:8000/connection/websocket'
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_chats(request):
    """دریافت لیست چت‌های کاربر"""
    chats = Chat.objects.filter(
        participants=request.user
    ).prefetch_related('participants', 'last_message__sender')
    
    chat_data = []
    for chat in chats:
        other_user = None
        if chat.chat_type == 'private':
            other_user = chat.participants.exclude(id=request.user.id).first()
        
        unread_count = Message.objects.filter(
            chat=chat,
            created_at__gt=chat.get_user_last_read(request.user)
        ).exclude(sender=request.user).count()
        
        chat_data.append({
            'chat_id': chat.id,
            'chat_type': chat.chat_type,
            'name': chat.name if chat.chat_type == 'group' else other_user.get_full_name(),
            'other_user_id': other_user.id if other_user else None,
            'last_message': {
                'text': chat.last_message.text if chat.last_message else '',
                'timestamp': int(chat.last_message.created_at.timestamp() * 1000) if chat.last_message else 0,
                'sender_name': chat.last_message.sender.get_full_name() if chat.last_message else ''
            },
            'unread_count': unread_count,
            'updated_at': int(chat.updated_at.timestamp() * 1000)
        })
    
    # مرتب‌سازی بر اساس آخرین فعالیت
    chat_data.sort(key=lambda x: x['updated_at'], reverse=True)
    
    return Response({'chats': chat_data})

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def send_message(request, chat_id):
    """ارسال پیام جدید"""
    try:
        chat = Chat.objects.get(id=chat_id, participants=request.user)
        text = request.data.get('text', '').strip()
        
        if not text:
            return Response({'error': 'متن پیام نمی‌تواند خالی باشد'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # ایجاد پیام
        message = Message.objects.create(
            chat=chat,
            sender=request.user,
            text=text
        )
        
        # به‌روزرسانی آخرین پیام چت
        chat.last_message = message
        chat.save()
        
        # ارسال notification به Centrifugo
        participants = list(chat.participants.values_list('id', flat=True))
        centrifugo_service.notify_new_message(message, participants)
        
        return Response({
            'message': {
                'id': message.id,
                'text': message.text,
                'sender_id': message.sender_id,
                'sender_name': message.sender.get_full_name(),
                'timestamp': int(message.created_at.timestamp() * 1000),
                'chat_id': chat.id
            }
        })
        
    except Chat.DoesNotExist:
        return Response({'error': 'چت یافت نشد'}, 
                      status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({'error': str(e)}, 
                      status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_chat_messages(request, chat_id):
    """دریافت پیام‌های چت"""
    try:
        chat = Chat.objects.get(id=chat_id, participants=request.user)
        page = int(request.GET.get('page', 1))
        page_size = 50
        
        messages = Message.objects.filter(chat=chat).order_by('-created_at')
        
        # صفحه‌بندی
        start = (page - 1) * page_size
        end = start + page_size
        page_messages = messages[start:end]
        
        message_data = [{
            'id': msg.id,
            'text': msg.text,
            'sender_id': msg.sender_id,
            'sender_name': msg.sender.get_full_name(),
            'timestamp': int(msg.created_at.timestamp() * 1000)
        } for msg in page_messages]
        
        return Response({
            'messages': list(reversed(message_data)),  # ترتیب صحیح
            'page': page,
            'has_more': messages.count() > end
        })
        
    except Chat.DoesNotExist:
        return Response({'error': 'چت یافت نشد'}, 
                      status=status.HTTP_404_NOT_FOUND)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def mark_chat_read(request, chat_id):
    """نشان‌گذاری چت به عنوان خوانده شده"""
    try:
        chat = Chat.objects.get(id=chat_id, participants=request.user)
        
        # به‌روزرسانی آخرین زمان خواندن
        chat_participant = chat.chatparticipant_set.get(user=request.user)
        chat_participant.last_read_at = timezone.now()
        chat_participant.save()
        
        return Response({'success': True})
        
    except Chat.DoesNotExist:
        return Response({'error': 'چت یافت نشد'}, 
                      status=status.HTTP_404_NOT_FOUND)
```

#### URLs

```python
# urls.py
from django.urls import path
from . import views

urlpatterns = [
    path('centrifugo/token/', views.get_centrifugo_token, name='centrifugo_token'),
    path('chats/', views.get_user_chats, name='user_chats'),
    path('chats/<int:chat_id>/messages/', views.get_chat_messages, name='chat_messages'),
    path('chats/<int:chat_id>/send/', views.send_message, name='send_message'),
    path('chats/<int:chat_id>/read/', views.mark_chat_read, name='mark_chat_read'),
]
```

### 2. **React Frontend Implementation**

#### دریافت Token و اتصال

```javascript
// hooks/useCentrifugoAuth.js
import { useState, useEffect } from 'react';

export const useCentrifugoAuth = () => {
  const [authData, setAuthData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchCentrifugoToken = async () => {
      try {
        const response = await fetch('/api/centrifugo/token/', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('خطا در دریافت token');
        }

        const data = await response.json();
        setAuthData(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchCentrifugoToken();
  }, []);

  return { authData, loading, error };
};
```

#### Hook اصلی TeamBy

```javascript
// hooks/useTeamByChat.js
import { useState, useEffect, useCallback } from 'react';
import { Centrifuge } from 'centrifuge';
import { useCentrifugoAuth } from './useCentrifugoAuth';

export const useTeamByChat = () => {
  const { authData, loading: authLoading } = useCentrifugoAuth();
  const [chats, setChats] = useState([]);
  const [loading, setLoading] = useState(true);
  const [centrifuge, setCentrifuge] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [currentChatId, setCurrentChatId] = useState(null);
  const [currentChatSub, setCurrentChatSub] = useState(null);

  // دریافت لیست اولیه چت‌ها
  const fetchChats = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/chats/', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setChats(data.chats);
      }
    } catch (error) {
      console.error('Error fetching chats:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // راه‌اندازی Centrifugo
  useEffect(() => {
    if (!authData) return;

    const client = new Centrifuge(authData.ws_url, {
      token: authData.token,
      debug: process.env.NODE_ENV === 'development'
    });

    client.on('connected', () => {
      setConnectionStatus('connected');
      console.log('Connected to Centrifugo');
    });

    client.on('disconnected', () => {
      setConnectionStatus('disconnected');
    });

    setCentrifuge(client);
    client.connect();

    return () => {
      client.disconnect();
    };
  }, [authData]);

  // اشتراک در کانال شخصی کاربر
  useEffect(() => {
    if (!centrifuge || !authData || connectionStatus !== 'connected') return;

    const userSub = centrifuge.newSubscription(`teamby:user#${authData.user_id}`);

    userSub.on('publication', (ctx) => {
      const data = ctx.data;

      // مدیریت انواع مختلف داده‌ها
      switch (data.type) {
        case 'chat_message':
          handleChatMessage(data);
          break;
        case 'notification':
          handleNotification(data);
          break;
        case 'chat_status':
          handleChatStatus(data);
          break;
        case 'chat_list_update':
          handleChatListUpdate(data);
          break;
        default:
          console.log('Unknown message type:', data.type);
      }
    });

    userSub.subscribe();

    return () => {
      userSub.unsubscribe();
    };
  }, [centrifuge, authData, connectionStatus]);

  // مدیریت پیام جدید چت
  const handleChatMessage = useCallback((data) => {
    setChats(prevChats => {
      const updatedChats = [...prevChats];
      const chatIndex = updatedChats.findIndex(c => c.chat_id === data.chat_id);

      if (chatIndex >= 0) {
        // به‌روزرسانی چت موجود
        updatedChats[chatIndex] = {
          ...updatedChats[chatIndex],
          last_message: data.message,
          unread_count: updatedChats[chatIndex].unread_count + 1,
          updated_at: data.message.timestamp
        };

        // انتقال به بالای لیست
        const updatedChat = updatedChats.splice(chatIndex, 1)[0];
        updatedChats.unshift(updatedChat);
      } else {
        // چت جدید - باید از API کامل fetch شود
        fetchChats();
      }

      return updatedChats;
    });

    // اگر داخل همین چت هستیم، پیام را به لیست پیام‌ها اضافه کن
    if (currentChatId === data.chat_id) {
      // ارسال event برای component چت که پیام جدید آمده
      window.dispatchEvent(new CustomEvent('newChatMessage', {
        detail: data.message
      }));
    }
  }, [currentChatId, fetchChats]);

  // مدیریت نوتیفیکیشن‌ها
  const handleNotification = useCallback((data) => {
    // نمایش نوتیفیکیشن
    if (Notification.permission === 'granted') {
      new Notification(data.title, {
        body: data.message,
        icon: '/icon.png'
      });
    }

    // ذخیره در state برای نمایش در UI
    setNotifications(prev => [data, ...prev.slice(0, 49)]);
  }, []);

  // مدیریت وضعیت چت (تایپ کردن، آنلاین و...)
  const handleChatStatus = useCallback((data) => {
    if (currentChatId === data.chat_id) {
      // ارسال event برای component چت
      window.dispatchEvent(new CustomEvent('chatStatus', {
        detail: data
      }));
    }
  }, [currentChatId]);

  // مدیریت به‌روزرسانی لیست چت‌ها
  const handleChatListUpdate = useCallback((data) => {
    setChats(prevChats => {
      switch (data.action) {
        case 'update':
          return prevChats.map(chat =>
            chat.chat_id === data.chat.chat_id
              ? { ...chat, ...data.chat }
              : chat
          );
        case 'add':
          return [data.chat, ...prevChats];
        case 'delete':
          return prevChats.filter(chat => chat.chat_id !== data.chat.chat_id);
        default:
          return prevChats;
      }
    });
  }, []);

  // ورود به چت مشخص (فقط تنظیم state)
  const enterChat = useCallback((chatId) => {
    setCurrentChatId(chatId);
  }, []);

  // خروج از چت
  const exitChat = useCallback(() => {
    setCurrentChatId(null);
  }, []);

  // ارسال پیام
  const sendMessage = useCallback(async (chatId, text) => {
    try {
      const response = await fetch(`/api/chats/${chatId}/send/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ text })
      });

      if (!response.ok) {
        throw new Error('خطا در ارسال پیام');
      }

      return await response.json();
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }, []);

  // نشان‌گذاری به عنوان خوانده شده
  const markAsRead = useCallback(async (chatId) => {
    try {
      await fetch(`/api/chats/${chatId}/read/`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
          'Content-Type': 'application/json'
        }
      });

      // به‌روزرسانی local state
      setChats(prevChats => 
        prevChats.map(chat => 
          chat.chat_id === chatId 
            ? { ...chat, unread_count: 0 }
            : chat
        )
      );
    } catch (error) {
      console.error('Error marking as read:', error);
    }
  }, []);

  // بارگذاری اولیه
  useEffect(() => {
    if (!authLoading && authData) {
      fetchChats();
    }
  }, [authLoading, authData, fetchChats]);

  return {
    chats,
    loading: loading || authLoading,
    connectionStatus,
    currentChatId,
    notifications,
    enterChat,
    exitChat,
    sendMessage,
    markAsRead,
    refreshChats: fetchChats
  };
};
```

#### Component اصلی

```javascript
// components/TeamByChatApp.js
import React, { useState } from 'react';
import { useTeamByChat } from '../hooks/useTeamByChat';
import ChatsList from './ChatsList';
import ChatWindow from './ChatWindow';

const TeamByChatApp = () => {
  const {
    chats,
    loading,
    connectionStatus,
    currentChatId,
    enterChat,
    exitChat,
    sendMessage,
    markAsRead
  } = useTeamByChat();

  const [selectedChat, setSelectedChat] = useState(null);

  const handleChatSelect = (chat) => {
    setSelectedChat(chat);
    enterChat(chat.chat_id);

    // نشان‌گذاری به عنوان خوانده شده
    if (chat.unread_count > 0) {
      markAsRead(chat.chat_id);
    }
  };

  const handleBackToList = () => {
    setSelectedChat(null);
    exitChat();
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="spinner"></div>
        <p>در حال بارگذاری...</p>
      </div>
    );
  }

  return (
    <div className="teamby-chat-app">
      <div className="app-header">
        <h1>TeamBy Chat</h1>
        <div className={`connection-status ${connectionStatus}`}>
          {connectionStatus === 'connected' ? '🟢 آنلاین' : '🔴 آفلاین'}
        </div>
      </div>

      <div className="app-content">
        {!selectedChat ? (
          <ChatsList
            chats={chats}
            onChatSelect={handleChatSelect}
          />
        ) : (
          <ChatWindow
            chat={selectedChat}
            onBack={handleBackToList}
            onSendMessage={sendMessage}
          />
        )}
      </div>
    </div>
  );
};

export default TeamByChatApp;
```

#### Component لیست چت‌ها

```javascript
// components/ChatsList.js
import React from 'react';

const ChatsList = ({ chats, onChatSelect }) => {
  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('fa-IR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return date.toLocaleDateString('fa-IR');
    }
  };

  return (
    <div className="chats-list">
      <div className="list-header">
        <h2>چت‌ها ({chats.length})</h2>
      </div>

      <div className="chats-container">
        {chats.length === 0 ? (
          <div className="no-chats">
            <p>هیچ چتی وجود ندارد</p>
          </div>
        ) : (
          chats.map(chat => (
            <div
              key={chat.chat_id}
              className="chat-item"
              onClick={() => onChatSelect(chat)}
            >
              <div className="chat-avatar">
                {chat.chat_type === 'private' ? '👤' : '👥'}
              </div>

              <div className="chat-info">
                <div className="chat-name">
                  {chat.name}
                </div>
                <div className="last-message">
                  {chat.last_message.text || 'هیچ پیامی وجود ندارد'}
                </div>
              </div>

              <div className="chat-meta">
                <div className="chat-time">
                  {formatTime(chat.last_message.timestamp)}
                </div>
                {chat.unread_count > 0 && (
                  <div className="unread-badge">
                    {chat.unread_count}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ChatsList;
```

#### Component پنجره چت

```javascript
// components/ChatWindow.js
import React, { useState, useEffect, useRef } from 'react';

const ChatWindow = ({ chat, onBack, onSendMessage }) => {
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef(null);

  // دریافت پیام‌های چت
  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/chats/${chat.chat_id}/messages/`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setMessages(data.messages);
        }
      } catch (error) {
        console.error('Error fetching messages:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();
  }, [chat.chat_id]);

  // اسکرول به آخرین پیام
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // ارسال پیام
  const handleSendMessage = async (e) => {
    e.preventDefault();
    if (!newMessage.trim() || sending) return;

    try {
      setSending(true);
      const response = await onSendMessage(chat.chat_id, newMessage);

      // اضافه کردن پیام به لیست
      setMessages(prev => [...prev, response.message]);
      setNewMessage('');
    } catch (error) {
      alert('خطا در ارسال پیام');
    } finally {
      setSending(false);
    }
  };

  const formatMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('fa-IR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="chat-window">
      <div className="chat-header">
        <button onClick={onBack} className="back-button">
          ←
        </button>
        <div className="chat-info">
          <h3>{chat.name}</h3>
          <span className="chat-type">
            {chat.chat_type === 'private' ? 'چت شخصی' : 'گروه'}
          </span>
        </div>
      </div>

      <div className="messages-container">
        {loading ? (
          <div className="loading">در حال بارگذاری پیام‌ها...</div>
        ) : (
          <>
            {messages.map(message => (
              <div
                key={message.id}
                className={`message ${message.sender_id === chat.other_user_id ? 'received' : 'sent'}`}
              >
                <div className="message-content">
                  <div className="message-text">{message.text}</div>
                  <div className="message-time">
                    {formatMessageTime(message.timestamp)}
                  </div>
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      <form onSubmit={handleSendMessage} className="message-form">
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="پیام خود را بنویسید..."
          disabled={sending}
        />
        <button
          type="submit"
          disabled={sending || !newMessage.trim()}
        >
          {sending ? '...' : 'ارسال'}
        </button>
      </form>
    </div>
  );
};

export default ChatWindow;
```

#### Hook ماژولار برای مدیریت انواع پیام‌ها

```javascript
// hooks/useMessageHandler.js
import { useCallback } from 'react';

export const useMessageHandler = () => {

  // مدیریت پیام‌های چت
  const handleChatMessage = useCallback((data, currentChatId, setChats, setMessages) => {
    // به‌روزرسانی لیست چت‌ها
    setChats(prevChats => {
      const updatedChats = [...prevChats];
      const chatIndex = updatedChats.findIndex(c => c.chat_id === data.chat_id);

      if (chatIndex >= 0) {
        updatedChats[chatIndex] = {
          ...updatedChats[chatIndex],
          last_message: data.message,
          unread_count: currentChatId === data.chat_id
            ? updatedChats[chatIndex].unread_count
            : updatedChats[chatIndex].unread_count + 1,
          updated_at: data.message.timestamp
        };

        // انتقال به بالای لیست
        const updatedChat = updatedChats.splice(chatIndex, 1)[0];
        updatedChats.unshift(updatedChat);
      }

      return updatedChats;
    });

    // اگر داخل همین چت هستیم، پیام را اضافه کن
    if (currentChatId === data.chat_id && setMessages) {
      setMessages(prev => [...prev, data.message]);
    }
  }, []);

  // مدیریت نوتیفیکیشن‌ها
  const handleNotification = useCallback((data, setNotifications) => {
    // نمایش browser notification
    if (Notification.permission === 'granted') {
      const notification = new Notification(data.title, {
        body: data.message,
        icon: '/icon.png',
        tag: data.notification_type
      });

      // بستن خودکار بعد از 5 ثانیه
      setTimeout(() => notification.close(), 5000);
    }

    // ذخیره در state
    setNotifications(prev => [data, ...prev.slice(0, 49)]);
  }, []);

  // مدیریت وضعیت چت
  const handleChatStatus = useCallback((data, currentChatId, setChatStatus) => {
    if (currentChatId === data.chat_id && setChatStatus) {
      setChatStatus(prev => ({
        ...prev,
        [data.user_id]: {
          status: data.status,
          timestamp: Date.now()
        }
      }));

      // پاک کردن وضعیت تایپ بعد از 3 ثانیه
      if (data.status === 'typing') {
        setTimeout(() => {
          setChatStatus(prev => ({
            ...prev,
            [data.user_id]: { status: 'idle', timestamp: Date.now() }
          }));
        }, 3000);
      }
    }
  }, []);

  // مدیریت به‌روزرسانی لیست چت‌ها
  const handleChatListUpdate = useCallback((data, setChats) => {
    setChats(prevChats => {
      switch (data.action) {
        case 'update':
          return prevChats.map(chat =>
            chat.chat_id === data.chat.chat_id
              ? { ...chat, ...data.chat }
              : chat
          );
        case 'add':
          return [data.chat, ...prevChats];
        case 'delete':
          return prevChats.filter(chat => chat.chat_id !== data.chat.chat_id);
        default:
          return prevChats;
      }
    });
  }, []);

  return {
    handleChatMessage,
    handleNotification,
    handleChatStatus,
    handleChatListUpdate
  };
};
```

#### Component نوتیفیکیشن

```javascript
// components/NotificationCenter.js
import React, { useState } from 'react';

const NotificationCenter = ({ notifications, onClear }) => {
  const [isOpen, setIsOpen] = useState(false);

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'friend_request': return '👤';
      case 'system_update': return '🔔';
      case 'group_invite': return '👥';
      default: return '📢';
    }
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = (now - date) / (1000 * 60);

    if (diffInMinutes < 1) return 'الان';
    if (diffInMinutes < 60) return `${Math.floor(diffInMinutes)} دقیقه پیش`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ساعت پیش`;
    return date.toLocaleDateString('fa-IR');
  };

  return (
    <div className="notification-center">
      <button
        className="notification-toggle"
        onClick={() => setIsOpen(!isOpen)}
      >
        🔔
        {notifications.length > 0 && (
          <span className="notification-badge">{notifications.length}</span>
        )}
      </button>

      {isOpen && (
        <div className="notification-dropdown">
          <div className="notification-header">
            <h3>اعلان‌ها</h3>
            {notifications.length > 0 && (
              <button onClick={onClear} className="clear-all">
                پاک کردن همه
              </button>
            )}
          </div>

          <div className="notification-list">
            {notifications.length === 0 ? (
              <div className="no-notifications">
                هیچ اعلانی وجود ندارد
              </div>
            ) : (
              notifications.map((notification, index) => (
                <div key={index} className="notification-item">
                  <div className="notification-icon">
                    {getNotificationIcon(notification.notification_type)}
                  </div>
                  <div className="notification-content">
                    <div className="notification-title">
                      {notification.title}
                    </div>
                    <div className="notification-message">
                      {notification.message}
                    </div>
                    <div className="notification-time">
                      {formatTime(notification.timestamp)}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
```

#### Component نمایش وضعیت چت

```javascript
// components/ChatStatus.js
import React from 'react';

const ChatStatus = ({ chatStatus, users }) => {
  const getStatusText = (userId) => {
    const status = chatStatus[userId];
    if (!status) return null;

    const user = users.find(u => u.id === userId);
    const userName = user ? user.name : `کاربر ${userId}`;

    switch (status.status) {
      case 'typing':
        return `${userName} در حال تایپ...`;
      case 'online':
        return `${userName} آنلاین است`;
      default:
        return null;
    }
  };

  const activeStatuses = Object.keys(chatStatus).filter(userId => {
    const status = chatStatus[userId];
    return status && ['typing', 'online'].includes(status.status);
  });

  if (activeStatuses.length === 0) return null;

  return (
    <div className="chat-status">
      {activeStatuses.map(userId => (
        <div key={userId} className={`status-item ${chatStatus[userId].status}`}>
          {getStatusText(parseInt(userId))}
        </div>
      ))}
    </div>
  );
};

export default ChatStatus;
```

## 🎯 مزایای رویکرد تک کانال

### 1. **کد تمیز و ماژولار**
```javascript
// ✅ فقط یک subscription برای همه چیز
const userSub = centrifuge.newSubscription(`teamby:user#${userId}`);

// ✅ مدیریت انواع مختلف با switch
switch (data.type) {
  case 'chat_message': handleChatMessage(data); break;
  case 'notification': handleNotification(data); break;
  case 'chat_status': handleChatStatus(data); break;
  case 'chat_list_update': handleChatListUpdate(data); break;
}
```

### 2. **Performance بهتر**
- فقط یک WebSocket subscription برای همه چیز
- کمترین بار روی Centrifugo
- مدیریت بهینه memory
- حتی پیام‌های real-time چت از همین کانال

### 3. **مقیاس‌پذیری بالا**
- حتی با هزاران چت، فقط یک کانال
- آسان برای debugging
- قابل توسعه برای انواع جدید پیام
- نیازی به مدیریت چندین subscription نیست

### 4. **تجربه کاربری عالی**
- Real-time updates برای همه چیز
- نوتیفیکیشن‌های هوشمند
- مدیریت وضعیت تایپ
- به‌روزرسانی فوری لیست چت‌ها
- پیام‌های فوری در چت فعال

## 📱 مثال کامل: همه چیز از یک کانال

```javascript
// مثال: کاربر 123 در چت 456 است و این اتفاقات می‌افتد:

// 1. پیام جدید در چت فعلی (456)
{
  type: 'chat_message',
  chat_id: 456,
  message: { text: 'سلام!', sender_id: 789 }
}
// ✅ React: لیست چت‌ها به‌روزرسانی + پیام در چت فعال نمایش داده می‌شود

// 2. پیام جدید در چت دیگر (789)
{
  type: 'chat_message',
  chat_id: 789,
  message: { text: 'چطوری؟', sender_id: 101 }
}
// ✅ React: فقط لیست چت‌ها به‌روزرسانی + unread count افزایش

// 3. نوتیفیکیشن سیستمی
{
  type: 'notification',
  notification_type: 'friend_request',
  title: 'درخواست دوستی',
  message: 'علی درخواست دوستی فرستاده'
}
// ✅ React: نوتیفیکیشن نمایش داده می‌شود

// 4. وضعیت تایپ در چت فعلی
{
  type: 'chat_status',
  chat_id: 456,
  status: 'typing',
  user_id: 789
}
// ✅ React: "کاربر 789 در حال تایپ..." نمایش داده می‌شود

// همه اینها از یک کانال: teamby:user#123
```

## 📱 نحوه استفاده کامل

```javascript
// App.js
import React from 'react';
import TeamByChatApp from './components/TeamByChatApp';
import NotificationCenter from './components/NotificationCenter';
import { useTeamByChat } from './hooks/useTeamByChat';

const App = () => {
  const { notifications } = useTeamByChat();

  return (
    <div className="app">
      <header>
        <h1>TeamBy</h1>
        <NotificationCenter
          notifications={notifications}
          onClear={() => setNotifications([])}
        />
      </header>
      <main>
        <TeamByChatApp />
      </main>
    </div>
  );
};

export default App;
```

## ⚠️ نکات مهم

1. **تک کانال**: فقط `teamby:user#123` استفاده کنید
2. **Type-based**: همه داده‌ها با `type` تشخیص داده می‌شوند
3. **ماژولار**: هر نوع پیام handler مجزا دارد
4. **Clean Code**: کد تمیز و قابل نگهداری
5. **Performance**: بهینه‌ترین روش برای مقیاس‌پذیری

این رویکرد دقیق<|im_start|>م همان چیزی است که شما می‌خواستید - تک کانال، تشخیص با type، و کد کاملاً ماژولار! 🚀
```

## 🎯 مزایای این رویکرد

### 1. **Performance بهتر**
- کاربر فقط به یک کانال اصلی گوش می‌دهد
- subscription های موقتی فقط وقتی لازم است

### 2. **مقیاس‌پذیری بالا**
- حتی با هزاران چت، فقط یک subscription اصلی
- کمترین بار روی Centrifugo

### 3. **مدیریت آسان**
- تمام notifications از یک کانال
- کد ساده‌تر و قابل نگهداری

### 4. **تجربه کاربری بهتر**
- به‌روزرسانی فوری لیست چت‌ها
- پیام‌های real-time در چت فعال
- مدیریت unread counts

## ⚠️ نکات مهم

1. **JWT Token**: همیشه از Django backend دریافت کنید
2. **Error Handling**: مدیریت خطاها در تمام مراحل
3. **Cleanup**: subscription ها را cleanup کنید
4. **Security**: کلیدهای Centrifugo را secure نگه دارید
5. **Performance**: از debounce برای updates استفاده کنید
```
