#!/usr/bin/env python3
"""
Centrifugo Client Script
Connects to Centrifugo WebSocket and listens to user channel
Usage: python centrifugo_client.py --user-id 31
"""

import asyncio
import json
import websockets
import argparse
import os
import sys
import django
from datetime import datetime
from asgiref.sync import sync_to_async

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.account.centrifugo import centrifugo_service
from apps.employee.models import Employee


class CentrifugoClient:
    def __init__(self, user_id, ws_url='ws://localhost:8000/connection/websocket'):
        self.user_id = user_id
        self.ws_url = ws_url
        self.token = None
        self.websocket = None
        self.channel = f'teamby:user#{user_id}'
        
    async def get_token(self):
        """Get JWT token for authentication"""
        try:
            # Check if user exists (using sync_to_async)
            employee = await sync_to_async(Employee.objects.get)(id=self.user_id)
            print(f"🔑 Generating token for: {employee.full_name} (ID: {employee.id})")

            # Generate token (using sync_to_async)
            self.token = await sync_to_async(centrifugo_service.generate_token)(self.user_id)
            print(f"✅ Token generated successfully")
            return True

        except Employee.DoesNotExist:
            print(f"❌ Employee with ID {self.user_id} not found")
            return False
        except Exception as e:
            print(f"❌ Error generating token: {str(e)}")
            return False
    
    async def connect(self):
        """Connect to Centrifugo WebSocket"""
        if not await self.get_token():
            return False
            
        try:
            print(f"🔌 Connecting to: {self.ws_url}")
            print(f"📡 Listening to channel: {self.channel}")
            print("=" * 60)
            
            # Connect to WebSocket
            self.websocket = await websockets.connect(self.ws_url)
            
            # Send connection message with token
            connect_message = {
                "id": 1,
                "connect": {
                    "token": self.token,
                    "name": "python-client"
                }
            }
            
            await self.websocket.send(json.dumps(connect_message))
            
            # Wait for connection response
            response = await self.websocket.recv()
            connect_response = json.loads(response)
            
            if connect_response.get('connect', {}).get('client'):
                client_id = connect_response['connect']['client']
                print(f"✅ Connected successfully! Client ID: {client_id}")
                
                # Subscribe to user channel
                await self.subscribe_to_channel()
                
                # Start listening for messages
                await self.listen_for_messages()
                
            else:
                print(f"❌ Connection failed: {connect_response}")
                return False
                
        except Exception as e:
            print(f"❌ Connection error: {str(e)}")
            return False
    
    async def subscribe_to_channel(self):
        """Subscribe to the user channel"""
        try:
            subscribe_message = {
                "id": 2,
                "subscribe": {
                    "channel": self.channel
                }
            }
            
            await self.websocket.send(json.dumps(subscribe_message))
            
            # Wait for subscription response
            response = await self.websocket.recv()
            sub_response = json.loads(response)
            
            if 'subscribe' in sub_response:
                print(f"✅ Subscribed to channel: {self.channel}")
                print(f"🎧 Listening for employee status updates...")
                print("=" * 60)
            else:
                print(f"❌ Subscription failed: {sub_response}")
                
        except Exception as e:
            print(f"❌ Subscription error: {str(e)}")
    
    async def listen_for_messages(self):
        """Listen for incoming messages"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError:
                    print(f"⚠️  Invalid JSON received: {message}")
                except Exception as e:
                    print(f"⚠️  Error handling message: {str(e)}")
                    
        except websockets.exceptions.ConnectionClosed:
            print("🔌 Connection closed")
        except Exception as e:
            print(f"❌ Listening error: {str(e)}")
    
    async def handle_message(self, data):
        """Handle incoming messages"""
        current_time = datetime.now().strftime('%H:%M:%S')
        
        # Handle push messages (actual notifications)
        if 'push' in data:
            push_data = data['push']
            channel = push_data.get('channel', '')
            pub_data = push_data.get('pub', {}).get('data', {})
            
            if channel == self.channel:
                message_type = pub_data.get('type', 'unknown')
                
                if message_type == 'employee_online':
                    employee_id = pub_data.get('employee_id')
                    employee_name = pub_data.get('employee_name')
                    timestamp = pub_data.get('timestamp')
                    
                    print(f"🟢 [{current_time}] EMPLOYEE ONLINE")
                    print(f"   👤 Name: {employee_name}")
                    print(f"   🆔 ID: {employee_id}")
                    print(f"   ⏰ Time: {datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')}")
                    print("-" * 40)
                    
                elif message_type == 'employee_offline':
                    employee_id = pub_data.get('employee_id')
                    employee_name = pub_data.get('employee_name')
                    timestamp = pub_data.get('timestamp')
                    
                    print(f"🔴 [{current_time}] EMPLOYEE OFFLINE")
                    print(f"   👤 Name: {employee_name}")
                    print(f"   🆔 ID: {employee_id}")
                    print(f"   ⏰ Time: {datetime.fromtimestamp(timestamp/1000).strftime('%Y-%m-%d %H:%M:%S')}")
                    print("-" * 40)
                    
                else:
                    print(f"📨 [{current_time}] OTHER MESSAGE")
                    print(f"   Type: {message_type}")
                    print(f"   Data: {json.dumps(pub_data, indent=2)}")
                    print("-" * 40)
        
        # Handle other message types (connection, subscription confirmations, etc.)
        elif any(key in data for key in ['connect', 'subscribe', 'unsubscribe']):
            # These are handled in their respective methods
            pass
        else:
            # Unknown message type
            print(f"📋 [{current_time}] RAW MESSAGE: {json.dumps(data, indent=2)}")
    
    async def close(self):
        """Close the WebSocket connection"""
        if self.websocket:
            await self.websocket.close()
            print("🔌 Connection closed")


async def main():
    parser = argparse.ArgumentParser(description='Centrifugo Client - Listen to employee status updates')
    parser.add_argument('--user-id', type=int, required=True, help='User ID to listen for')
    parser.add_argument('--ws-url', default='ws://localhost:8000/connection/websocket', help='WebSocket URL')
    
    args = parser.parse_args()
    
    print("🚀 Centrifugo Client Starting...")
    print(f"👤 User ID: {args.user_id}")
    print(f"🌐 WebSocket URL: {args.ws_url}")
    print("=" * 60)
    
    client = CentrifugoClient(args.user_id, args.ws_url)
    
    try:
        await client.connect()
    except KeyboardInterrupt:
        print("\n⏹️  Interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
    finally:
        await client.close()


if __name__ == '__main__':
    # Install required packages if not available
    try:
        import websockets
    except ImportError:
        print("❌ websockets package not found. Install it with:")
        print("pip install websockets")
        sys.exit(1)
    
    asyncio.run(main())
