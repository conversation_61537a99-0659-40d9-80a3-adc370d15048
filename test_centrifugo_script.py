#!/usr/bin/env python3
"""
Standalone script to test Centrifugo notifications
Usage: python test_centrifugo_script.py
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.account.centrifugo import centrifugo_service
from apps.employee.models import Employee
from django.db.models import Q


def list_employees():
    """List all employees"""
    print("=== Employee List ===")
    employees = Employee.objects.all().order_by('id')
    
    if not employees.exists():
        print("No employees found")
        return
    
    for emp in employees:
        teams = ', '.join([team.name for team in emp.teams.all()]) if emp.teams.exists() else 'No teams'
        print(f"ID: {emp.id:3d} | Name: {emp.full_name:25s} | Email: {emp.email or 'N/A':30s} | Teams: {teams}")
    
    print(f"\nTotal employees: {employees.count()}")


def show_notification_recipients(employee):
    """Show who will receive notifications for this employee"""
    teams = employee.teams.all()
    employees_with_access = Employee.objects.filter(
        Q(teams__in=teams) | Q(employees_with_permission=employee)
    ).distinct().exclude(id=employee.id)
    
    print(f"\nEmployee Teams: {', '.join([team.name for team in teams]) if teams.exists() else 'None'}")
    
    if employees_with_access.exists():
        print(f"\n=== Notification Recipients ({employees_with_access.count()}) ===")
        for emp in employees_with_access:
            channel = f"teamby:user#{emp.id}"
            print(f"  • {emp.full_name} (ID: {emp.id}) → Channel: {channel}")
    else:
        print("\n⚠️  No recipients found! This employee has no team members or permissions.")
        print("Make sure the employee is in a team or has permission relationships.")


def test_online_notification(employee_id):
    """Test online notification"""
    try:
        employee = Employee.objects.get(id=employee_id)
        
        print(f"\n=== Testing Online Notification ===")
        print(f"Employee: {employee.full_name} (ID: {employee.id})")
        
        show_notification_recipients(employee)
        
        print(f"\n=== Sending Online Notification ===")
        result = centrifugo_service.notify_user_online(employee_id)
        
        if result:
            print("✅ Online notification sent successfully!")
        else:
            print("❌ Failed to send online notification")
            
        return result
        
    except Employee.DoesNotExist:
        print(f"❌ Employee with ID {employee_id} not found")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def test_offline_notification(employee_id):
    """Test offline notification"""
    try:
        employee = Employee.objects.get(id=employee_id)
        
        print(f"\n=== Testing Offline Notification ===")
        print(f"Employee: {employee.full_name} (ID: {employee.id})")
        
        show_notification_recipients(employee)
        
        print(f"\n=== Sending Offline Notification ===")
        result = centrifugo_service.notify_user_offline(employee_id)
        
        if result:
            print("✅ Offline notification sent successfully!")
        else:
            print("❌ Failed to send offline notification")
            
        return result
        
    except Employee.DoesNotExist:
        print(f"❌ Employee with ID {employee_id} not found")
        return False
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


def generate_token(user_id):
    """Generate Centrifugo token"""
    try:
        employee = Employee.objects.get(id=user_id)
        
        print(f"\n=== Generating Centrifugo Token ===")
        print(f"User: {employee.full_name} (ID: {employee.id})")
        
        token = centrifugo_service.generate_token(user_id)
        
        print(f"\n=== Token Generated Successfully ===")
        print(f"Token: {token}")
        print(f"Channel: teamby:user#{user_id}")
        print(f"Expires: 24 hours from now")
        
        print(f"\n=== Connection Info ===")
        print("Use this token to connect to Centrifugo WebSocket")
        print("Example JavaScript code:")
        print(f"""
const centrifuge = new Centrifuge('ws://localhost:8000/connection/websocket', {{
    token: '{token}'
}});

const sub = centrifuge.subscribe('teamby:user#{user_id}', function(message) {{
    console.log('Received:', message.data);
}});

centrifuge.connect();
        """)
        
        return token
        
    except Employee.DoesNotExist:
        print(f"❌ Employee with ID {user_id} not found")
        return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None


def main():
    """Main interactive function"""
    print("🚀 Centrifugo Test Script")
    print("=" * 50)
    
    while True:
        print("\nChoose an option:")
        print("1. List all employees")
        print("2. Test online notification")
        print("3. Test offline notification") 
        print("4. Generate Centrifugo token")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            list_employees()
            
        elif choice == '2':
            employee_id = input("Enter employee ID for online notification: ").strip()
            try:
                employee_id = int(employee_id)
                test_online_notification(employee_id)
            except ValueError:
                print("❌ Invalid employee ID. Please enter a number.")
                
        elif choice == '3':
            employee_id = input("Enter employee ID for offline notification: ").strip()
            try:
                employee_id = int(employee_id)
                test_offline_notification(employee_id)
            except ValueError:
                print("❌ Invalid employee ID. Please enter a number.")
                
        elif choice == '4':
            user_id = input("Enter user ID for token generation: ").strip()
            try:
                user_id = int(user_id)
                generate_token(user_id)
            except ValueError:
                print("❌ Invalid user ID. Please enter a number.")
                
        elif choice == '5':
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter 1-5.")


if __name__ == '__main__':
    main()
