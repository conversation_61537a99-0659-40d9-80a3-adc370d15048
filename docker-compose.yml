version: '3.8'

services:
  web:
    container_name: timee_web
    build: .
    # dockerfile: Dockerfile.prod
    command: gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers=32 --timeout 560
    volumes:
      - hajzee_backend_static_volume:/usr/src/app/static
      # - ./volumes/static_data:/usr/src/app/static/
      - ./volumes/media_data:/usr/src/app/mediafiles/
    ports:
      - "9042:8000"
    env_file:
      - .env.dev
    depends_on:
      - postgres
      - redis

  postgres:
    container_name: timee_db
    ports:
      - "5448:5432"
    image: postgres:14.0
    volumes:
      - ./volumes/postgres_data:/var/lib/postgresql/data/
    env_file:
      - .env.dev

  redis:
    container_name: timee_redis
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  celery:
    container_name: timee_celery
    build: .
    command: celery -A config worker -l info
    volumes:
      - .:/usr/src/app/
    env_file:
      - .env.dev
    depends_on:
      - web
      - redis
      - postgres

  celery-beat:
    container_name: timee_celery_beat
    build: .
    command: celery -A config beat -l info
    volumes:
      - .:/usr/src/app/
    env_file:
      - .env.dev
    depends_on:
      - web
      - redis
      - postgres
      - celery

volumes:
  postgres_data:
  staticfiles:
  redis_data:
  hajzee_backend_static_volume:
    external: true
