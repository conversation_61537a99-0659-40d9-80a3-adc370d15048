---
description: Repository Information Overview
alwaysApply: true
---

# Timee App Information

## Summary
Timee is a Django-based time tracking and employee management application. It provides functionality for tracking employee activities, managing projects, teams, goals, and payroll. The application supports multiple languages (English and Persian) and includes features for messaging, meetings, and activity reporting.

## Structure
- **apps/**: Contains all Django applications (account, activity, employee, projects, teams, etc.)
- **config/**: Django project configuration and settings
- **templates/**: HTML templates for the application
- **static/**: Static files (CSS, JS, images)
- **mediafiles/**: User-uploaded media files
- **utils/**: Utility functions and helpers

## Language & Runtime
**Language**: Python
**Version**: Python 3.9+
**Framework**: Django 4.2+
**Build System**: pip
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- Django 4.2+
- Django REST Framework
- Celery
- PostgreSQL
- Redis
- django-unfold (Admin U<PERSON>)
- django-filer
- django-guardian
- django-cors-headers
- drf-yasg (API documentation)

**Development Dependencies**:
- django-debug-toolbar

## Build & Installation
```bash
# Create and activate virtual environment
python -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Setup database
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Run server
python manage.py runserver
```

## Docker
**Dockerfile**: Dockerfile, Dockerfile.prod
**Image**: Python 3.9-alpine
**Configuration**: Multi-container setup with web, PostgreSQL, Redis, Celery, and Celery-beat services
**Run Command**:
```bash
docker-compose up -d
```

## Database
**Engine**: PostgreSQL
**Configuration**: Configured in .env file with the following variables:
- POSTGRES_DB
- POSTGRES_USER
- POSTGRES_PASSWORD
- POSTGRES_HOST
- POSTGRES_PORT

## API
**Framework**: Django REST Framework
**Documentation**: drf-yasg (Swagger/OpenAPI)
**Authentication**: Token-based authentication

## Internationalization
**Languages**: English (en), Persian (fa)
**Translation**: Django's built-in i18n with gettext

## Background Tasks
**Task Queue**: Celery
**Broker**: Redis
**Schedule**: Celery Beat

## Main Applications
- **account**: User authentication and management
- **activity**: Time tracking and activity monitoring
- **employee**: Employee profile management
- **projects**: Project management
- **teams**: Team organization
- **goals**: Goal setting and tracking
- **payroll**: Salary and payment management
- **message**: Internal messaging system
- **meet**: Meeting scheduling and management