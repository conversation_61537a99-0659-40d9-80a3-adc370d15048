{% extends "employees_work_data.html" %}

{% block extra_scripts %}
<script>
    // Add report text to all activity list items
    document.addEventListener('DOMContentLoaded', function() {
        // Function to add report text display to activity items
        function addReportTextDisplay() {
            const activityItems = document.querySelectorAll('.activity-list-item');
            
            activityItems.forEach(function(item) {
                // Check if this item has a data-report attribute
                const reportText = item.getAttribute('data-report');
                
                if (reportText && reportText.trim() !== '') {
                    // Create report display element
                    const reportDiv = document.createElement('div');
                    reportDiv.className = 'activity-report';
                    reportDiv.innerHTML = reportText.replace(/\n/g, '<br>');
                    
                    // Insert after the activity status
                    const statusDiv = item.querySelector('.activity-status');
                    if (statusDiv) {
                        statusDiv.insertAdjacentElement('afterend', reportDiv);
                    } else {
                        item.appendChild(reportDiv);
                    }
                }
            });
        }
        
        // Call the function
        addReportTextDisplay();
    });
</script>
{% endblock %}