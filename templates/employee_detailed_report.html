{% load static %}
{% load jalali_filters %}
<!DOCTYPE html>
<html lang="fa">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>گزارش تفصیلی فعالیت‌های کارمند</title>

    <!-- External CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
/* Main Styles */
body {
  direction: rtl;
  text-align: right;
  font-family: "Vazirmatn", sans-serif;
  background-color: #f4f6f9;
  color: #333;
}

/* Card Styles */
.card {
  margin-top: 35px;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  border: none;
  background: #ffffff;
  position: relative;
  overflow: hidden;
}

.card-header {
  background: linear-gradient(135deg, #ae64d8, #01353B);
  color: white;
  font-weight: bold;
  padding: 30px;
  text-align: center;
  font-size: 2rem;
}

/* Employee Profile Styles */
.employee-avatar-container {
  position: relative;
  display: flex;
  justify-content: center;
  margin-top: -50px;
  margin-bottom: 20px;
}

.employee-avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 6px solid #ffffff;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  background-color: #fff;
}

.employee-info {
  text-align: center;
  margin-top: 60px;
  margin-bottom: 40px;
}

.employee-name {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-top: 20px;
}

.badge-custom {
  background-color: #6610f2;
  color: #fff;
  padding: 10px 25px;
  border-radius: 25px;
  font-size: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
}

/* Footer Styles */
.footer {
  margin-top: 60px;
  text-align: center;
  color: #555;
  font-size: 1rem;
}

/* Button Styles */
.back-btn {
  background: linear-gradient(135deg, #6c757d, #495057);
  color: white;
  padding: 10px 25px;
  border-radius: 30px;
  text-decoration: none;
  display: inline-block;
  margin-bottom: 15px;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  color: white;
}

/* Section Title Styles */
.section-title {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 25px;
  text-align: center;
  position: relative;
  padding-bottom: 15px;
}

.section-title:after {
  content: "";
  position: absolute;
  width: 100px;
  height: 4px;
  background: linear-gradient(135deg, #0062cc, #6610f2);
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
}

/* Month Summary Styles */
.month-summary {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.month-summary h3 {
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.month-summary .total-hours {
  font-size: 2.5rem;
  font-weight: bold;
  color: #0062cc;
  margin-bottom: 15px;
}

.month-summary .progress {
  height: 15px;
  border-radius: 10px;
  margin-bottom: 15px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.month-summary .progress-bar {
  background: linear-gradient(135deg, #0062cc, #6610f2);
  border-radius: 10px;
}

.month-summary .commitment-info {
  color: #6c757d;
  font-size: 1.1rem;
}

/* Day Card Styles */
.day-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.day-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.day-card:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0.8) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.day-card:hover:before {
  opacity: 0.5;
}

.day-card.expanded:hover:before {
  opacity: 0;
}

.day-header {
  background: linear-gradient(135deg, #0062cc, #6610f2);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 15px 15px 0 0;
}

.day-header.active {
  background: linear-gradient(135deg, #0051a8, #5a0bd0);
  box-shadow: inset 0 -3px 6px rgba(0, 0, 0, 0.1);
}

.day-header.active + .day-entries {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.day-name {
  font-size: 1.3rem;
  font-weight: bold;
}

.day-date {
  font-size: 1rem;
  opacity: 0.9;
}

.day-hours {
  background: rgba(255, 255, 255, 0.2);
  padding: 5px 15px;
  border-radius: 20px;
  font-weight: bold;
}

/* Entry Card Styles */
.entry-card {
  padding: 20px;
  border-bottom: 1px solid #eee;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 15px;
  border-radius: 10px;
  background-color: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
}

.entry-card:last-child {
  margin-bottom: 0;
}

.entry-card:hover {
  background-color: #f8f9fa;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.day-entries {
  background-color: white;
  border-radius: 0 0 15px 15px;
  overflow: hidden;
  transition: max-height 0.5s ease;
}

.entry-time {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  color: #0062cc;
  font-weight: bold;
}

.entry-time span {
  background: #e9ecef;
  padding: 8px 15px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
}

.entry-time span i {
  color: #0062cc;
}

.entry-report {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 15px;
  position: relative;
  border-right: 3px solid #0062cc;
}

.entry-report::before {
  content: '"';
  font-size: 2.5rem;
  color: #0062cc;
  opacity: 0.1;
  position: absolute;
  top: -5px;
  right: 10px;
}

.entry-report::after {
  content: '"';
  font-size: 2.5rem;
  color: #0062cc;
  opacity: 0.1;
  position: absolute;
  bottom: -35px;
  left: 10px;
}

.entry-duration {
  text-align: left;
  color: #6c757d;
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.entry-duration::before {
  content: "\f017";
  font-family: "Font Awesome 5 Free";
  margin-right: 8px;
  color: #6c757d;
}

/* No Entries Styles */
.no-entries {
  text-align: center;
  padding: 30px;
  color: #6c757d;
  font-style: italic;
  background-color: #f8f9fa;
  border-radius: 10px;
  margin: 15px;
  border: 1px dashed #dee2e6;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.no-entries:before {
  content: "\f7d5";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 2rem;
  color: #adb5bd;
  margin-bottom: 10px;
}

/* Month Navigation Styles */
.month-navigation {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.month-btn {
  background: linear-gradient(135deg, #0062cc, #6610f2);
  color: white;
  padding: 10px 25px;
  border-radius: 30px;
  text-decoration: none;
  transition: all 0.3s;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.month-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  color: white;
}

.current-month {
  font-size: 1.5rem;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
}

/* Toggle Icon Styles */
.toggle-icon {
  transition: transform 0.3s ease;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
}

.day-header.active .toggle-icon {
  transform: rotate(180deg);
}

/* Animation Styles */
@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 1000px;
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    max-height: 1000px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.day-entries.expanding {
  animation: slideDown 0.5s ease forwards;
}

.day-entries.collapsing {
  animation: slideUp 0.5s ease forwards;
}

/* Hover Effects */
.day-header:hover {
  background: linear-gradient(135deg, #0062cc, #7a43f2);
}

/* Ripple Effect */
.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(2);
    opacity: 0;
  }
}

/* Selected Day Details Styles */
.selected-day-details {
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
  border-right: 5px solid #0062cc;
}

.selected-day-details .section-title {
  color: #0062cc;
  margin-top: 0;
}

.day-summary {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.day-summary .day-hours {
  font-size: 1.2rem;
  color: #333;
  background: #f8f9fa;
  padding: 10px 20px;
  border-radius: 30px;
  display: inline-block;
}

.day-summary .day-hours i {
  color: #0062cc;
  margin-left: 5px;
}

.day-entries {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Calendar Styles */
.calendar-container {
  margin-bottom: 30px;
}

.calendar {
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: linear-gradient(135deg, #0062cc, #6610f2);
  color: white;
  padding: 15px 0;
}

.weekday {
  text-align: center;
  font-weight: bold;
}

.calendar-body {
  padding: 10px;
}

.calendar-week {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
  margin-bottom: 5px;
}

.calendar-day {
  aspect-ratio: 1;
  border-radius: 10px;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 5px;
}

.calendar-day:not(.empty):hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background: #e9ecef;
}

.calendar-day.empty {
  background: transparent;
  cursor: default;
}

.calendar-day.today {
  background: #e8f4ff;
  border: 2px solid #0062cc;
}

.calendar-day.is-selected {
  background: #e0f7fa;
  border: 2px solid #00bcd4;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.day-number {
  font-weight: bold;
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.day-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(135deg, #0062cc, #6610f2);
  opacity: 0.7;
  border-radius: 0 0 10px 10px;
  transition: height 0.3s ease;
}

.day-hours {
  font-size: 0.8rem;
  color: #6c757d;
  margin-top: 5px;
  z-index: 1;
}

/* Modal Styles */
.custom-modal .modal-content {
  border-radius: 15px;
  border: none;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.custom-modal .modal-header {
  background: linear-gradient(135deg, #0062cc, #6610f2);
  color: white;
  border-radius: 0;
  border-bottom: none;
  padding: 15px 20px;
}

.custom-modal .modal-body {
  padding: 20px;
}

.custom-modal .modal-footer {
  border-top: none;
  padding: 15px 20px;
}

.custom-modal .btn-secondary {
  background: linear-gradient(135deg, #6c757d, #495057);
  border: none;
  border-radius: 30px;
  padding: 8px 25px;
  transition: all 0.3s;
}

.custom-modal .btn-secondary:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Day Info in Modal */
.day-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.day-date-display {
  font-size: 1.2rem;
  color: #6c757d;
  margin-bottom: 10px;
}

.day-hours-display {
  font-size: 1.8rem;
  font-weight: bold;
  color: #0062cc;
}

.day-entries-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

.day-entries-container::-webkit-scrollbar {
  width: 6px;
}

.day-entries-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.day-entries-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 10px;
}

.day-entries-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.no-entries-message {
  text-align: center;
  padding: 30px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.no-entries-message i {
  font-size: 2rem;
  color: #adb5bd;
}

/* Fix for modal backdrop */
.modal-backdrop {
  opacity: 0.5 !important;
  z-index: 1040 !important;
}

.modal {
  z-index: 1050 !important;
}

/* Prevent body scrolling when modal is open */
body.modal-open {
  overflow: hidden !important;
  padding-right: 0 !important;
}

/* Ensure modal is visible and interactive */
.custom-modal {
  pointer-events: auto !important;
}

.custom-modal .modal-dialog {
  pointer-events: auto !important;
  z-index: 1055 !important;
}

    </style>     
</head>
<body>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-11">
            <!-- Main Report Card -->
            <div class="card" data-aos="fade-up" data-aos-duration="1000">
                <div class="card-header">
                    گزارش تفصیلی فعالیت‌ها
                </div>
                <div class="card-body">
                    <!-- Employee Profile Component -->
                    {% include "components/employee_profile.html" %}

                    <!-- Back Button -->
                    <div class="period-nav">
                        <a href="{% url 'admin:employee_export_html' employee.id %}" class="back-btn">
                            <i class="fas fa-arrow-right ml-2"></i> بازگشت به گزارش کلی
                        </a>
                    </div>

                    <!-- Month Navigation Component -->
                    {% include "components/month_navigation.html" %}

                    <!-- Month Summary Component -->
                    {% include "components/month_summary.html" %}

                    <!-- Calendar Section -->
                    <h3 class="section-title">تقویم ماهانه</h3>
                    {% include "components/calendar.html" %}

                    <!-- Selected Day Details Section -->
                    {% if show_day_details %}
                    <div class="selected-day-details" data-aos="fade-up">
                        <h3 class="section-title">{{ selected_day_title }}</h3>
                        <div class="day-summary">
                            <div class="day-hours">
                                <i class="fas fa-clock"></i> مجموع ساعات: {{ selected_day_hours_display }}
                            </div>
                        </div>

                        <div class="day-entries">
                            {% for entry in selected_day_entries %}
                            <div class="entry-card" data-aos="fade-up" data-aos-delay="{{ forloop.counter0|add:1|multiply:100 }}">
                                <div class="entry-time">
                                    <div><i class="fas fa-play-circle"></i> {{ entry.start }}</div>
                                    <div><i class="fas fa-stop-circle"></i> {{ entry.end }}</div>
                                </div>
                                {% if entry.report_text %}
                                <div class="entry-report">
                                    {{ entry.report_text|linebreaks }}
                                </div>
                                {% endif %}
                                <div class="entry-duration">
                                    <i class="fas fa-hourglass-half"></i> مدت زمان: {{ entry.duration }}
                                </div>
                            </div>
                            {% empty %}
                            <div class="alert alert-info text-center">
                                در این روز فعالیتی ثبت نشده است.
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Daily Reports Section -->
                    <h3 class="section-title">گزارش روزانه فعالیت‌ها</h3>

                    {% for day in daily_entries %}
                        {% include "components/day_entry.html" %}
                    {% empty %}
                    <div class="alert alert-info text-center">
                        در این ماه فعالیتی ثبت نشده است.
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Footer -->
            <div class="footer">
                <p>سیستم گزارش‌دهی تفصیلی ساعات کاری</p>
            </div>
        </div>
    </div>
</div>

<!-- External JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<!-- Custom JavaScript -->
<script>
// Initialize AOS with custom settings
document.addEventListener("DOMContentLoaded", function () {
  // Initialize AOS animation library
  AOS.init({
    duration: 800,
    easing: "ease-out-cubic",
    once: true,
    offset: 50,
    delay: 50,
  });

  // Optional: Automatically open the first day card
  const firstDayHeader = document.querySelector(".day-header");
  if (firstDayHeader) {
    // Uncomment the line below to automatically open the first day
    // setTimeout(() => toggleDayEntries(firstDayHeader), 1000);
  }

  // Initialize calendar day click handlers
  initCalendarDayClicks();
});

/**
 * Toggle day entries visibility with animation
 * @param {HTMLElement} header - The day header element that was clicked
 */
function toggleDayEntries(header) {
  // Toggle active class on header
  header.classList.toggle("active");

  // Get the parent day card
  const dayCard = header.closest(".day-card");

  // Find the entries container
  const entriesContainer = header.nextElementSibling;

  // Toggle visibility with animation
  if (
    entriesContainer.style.display === "none" ||
    entriesContainer.style.display === ""
  ) {
    // Show entries with animation
    entriesContainer.style.display = "block";
    entriesContainer.classList.add("expanding");
    entriesContainer.classList.remove("collapsing");

    // Add expanded class to day card
    dayCard.classList.add("expanded");

    // Add ripple effect
    createRippleEffect(header);
  } else {
    // Hide entries with animation
    entriesContainer.classList.add("collapsing");
    entriesContainer.classList.remove("expanding");

    // Remove expanded class from day card
    dayCard.classList.remove("expanded");

    // Wait for animation to complete before hiding
    setTimeout(() => {
      entriesContainer.style.display = "none";
    }, 500);
  }
}

/**
 * Create ripple effect on click
 * @param {HTMLElement} element - The element to apply the ripple effect to
 */
function createRippleEffect(element) {
  const ripple = document.createElement("span");
  ripple.classList.add("ripple-effect");

  const rect = element.getBoundingClientRect();
  const size = Math.max(rect.width, rect.height);

  ripple.style.width = ripple.style.height = `${size}px`;
  ripple.style.left = `${event.clientX - rect.left - size / 2}px`;
  ripple.style.top = `${event.clientY - rect.top - size / 2}px`;

  element.appendChild(ripple);

  setTimeout(() => {
    ripple.remove();
  }, 600);
}

/**
 * Initialize calendar day click handlers
 */
function initCalendarDayClicks() {
  const calendarDays = document.querySelectorAll(".calendar-day:not(.empty)");

  calendarDays.forEach((day) => {
    day.addEventListener("click", function () {
      const dayIndex = this.getAttribute("data-day-index");
      const date = this.getAttribute("data-date");

      // Find the corresponding day data in the daily_entries array
      const dayData = window.dailyEntriesData.find(
        (entry) => entry.date === date
      );

      if (dayData) {
        showDayDetailsModal(dayData);
      }
    });
  });
}

/**
 * Show day details in modal
 * @param {Object} dayData - The day data object
 */
function showDayDetailsModal(dayData) {
  // Get modal elements
  const modalElement = document.getElementById("dayDetailsModal");
  const modalTitle = document.getElementById("dayDetailsModalLabel");
  const dayDate = document.getElementById("dayDate");
  const dayHours = document.getElementById("dayHours");
  const dayEntries = document.getElementById("dayEntries");
  const noEntries = document.getElementById("noEntries");

  // Set modal title and date
  modalTitle.textContent = `گزارش فعالیت‌های روز ${dayData.weekday}`;
  dayDate.textContent = dayData.date;
  dayHours.textContent = `${dayData.hours_display} ساعت`;

  // Clear previous entries
  dayEntries.innerHTML = "";

  // Check if there are entries
  if (dayData.entries && dayData.entries.length > 0) {
    // Hide no entries message
    noEntries.style.display = "none";

    // Add entries to modal with animation delay
    dayData.entries.forEach((entry, index) => {
      const entryCard = document.createElement("div");
      entryCard.className = "entry-card";
      entryCard.style.animation = `fadeIn 0.3s ease forwards ${index * 0.1}s`;
      entryCard.style.opacity = "0";

      let entryHtml = `
                <div class="entry-time">
                    <span><i class="fas fa-play-circle"></i> ${entry.start}</span>
                    <span><i class="fas fa-stop-circle"></i> ${entry.end}</span>
                </div>
            `;

      if (entry.report_text) {
        entryHtml += `
                    <div class="entry-report">
                        ${entry.report_text.replace(/\n/g, "<br>")}
                    </div>
                `;
      }

      entryHtml += `
                <div class="entry-duration">
                    مدت زمان: ${entry.duration} ساعت
                </div>
            `;

      entryCard.innerHTML = entryHtml;
      dayEntries.appendChild(entryCard);
    });
  } else {
    // Show no entries message
    noEntries.style.display = "flex";
  }

  // Fix for backdrop issue - ensure old backdrops are removed
  const oldBackdrops = document.querySelectorAll(".modal-backdrop");
  oldBackdrops.forEach((backdrop) => {
    backdrop.parentNode.removeChild(backdrop);
  });

  // Remove any existing modal-open class and inline styles
  document.body.classList.remove("modal-open");
  document.body.style.overflow = "";
  document.body.style.paddingRight = "";

  // Create a new modal instance and show it
  if (window.currentModal) {
    try {
      window.currentModal.dispose();
    } catch (e) {
      console.log("Error disposing modal:", e);
    }
  }

  // Make sure the modal element is properly reset
  modalElement.style.display = "block";
  modalElement.classList.remove("fade");
  setTimeout(() => {
    modalElement.classList.add("fade");
    window.currentModal = new bootstrap.Modal(modalElement, {
      backdrop: true,
      keyboard: true,
      focus: true,
    });
    window.currentModal.show();
  }, 100);

  // Add event listener to clean up when modal is hidden
  modalElement.addEventListener(
    "hidden.bs.modal",
    function () {
      // Remove all backdrop elements
      const backdrops = document.querySelectorAll(".modal-backdrop");
      backdrops.forEach((backdrop) => {
        backdrop.parentNode.removeChild(backdrop);
      });

      // Reset body classes and styles
      document.body.classList.remove("modal-open");
      document.body.style.overflow = "";
      document.body.style.paddingRight = "";
    },
    { once: true }
  );
}

</script>

<!-- Hidden input for employee ID -->
<input type="hidden" id="employee-id" value="{{ employee.id }}">

<!-- Pass daily entries data to JavaScript -->
<script>
    // Make daily entries data available to JavaScript
    window.dailyEntriesData = [
        {% for day in daily_entries %}
        {
            date: "{{ day.date }}",
            weekday: "{{ day.weekday }}",
            hours: {{ day.hours }},
            hours_display: "{{ day.hours_display }}",
            entries: [
                {% for entry in day.entries %}
                {
                    start: "{{ entry.start }}",
                    end: "{{ entry.end }}",
                    report_text: `{{ entry.report_text|escapejs }}`,
                    duration: "{{ entry.duration }}"
                }{% if not forloop.last %},{% endif %}
                {% endfor %}
            ]
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    // Initialize AOS (Animate On Scroll)
    document.addEventListener('DOMContentLoaded', function() {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true
        });
    });

</script>
</body>
</html>