{% comment %}
    Component: Calendar
    Description: Displays a monthly calendar with daily activity indicators
{% endcomment %}

<div class="calendar-container">
    <div class="calendar">
        <div class="calendar-header">
            <div class="weekday">شنبه</div>
            <div class="weekday">یکشنبه</div>
            <div class="weekday">دوشنبه</div>
            <div class="weekday">سه‌شنبه</div>
            <div class="weekday">چهارشنبه</div>
            <div class="weekday">پنج‌شنبه</div>
            <div class="weekday">جمعه</div>
        </div>
        <div class="calendar-body">
            {% for week in calendar_weeks %}
            <div class="calendar-week">
                {% for day in week %}
                <div class="calendar-day {% if day.is_empty %}empty{% endif %} {% if day.is_today %}today{% endif %} {% if day.is_selected %}is-selected{% endif %}"
                     {% if not day.is_empty %}data-date="{{ day.date }}" data-day-index="{{ day.index }}"{% endif %}>
                    {% if not day.is_empty %}
                    <div class="day-number">{{ day.day }}</div>
                    {% if day.has_entries %}
                    <div class="day-indicator" style="height: {{ day.activity_percentage }}%"></div>
                    <div class="day-hours">{{ day.hours_display }}</div>
                    {% endif %}
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- SweetAlert2 Library -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Day Details Modal JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const calendarDays = document.querySelectorAll('.calendar-day:not(.empty)');

        // Modal functionality
        function openDayDetails(date, dayIndex) {
            // Show loading state
            Swal.fire({
                title: 'در حال بارگذاری...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Fetch day details from server
            fetchDayDetails(date, dayIndex)
                .then(data => {
                    // Close loading dialog
                    Swal.close();

                    // Format date for display
                    const formattedDate = formatPersianDate(date);

                    // Create modal content
                    const modalContent = createModalContent(data, formattedDate);

                    // Show modal with SweetAlert2
                    Swal.fire({
                        title: 'گزارش فعالیت‌های روز',
                        html: modalContent,
                        width: '800px',
                        showCloseButton: true,
                        showConfirmButton: true,
                        confirmButtonText: 'بستن',
                        buttonsStyling: true,
                        customClass: {
                            container: 'day-details-swal-container',
                            popup: 'day-details-swal-popup',
                            header: 'day-details-swal-header',
                            title: 'day-details-swal-title',
                            closeButton: 'day-details-swal-close',
                            content: 'day-details-swal-content',
                            confirmButton: 'btn btn-secondary'
                        },
                        backdrop: `rgba(0,0,0,0.4)`,
                        animation: true
                    });
                })
                .catch(error => {
                    console.error('Error fetching day details:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'خطا',
                        text: 'مشکلی در دریافت اطلاعات روز رخ داده است.',
                        confirmButtonText: 'بستن'
                    });
                });
        }

        function fetchDayDetails(date, dayIndex) {
            // Get employee ID from the page
            const employeeId = document.getElementById('employee-id').value;

            // API endpoint with employee_id parameter
            return fetch(`/api/day-details/?date=${date}&day_index=${dayIndex}&employee_id=${employeeId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                });
        }

        function createModalContent(data, formattedDate) {
            let content = `
                <div class="day-info">
                    <div class="day-date-display">${formattedDate}</div>
                    ${data.total_hours ? `<div class="day-hours-display">${data.total_hours} ساعت</div>` : ''}
                </div>
            `;

            if (data.entries && data.entries.length > 0) {
                content += '<div class="day-entries-container">';
                data.entries.forEach(entry => {
                    // Format description text with line breaks
                    const description = entry.description ? entry.description.replace(/\n/g, '<br>') : '';

                    content += `
                        <div class="day-entry">
                            <div class="entry-title">${entry.title || 'بدون عنوان'}</div>
                            <div class="entry-details">
                                <div class="entry-time">${entry.start_time} - ${entry.end_time}</div>
                                <div class="entry-duration">${entry.duration} ساعت</div>
                            </div>
                            ${description ? `<div class="entry-description">${description}</div>` : ''}
                        </div>
                    `;
                });
                content += '</div>';
            } else {
                content += `
                    <div class="no-entries-message">
                        <i class="fas fa-calendar-times"></i>
                        در این روز فعالیتی ثبت نشده است.
                    </div>
                `;
            }

            return content;
        }

        function formatPersianDate(dateStr) {
            // Parse the Persian date (format: YYYY/MM/DD)
            const [year, month, day] = dateStr.split('/');

            // Get month name based on month number
            const persianMonths = [
                'فروردین', 'اردیبهشت', 'خرداد', 'تیر', 'مرداد', 'شهریور',
                'مهر', 'آبان', 'آذر', 'دی', 'بهمن', 'اسفند'
            ];

            const monthName = persianMonths[parseInt(month) - 1];

            // Format as "DD Month YYYY"
            return `${day} ${monthName} ${year}`;
        }

        // Event listeners
        calendarDays.forEach(day => {
            day.addEventListener('click', function() {
                const date = this.dataset.date;
                const dayIndex = this.dataset.dayIndex;
                openDayDetails(date, dayIndex);
            });
        });
    });
</script>

<!-- Day Details CSS -->
<style>
    /* SweetAlert2 Custom Styles */
    .day-details-swal-container {
        z-index: 1060;
        direction: rtl;
    }

    .day-details-swal-popup {
        padding: 0;
        border-radius: 10px;
    }

    .day-details-swal-header {
        border-bottom: 1px solid #eee;
        padding: 16px 20px;
    }

    .day-details-swal-title {
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        color: #333;
    }

    .day-details-swal-close {
        color: #666 !important;
        font-size: 1.5rem !important;
    }

    .day-details-swal-content {
        padding: 20px;
        text-align: right;
    }

    /* Day Details Content Styles */
    .day-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .day-date-display {
        font-size: 1.1rem;
        font-weight: 500;
    }

    .day-hours-display {
        font-size: 1.1rem;
        color: #2c7be5;
        font-weight: 500;
    }

    .day-entries-container {
        display: flex;
        flex-direction: column;
        gap: 15px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .day-entry {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 15px;
        border-right: 4px solid #2c7be5;
        text-align: right;
    }

    .entry-title {
        font-weight: 600;
        margin-bottom: 8px;
    }

    .entry-details {
        display: flex;
        justify-content: space-between;
        font-size: 0.9rem;
        color: #666;
    }

    .entry-description {
        margin-top: 10px;
        font-size: 0.9rem;
        color: #444;
    }

    .no-entries-message {
        text-align: center;
        padding: 30px;
        color: #888;
    }

    .no-entries-message i {
        display: block;
        font-size: 2.5rem;
        margin-bottom: 15px;
        color: #ccc;
    }
</style>