{% comment %}
    Component: Day Entry
    Description: Displays a single day card with all its time entries
{% endcomment %}

<div class="day-card" data-aos="fade-up" data-aos-delay="{{ forloop.counter|add:100 }}">
    <div class="day-header" onclick="toggleDayEntries(this)" style="cursor: pointer;">
        <div>
            <div class="day-name">{{ day.weekday }}</div>
            <div class="day-date">{{ day.date }}</div>
        </div>
        <div class="d-flex align-items-center">
            <div class="day-hours me-3">{{ day.hours_display }} ساعت</div>
            <i class="fas fa-chevron-down toggle-icon"></i>
        </div>
    </div>

    <div class="day-entries" style="display: none;">
        {% if day.entries %}
            {% for entry in day.entries %}
            <div class="entry-card">
                <div class="entry-time">
                    <span><i class="fas fa-play-circle"></i> {{ entry.start }}</span>
                    <span><i class="fas fa-stop-circle"></i> {{ entry.end }}</span>
                </div>

                {% if entry.report_text %}
                <div class="entry-report">
                    {{ entry.report_text|linebreaks }}
                </div>
                {% endif %}

                <div class="entry-duration">
                    مدت زمان: {{ entry.duration }} ساعت
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-entries">
                در این روز فعالیتی ثبت نشده است.
            </div>
        {% endif %}
    </div>
</div>