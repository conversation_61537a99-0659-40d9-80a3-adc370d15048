<!DOCTYPE html>
<html lang="fa">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Vazirmatn:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" rel="stylesheet">
    <title>گزارش ساعات فعال کارمند</title>
    <style>
        body {
            direction: rtl;
            text-align: right;
            font-family: 'Vazirmatn', sans-serif;
            background-color: #f4f6f9;
            color: #333;
        }
        .card {
            margin-top: 35px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: none;
            background: #ffffff;
            position: relative;
            overflow: hidden;
        }
        .card-header {
            background: linear-gradient(135deg, #ae64d8, #01353B);
            color: white;
            font-weight: bold;
            padding: 30px;
            text-align: center;
            font-size: 2rem;
        }
        .employee-avatar-container {
            position: relative;
            display: flex;
            justify-content: center;
            margin-top: -50px;
            margin-bottom: 20px;
        }
        .employee-avatar {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            border: 6px solid #ffffff;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            background-color: #fff;
        }
        .stat-box {
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease-in-out;
            position: relative;
            cursor: pointer;
        }
        .stat-box:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        .stat-box h4 {
            color: #ae64d8;
            margin-bottom: 15px;
            font-weight: bold;
            font-size: 1.5rem;
        }
        .stat-box i {
            color: #01353B;
            margin-bottom: 20px;
            font-size: 3.5rem;
        }
        .footer {
            margin-top: 60px;
            text-align: center;
            color: #555;
            font-size: 1rem;
        }
        .row {
            margin-bottom: 30px;
        }
        .badge-custom {
            background-color: #ae64d8;
            color: #fff;
            padding: 10px 25px;
            border-radius: 25px;
            font-size: 1rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-top: 10px;
        }
        .employee-info {
            text-align: center;
            margin-top: 60px;
            margin-bottom: 40px;
        }
        .employee-name {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-top: 20px;
        }
        .activity-details {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .activity-details h4 {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 25px;
        }
        .activity-list {
            list-style: none;
            padding: 0;
        }
        .activity-list-item {
            background: #ffffff;
            padding: 25px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: transform 0.3s ease-in-out;
        }
        .activity-list-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
        }
        .activity-time {
            font-size: 1.2rem;
            font-weight: bold;
            color: #ae64d8;
        }
        .activity-status {
            font-size: 1.2rem;
            color: #333;
            background: #f1f1f1;
            padding: 10px 15px;
            border-radius: 10px;
        }
        
        /* New styles for tables */
        .table-container {
            margin-top: 30px;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            padding: 30px;
            overflow: hidden;
        }
        
        .custom-table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: separate;
            border-spacing: 0 10px;
        }
        
        .custom-table thead th {
            background: linear-gradient(135deg, #ae64d8, #01353B);
            color: white;
            padding: 15px;
            font-size: 1.1rem;
            text-align: center;
            border: none;
            border-radius: 10px;
        }
        
        .custom-table tbody td {
            padding: 20px 15px;
            vertical-align: middle;
            border: none;
            background: #f8f9fa;
            font-size: 1.1rem;
            text-align: center;
        }
        
        .custom-table tbody tr {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            border-radius: 10px;
            transition: transform 0.2s ease-in-out;
        }
        
        .custom-table tbody tr:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .custom-table tbody td:first-child {
            border-top-right-radius: 10px;
            border-bottom-right-radius: 10px;
        }
        
        .custom-table tbody td:last-child {
            border-top-left-radius: 10px;
            border-bottom-left-radius: 10px;
        }
        
        .detail-btn {
            background: linear-gradient(135deg, #ae64d8, #01353B);
            color: white;
            padding: 8px 20px;
            border-radius: 30px;
            font-size: 0.9rem;
            border: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        .detail-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .hours-value {
            font-weight: bold;
            color: #ae64d8;
            font-size: 1.2rem;
        }
        
        .period-nav {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 10px 25px;
            border-radius: 30px;
            text-decoration: none;
            display: inline-block;
            margin-bottom: 15px;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .back-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .section-title {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
            padding-bottom: 15px;
        }
        
        .section-title:after {
            content: '';
            position: absolute;
            width: 100px;
            height: 4px;
            background: linear-gradient(135deg, #ae64d8, #01353B);
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .total-hours {
            text-align: center;
            margin-bottom: 30px;
            padding: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .total-hours span {
            font-size: 2rem;
            font-weight: bold;
            color: #ae64d8;
        }
        
        .total-hours p {
            color: #6c757d;
            margin-top: 5px;
            font-size: 1.1rem;
        }

        .activity-report {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
            position: relative;
            font-size: 0.95rem;
            line-height: 1.6;
            color: #333;
        }

        .activity-report::before {
            content: '"';
            font-size: 3rem;
            color: #ae64d8;
            opacity: 0.2;
            position: absolute;
            top: -10px;
            right: 10px;
        }

        .activity-report::after {
            content: '"';
            font-size: 3rem;
            color: #ae64d8;
            opacity: 0.2;
            position: absolute;
            bottom: -40px;
            left: 10px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-xl-10 col-lg-11">
            <div class="card" data-aos="fade-up" data-aos-duration="1000">
                <div class="card-header">
                    گزارش ساعات فعال
                </div>
                <div class="card-body">
                    <div class="employee-avatar-container">
                        {% if employee.avatar %}
                        <img src="{{ employee.avatar.url }}" class="employee-avatar" alt="Avatar">
                        {% else %}
                        <div class="employee-avatar d-flex align-items-center justify-content-center">
                            <i class="fas fa-user fa-4x text-primary"></i>
                        </div>
                        {% endif %}
                    </div>
                    <div class="employee-info">
                        <p class="employee-name">{{ employee.full_name }}</p>
                        <span class="badge-custom">تاریخ امروز (شمسی): {{ today_jalali }}</span>
                    </div>
                    
                    {% if period == 'week_day' %}
                        <div class="period-nav">
                            <a href="?period=week" class="back-btn">
                                <i class="fas fa-arrow-right ml-2"></i> بازگشت به گزارش هفتگی
                            </a>
                        </div>
                        <h3 class="section-title">{{ period_title }}</h3>
                        
                        {% if entries %}
                            <div class="activity-details mt-4">
                                <div class="total-hours">
                                    <p>مجموع ساعات کاری:</p>
                                    {% if day_hours_display %}
                                        <span>{{ day_hours_display }} ساعت</span>
                                    {% else %}
                                        {% with day_index=request.GET.day|add:"0" %}
                                            {% for day in daily_hours %}
                                                {% if forloop.counter0 == day_index %}
                                                    <span>{{ day.hours_display }} ساعت</span>
                                                {% endif %}
                                            {% endfor %}
                                        {% endwith %}
                                    {% endif %}
                                </div>
                                <h4>جزئیات فعالیت‌ها</h4>
                                <ul class="activity-list">
                                    {% for entry in entries %}
                                        <li class="activity-list-item">
                                            <div class="activity-time">
                                                <strong>شروع:</strong> {{ entry.start }}
                                            </div>
                                            <div class="activity-status">
                                                <strong>پایان:</strong> {{ entry.end }}
                                            </div>
                                        </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% else %}
                            {% with day_index=request.GET.day|add:"0" %}
                                {% for day in daily_hours %}
                                    {% if forloop.counter0 == day_index %}
                                        {% if day.hours > 0 %}
                                            <div class="activity-details mt-4">
                                                <div class="total-hours">
                                                    <p>مجموع ساعات کاری:</p>
                                                    <span>{{ day.hours_display }} ساعت</span>
                                                </div>
                                                <h4>جزئیات فعالیت‌ها</h4>
                                                <ul class="activity-list">
                                                    {% for entry in day.entries %}
                                                        <li class="activity-list-item">
                                                            <div class="activity-time">
                                                                <strong>شروع:</strong> {{ entry.start }}
                                                            </div>
                                                            <div class="activity-status">
                                                                <strong>پایان:</strong> {{ entry.end }}
                                                            </div>
                                                        </li>
                                                    {% empty %}
                                                        <div class="alert alert-info text-center">
                                                            اطلاعات جزئی در دسترس نیست، اما در این روز {{ day.hours_display }} ساعت فعالیت ثبت شده است.
                                                        </div>
                                                    {% endfor %}
                                                </ul>
                                            </div>
                                        {% else %}
                                            <div class="alert alert-info text-center mt-5">
                                                در این روز فعالیتی ثبت نشده است.
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                {% empty %}
                                    <div class="alert alert-info text-center mt-5">
                                        در این روز فعالیتی ثبت نشده است.
                                    </div>
                                {% endfor %}
                            {% endwith %}
                        {% endif %}
                    
                    {% elif period == 'week' or period == 'month_week' %}
                        {% if period == 'month_week' %}
                            <div class="period-nav">
                                <a href="?period=month" class="back-btn">
                                    <i class="fas fa-arrow-right ml-2"></i> بازگشت به گزارش ماهانه
                                </a>
                            </div>
                        {% endif %}
                        
                        <h3 class="section-title">{{ period_title|default:"گزارش هفتگی" }}</h3>
                        
                        <div class="table-container">
                            <div class="total-hours">
                                <p>مجموع ساعات کاری هفته:</p>
                                {% if total_hours_display %}
                                    <span>{{ total_hours_display }} ساعت</span>
                                {% elif daily_hours %}
                                    {% with total_hours=0 %}
                                        {% for day in daily_hours %}
                                            {% with total_hours=total_hours|add:day.hours %}
                                                {% if forloop.last %}
                                                    <span>{{ week_hours_display }} ساعت</span>
                                                {% endif %}
                                            {% endwith %}
                                        {% endfor %}
                                    {% endwith %}
                                {% else %}
                                    <span>{{ week_hours_display }} ساعت</span>
                                {% endif %}
                            </div>
                            <table class="custom-table">
                                <thead>
                                    <tr>
                                        <th>روز هفته</th>
                                        <th>تاریخ</th>
                                        <th>مجموع ساعات</th>
                                        <th>جزئیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for day in daily_hours %}
                                    <tr>
                                        <td>{{ day.weekday }}</td>
                                        <td>{{ day.date }}</td>
                                        <td class="hours-value">{{ day.hours_display }} ساعت</td>
                                        <td>
                                            <a href="?period=week_day&day={{ forloop.counter0 }}{% if request.GET.week %}&week={{ request.GET.week }}{% endif %}" class="detail-btn">
                                                جزئیات بیشتر
                                                <i class="fas fa-arrow-left ms-2"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if entries and not request.GET.week %}
                            <div class="activity-details mt-5">
                                <h4>تمام فعالیت‌های این هفته</h4>
                                <ul class="activity-list">
                                    {% for entry in entries %}
                                        <li class="activity-list-item">
                                            <div class="activity-time">
                                                <strong>شروع:</strong> {{ entry.start }}
                                            </div>
                                            <div class="activity-status">
                                                <strong>پایان:</strong> {{ entry.end }}
                                            </div>
                                        </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    
                    {% elif period == 'month' %}
                        <h3 class="section-title">گزارش ماهانه</h3>
                        
                        <div class="table-container">
                            <div class="total-hours">
                                <p>مجموع ساعات کاری ماه:</p>
                                <span>{{ month_hours_display }} ساعت</span>
                            </div>
                            <table class="custom-table">
                                <thead>
                                    <tr>
                                        <th>هفته</th>
                                        <th>بازه زمانی</th>
                                        <th>مجموع ساعات</th>
                                        <th>جزئیات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for week in weekly_hours %}
                                    <tr>
                                        <td>هفته {{ week.week_number }}</td>
                                        <td>{{ week.start_date }} تا {{ week.end_date }}</td>
                                        <td class="hours-value">{{ week.hours_display }} ساعت</td>
                                        <td>
                                            <a href="?period=month_week&week={{ forloop.counter0 }}" class="detail-btn">
                                                جزئیات بیشتر
                                                <i class="fas fa-arrow-left ms-2"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        {% if entries %}
                            <div class="activity-details mt-5">
                                <h4>تمام فعالیت‌های این ماه</h4>
                                <ul class="activity-list">
                                    {% for entry in entries %}
                                        <li class="activity-list-item">
                                            <div class="activity-time">
                                                <strong>شروع:</strong> {{ entry.start }}
                                            </div>
                                            <div class="activity-status">
                                                <strong>پایان:</strong> {{ entry.end }}
                                            </div>
                                        </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}
                    
                    {% elif period == 'today' %}
                        <h3 class="section-title">گزارش روزانه</h3>
                        
                        <div class="activity-details mt-4">
                            <div class="total-hours">
                                <p>مجموع ساعات کاری امروز:</p>
                                <span>{{ today_hours_display }} ساعت</span>
                            </div>
                            <h4>جزئیات فعالیت‌های امروز</h4>
                            {% if entries %}
                                <ul class="activity-list">
                                    {% for entry in entries %}
                                        <li class="activity-list-item">
                                            <div class="activity-time">
                                                <strong>شروع:</strong> {{ entry.start }}
                                            </div>
                                            <div class="activity-status">
                                                <strong>پایان:</strong> {{ entry.end }}
                                            </div>
                                        </li>
                                    {% endfor %}
                                </ul>
                            {% else %}
                                <div class="alert alert-info text-center mt-3">
                                    امروز فعالیتی ثبت نشده است.
                                </div>
                            {% endif %}
                        </div>
                    
                    {% else %}
                        <div class="row">
                            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
                                <div class="stat-box" onclick="window.location.href='?period=today'">
                                    <i class="fas fa-clock"></i>
                                    <h4>ساعت‌های کاری امروز</h4>
                                    <p><strong>{{ today_hours_display|default:"0:00" }} ساعت</strong></p>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="400">
                                <div class="stat-box" onclick="window.location.href='?period=week'">
                                    <i class="fas fa-calendar-week"></i>
                                    <h4>ساعت‌های کاری این هفته</h4>
                                    <p><strong>{{ week_hours_display|default:"0:00" }} ساعت</strong></p>
                                    <small>({{ start_of_week_jalali }} تا {{ end_of_week_jalali }})</small>
                                </div>
                            </div>
                            <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="600">
                                <div class="stat-box" onclick="window.location.href='?period=month'">
                                    <i class="fas fa-calendar-alt"></i>
                                    <h4>ساعت‌های کاری این ماه</h4>
                                    <p><strong>{{ month_hours_display|default:"0:00" }} ساعت</strong></p>
                                    <small>({{ start_of_month_jalali }} تا {{ end_of_month_jalali }})</small>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12 text-center" data-aos="fade-up" data-aos-delay="800">
                                <a href="{% url 'admin:employee_detailed_report' employee.id %}" class="btn btn-primary btn-lg" style="background: linear-gradient(135deg, #0062cc, #6610f2); border: none; border-radius: 30px; padding: 12px 35px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2); transition: all 0.3s ease;">
                                    <i class="fas fa-chart-line me-2"></i>
                                    مشاهده جزعیات گزارش ها
                                </a>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="footer">
                <p>سیستم گزارش‌دهی ساعات کاری</p>
            </div>
            
            {% if debug_info %}
            <!-- Debug information (hidden by default) -->
            <div class="debug-info" style="display: none; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                <h5>Debug Info:</h5>
                <pre>{{ debug_info }}</pre>
            </div>
            {% endif %}
        </div>
    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
<script>
    AOS.init();
</script>
{% block extra_scripts %}{% endblock %}
</body>
</html>
