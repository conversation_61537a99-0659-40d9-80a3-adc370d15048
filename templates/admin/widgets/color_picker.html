{% load i18n %}

<div class="color-picker-container">
    <input type="{{ widget.type }}" name="{{ widget.name }}" {% if widget.value != None %}value="{{ widget.value }}"{% endif %}
        {% include "django/forms/widgets/attrs.html" %} />
    
    {% if widget.value %}
    <div class="color-preview" style="display: inline-block; width: 24px; height: 24px; background-color: {{ widget.value }}; border-radius: 4px; margin-left: 10px; vertical-align: middle;"></div>
    {% endif %}
</div>

<style>
    .color-picker-container {
        display: flex;
        align-items: center;
    }
    
    .color-picker {
        width: 100px;
        height: 40px;
        padding: 0;
        border: none;
        cursor: pointer;
    }
    
    .color-picker::-webkit-color-swatch-wrapper {
        padding: 0;
    }
    
    .color-picker::-webkit-color-swatch {
        border: none;
        border-radius: 4px;
    }
</style>