{% load i18n admin_urls static admin_modify %}

{# Custom template for the topic field in TopicProject inline forms #}
<div class="field-box field-topic" 
     data-controller="topic-select unfold-topic-select"
     data-action="change->topic-select#handleChange">
    
    {{ field.errors }}
    
    <select name="{{ field.html_name }}" 
            id="{{ field.auto_id }}" 
            class="topic-select unfold-select"
            data-unfold-component="select"
            data-topic-select-target="select">
        {% for option in field.field.choices %}
            <option value="{{ option.0 }}" 
                    {% if option.0 == field.value|stringformat:"s" %}selected{% endif %}>
                {{ option.1 }}
            </option>
        {% endfor %}
    </select>
    
    {% if field.help_text %}
        <div class="help">{{ field.help_text|safe }}</div>
    {% endif %}
</div>

<script>
/**
 * This script prevents duplicate topic selection in TopicProject inline forms
 * It disables already selected topics in dropdown menus to prevent the
 * "Topic Project with this Topic and Project already exists" error
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to update disabled options - exposed globally for other scripts
    window.updateDisabledTopics = function() {
        // Get all topic select elements in the inline forms
        let topicSelects = document.querySelectorAll('.topic-select');
        
        // If no elements found, try with a more generic selector
        if (topicSelects.length === 0) {
            topicSelects = document.querySelectorAll('select[name$="-topic"]');
        }
        
        // Create a map to track selected topics and their select elements
        const selectedTopicsMap = new Map();
        
        // First pass: collect all selected topics
        topicSelects.forEach(select => {
            const selectedValue = select.value;
            if (selectedValue && selectedValue !== '') {
                if (!selectedTopicsMap.has(selectedValue)) {
                    selectedTopicsMap.set(selectedValue, []);
                }
                selectedTopicsMap.get(selectedValue).push(select.id);
            }
        });
        
        // Second pass: disable options that are already selected in other selects
        topicSelects.forEach(select => {
            const currentValue = select.value;
            
            // Reset all options to enabled first
            Array.from(select.options).forEach(option => {
                option.disabled = false;
                
                // Remove Unfold/Tailwind classes for disabled state
                option.classList.remove(
                    'text-gray-400', 'dark:text-gray-500', 
                    'bg-gray-100', 'dark:bg-gray-800', 
                    'opacity-50', 'cursor-not-allowed'
                );
                
                // Remove any "already selected" text
                if (option.textContent.includes(' (already selected)')) {
                    option.textContent = option.textContent.replace(' (already selected)', '');
                }
            });
            
            // Disable options that are selected elsewhere
            Array.from(select.options).forEach(option => {
                const optionValue = option.value;
                if (!optionValue || optionValue === '') return; // Skip empty option
                
                const selectsWithThisValue = selectedTopicsMap.get(optionValue) || [];
                const isSelectedElsewhere = selectsWithThisValue.length > 0 && 
                                           !selectsWithThisValue.includes(select.id);
                
                if (isSelectedElsewhere && optionValue !== currentValue) {
                    option.disabled = true;
                    
                    // Add Unfold/Tailwind classes for disabled state
                    option.classList.add(
                        'text-gray-400', 'dark:text-gray-500', 
                        'bg-gray-100', 'dark:bg-gray-800', 
                        'opacity-50', 'cursor-not-allowed'
                    );
                    
                    // Add a note about where it's selected (for better UX)
                    if (!option.textContent.includes(' (already selected)')) {
                        option.textContent = `${option.textContent} (already selected)`;
                    }
                }
            });
        });
    }
    
    // Run on page load
    window.updateDisabledTopics();
    
    // Add change event listeners to all select elements
    document.addEventListener('change', function(event) {
        if (event.target.tagName === 'SELECT' && 
            (event.target.classList.contains('topic-select') || event.target.name.includes('-topic'))) {
            window.updateDisabledTopics();
        }
    });
    
    // Handle the "Add another" button click
    const addButtons = document.querySelectorAll('.add-row a, .unfold-add-row, [data-action="add-row"]');
    addButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Use setTimeout to allow the DOM to update before we process
            setTimeout(window.updateDisabledTopics, 100);
        });
    });
    
    // Also handle the Django admin's "Add another" popup
    if (window.dismissAddAnotherPopup) {
        // Store the original function
        const originalDismissAddAnotherPopup = window.dismissAddAnotherPopup;
        
        // Override with our version that updates disabled topics
        window.dismissAddAnotherPopup = function(win, newId, newRepr) {
            // Call the original function
            originalDismissAddAnotherPopup(win, newId, newRepr);
            
            // Update our disabled topics
            setTimeout(window.updateDisabledTopics, 100);
        };
    }
    
    // For Django Unfold's dynamic form handling
    document.addEventListener('unfold:form-added', function() {
        setTimeout(window.updateDisabledTopics, 100);
    });
    
    // For Django Unfold's tab switching
    document.addEventListener('unfold:tab-changed', function() {
        setTimeout(window.updateDisabledTopics, 100);
    });
    
    // Enhance select elements with Unfold styling
    function enhanceSelectElements() {
        // Find all topic select elements
        const topicSelects = document.querySelectorAll('.topic-select, select[name$="-topic"]');
        
        topicSelects.forEach(select => {
            // Add Unfold-specific classes if they don't already exist
            if (!select.classList.contains('unfold-select')) {
                select.classList.add(
                    'unfold-select',
                    'block',
                    'w-full',
                    'rounded-md',
                    'shadow-sm',
                    'focus:ring-primary-500',
                    'focus:border-primary-500',
                    'sm:text-sm'
                );
                
                // Add data attributes for Unfold's JavaScript
                select.setAttribute('data-unfold-component', 'select');
            }
        });
    }
    
    // Run enhanceSelectElements on page load
    enhanceSelectElements();
    
    // Also enhance when new forms are added
    document.addEventListener('unfold:form-added', enhanceSelectElements);
    
    // And when tabs are changed
    document.addEventListener('unfold:tab-changed', enhanceSelectElements);
});

// Define the Stimulus controller for topic selection if Stimulus.js is available
if (window.Stimulus) {
    class TopicSelectController {
        static targets = ["select"];
        
        connect() {
            console.log("TopicSelectController connected");
            
            // Trigger the update of disabled topics when this controller connects
            if (window.updateDisabledTopics) {
                setTimeout(window.updateDisabledTopics, 50);
            }
        }
        
        // Handle change events from the select element
        handleChange(event) {
            console.log("Topic select changed:", event.target.value);
            
            // Trigger the update of disabled topics
            if (window.updateDisabledTopics) {
                window.updateDisabledTopics();
            }
        }
    }
    
    // Register the controller with Stimulus
    window.Stimulus.register('topic-select', TopicSelectController);
    
    // Register the Unfold-specific controller
    class UnfoldTopicSelectController {
        static targets = ["select"];
        
        connect() {
            console.log("UnfoldTopicSelectController connected");
            this.enhanceSelect();
        }
        
        enhanceSelect() {
            if (this.hasSelectTarget) {
                const select = this.selectTarget;
                
                // Add Unfold-specific classes
                select.classList.add(
                    'unfold-select',
                    'block',
                    'w-full',
                    'rounded-md',
                    'shadow-sm',
                    'focus:ring-primary-500',
                    'focus:border-primary-500',
                    'sm:text-sm'
                );
            }
        }
    }
    
    // Register the controller
    window.Stimulus.register('unfold-topic-select', UnfoldTopicSelectController);
}
</script>