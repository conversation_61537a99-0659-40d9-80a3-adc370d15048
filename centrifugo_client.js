/**
 * Centrifugo JavaScript Client
 * Connects to Centrifugo WebSocket and listens to user channel
 * 
 * Usage:
 * 1. First get a token: python manage.py test_centrifugo token --user-id 31
 * 2. Replace the token in this file
 * 3. Run: node centrifugo_client.js
 */

const WebSocket = require('ws');

class CentrifugoClient {
    constructor(userId, token, wsUrl = 'ws://localhost:8000/connection/websocket') {
        this.userId = userId;
        this.token = token;
        this.wsUrl = wsUrl;
        this.ws = null;
        this.channel = `teamby:user#${userId}`;
        this.messageId = 1;
    }

    connect() {
        console.log('🚀 Centrifugo JavaScript Client Starting...');
        console.log(`👤 User ID: ${this.userId}`);
        console.log(`🌐 WebSocket URL: ${this.wsUrl}`);
        console.log(`📡 Channel: ${this.channel}`);
        console.log('=' .repeat(60));

        this.ws = new WebSocket(this.wsUrl);

        this.ws.on('open', () => {
            console.log('🔌 WebSocket connection opened');
            this.authenticate();
        });

        this.ws.on('message', (data) => {
            try {
                const message = JSON.parse(data.toString());
                this.handleMessage(message);
            } catch (error) {
                console.log('⚠️  Invalid JSON received:', data.toString());
            }
        });

        this.ws.on('close', () => {
            console.log('🔌 WebSocket connection closed');
        });

        this.ws.on('error', (error) => {
            console.log('❌ WebSocket error:', error.message);
        });
    }

    authenticate() {
        const connectMessage = {
            id: this.messageId++,
            connect: {
                token: this.token,
                name: 'javascript-client'
            }
        };

        console.log('🔑 Authenticating...');
        this.ws.send(JSON.stringify(connectMessage));
    }

    subscribe() {
        const subscribeMessage = {
            id: this.messageId++,
            subscribe: {
                channel: this.channel
            }
        };

        console.log(`📡 Subscribing to channel: ${this.channel}`);
        this.ws.send(JSON.stringify(subscribeMessage));
    }

    handleMessage(data) {
        const currentTime = new Date().toLocaleTimeString();

        // Handle connection response
        if (data.connect) {
            if (data.connect.client) {
                console.log(`✅ Connected successfully! Client ID: ${data.connect.client}`);
                this.subscribe();
            } else {
                console.log('❌ Connection failed:', data);
            }
            return;
        }

        // Handle subscription response
        if (data.subscribe) {
            console.log(`✅ Subscribed to channel: ${this.channel}`);
            console.log('🎧 Listening for employee status updates...');
            console.log('=' .repeat(60));
            return;
        }

        // Handle push messages (actual notifications)
        if (data.push) {
            const pushData = data.push;
            const channel = pushData.channel;
            const pubData = pushData.pub?.data || {};

            if (channel === this.channel) {
                const messageType = pubData.type || 'unknown';

                if (messageType === 'employee_online') {
                    const employeeId = pubData.employee_id;
                    const employeeName = pubData.employee_name;
                    const timestamp = pubData.timestamp;
                    const time = new Date(timestamp).toLocaleString();

                    console.log(`🟢 [${currentTime}] EMPLOYEE ONLINE`);
                    console.log(`   👤 Name: ${employeeName}`);
                    console.log(`   🆔 ID: ${employeeId}`);
                    console.log(`   ⏰ Time: ${time}`);
                    console.log('-'.repeat(40));

                } else if (messageType === 'employee_offline') {
                    const employeeId = pubData.employee_id;
                    const employeeName = pubData.employee_name;
                    const timestamp = pubData.timestamp;
                    const time = new Date(timestamp).toLocaleString();

                    console.log(`🔴 [${currentTime}] EMPLOYEE OFFLINE`);
                    console.log(`   👤 Name: ${employeeName}`);
                    console.log(`   🆔 ID: ${employeeId}`);
                    console.log(`   ⏰ Time: ${time}`);
                    console.log('-'.repeat(40));

                } else {
                    console.log(`📨 [${currentTime}] OTHER MESSAGE`);
                    console.log(`   Type: ${messageType}`);
                    console.log(`   Data:`, JSON.stringify(pubData, null, 2));
                    console.log('-'.repeat(40));
                }
            }
            return;
        }

        // Handle other message types
        console.log(`📋 [${currentTime}] RAW MESSAGE:`, JSON.stringify(data, null, 2));
    }

    close() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// Configuration
const USER_ID = 31; // Change this to your user ID
const TOKEN = 'YOUR_JWT_TOKEN_HERE'; // Get this from: python manage.py test_centrifugo token --user-id 31
const WS_URL = 'ws://localhost:8000/connection/websocket';

// Validate configuration
if (TOKEN === 'YOUR_JWT_TOKEN_HERE') {
    console.log('❌ Please set your JWT token first!');
    console.log('1. Run: python manage.py test_centrifugo token --user-id ' + USER_ID);
    console.log('2. Copy the token and replace YOUR_JWT_TOKEN_HERE in this file');
    console.log('3. Run: node centrifugo_client.js');
    process.exit(1);
}

// Create and start client
const client = new CentrifugoClient(USER_ID, TOKEN, WS_URL);

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n⏹️  Interrupted by user');
    client.close();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n⏹️  Terminated');
    client.close();
    process.exit(0);
});

// Start the client
client.connect();
